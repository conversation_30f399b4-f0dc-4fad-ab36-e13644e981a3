module code.byted.org/motor/fwe_category

go 1.18

replace github.com/apache/thrift => github.com/apache/thrift v0.13.0

require (
	code.byted.org/gopkg/env v1.6.18
	code.byted.org/gopkg/lang v0.21.8
	code.byted.org/gopkg/lang/v2 v2.0.9
	code.byted.org/gopkg/logs v1.2.25
	code.byted.org/gopkg/logs/v2 v2.1.57
	code.byted.org/gopkg/pkg v0.1.0
	code.byted.org/gopkg/tccclient v1.6.7
	code.byted.org/gorm/bytedgen v0.3.21
	code.byted.org/gorm/bytedgorm v0.8.3
	code.byted.org/kite/kitex v1.18.1
	code.byted.org/kitex/apache_monitor v0.1.1
	code.byted.org/lang/gg v0.20.2
	code.byted.org/motor/fwe_ecom_lib/ecom_err v0.0.2
	code.byted.org/motor/fwe_ecom_product_common v0.0.72
	code.byted.org/motor/gopkg v1.3.50
	code.byted.org/overpass/common v0.0.0-20240906045009-906d4d6c4845
	code.byted.org/overpass/motor_car_agg_sort v0.0.0-20221017192644-f8207769c150
	code.byted.org/overpass/motor_car_base v0.0.0-20241111043423-64ab5776ecc3
	code.byted.org/webcast/libs_anycache v1.7.0
	github.com/bytedance/sonic v1.12.7
	github.com/cloudwego/gopkg v0.1.3
	github.com/cloudwego/kitex v0.12.1
	github.com/cloudwego/kitex/pkg/protocol/bthrift v0.0.0-20241205072100-85e3c72c57da
	github.com/go-sql-driver/mysql v1.7.0
	github.com/google/uuid v1.6.0
	github.com/pkg/errors v0.9.1
	github.com/tidwall/gjson v1.18.0
	golang.org/x/exp v0.0.0-20220827204233-334a2380cb91
	gorm.io/gen v0.3.21
	gorm.io/gorm v1.24.2
	gorm.io/plugin/dbresolver v1.4.0
)

require (
	code.byted.org/aiops/apm_vendor_byted v0.0.27 // indirect
	code.byted.org/aiops/metrics_codec v0.0.24 // indirect
	code.byted.org/aiops/monitoring-common-go v0.0.5 // indirect
	code.byted.org/bcc/bcc-go-client v0.1.48 // indirect
	code.byted.org/bcc/bcc-go-client/internal/sidecar/idl v0.0.4 // indirect
	code.byted.org/bcc/conf_engine v0.0.0-20230510030051-32fb55f74cf1 // indirect
	code.byted.org/bcc/pull_json_model v1.0.21 // indirect
	code.byted.org/bcc/tools v0.0.20 // indirect
	code.byted.org/bytedtrace/bytedtrace-client-go v1.2.3-pre // indirect
	code.byted.org/bytedtrace/bytedtrace-common/go v0.0.13 // indirect
	code.byted.org/bytedtrace/bytedtrace-compatible-lightweight-go v1.0.1 // indirect
	code.byted.org/bytedtrace/bytedtrace-conf-provider-client-go v0.0.26 // indirect
	code.byted.org/bytedtrace/bytedtrace-gls-switch v1.3.0 // indirect
	code.byted.org/bytedtrace/interface-go v1.0.20 // indirect
	code.byted.org/bytedtrace/serializer-go v1.0.0 // indirect
	code.byted.org/bytefaas/faas-go v1.6.22 // indirect
	code.byted.org/golf/consul v2.1.13+incompatible // indirect
	code.byted.org/golf/ssconf v0.0.1 // indirect
	code.byted.org/gopkg/apm_vendor_interface v0.0.3 // indirect
	code.byted.org/gopkg/asyncache v0.0.0-20210129072708-1df5611dba17 // indirect
	code.byted.org/gopkg/asynccache v0.0.0-20210422090342-26f94f7676b8 // indirect
	code.byted.org/gopkg/bytedmysql v1.1.15 // indirect
	code.byted.org/gopkg/consul v1.2.6 // indirect
	code.byted.org/gopkg/context v0.0.1 // indirect
	code.byted.org/gopkg/ctxvalues v0.7.0 // indirect
	code.byted.org/gopkg/debug v0.10.1 // indirect
	code.byted.org/gopkg/etcd_util v2.3.3+incompatible // indirect
	code.byted.org/gopkg/etcdproxy v0.1.1 // indirect
	code.byted.org/gopkg/idgenerator v1.0.15 // indirect
	code.byted.org/gopkg/localcache v0.10.2 // indirect
	code.byted.org/gopkg/localcache/base v0.9.0 // indirect
	code.byted.org/gopkg/localcache/contributes/freecache v0.7.4 // indirect
	code.byted.org/gopkg/localcache/contributes/gcache v0.8.1 // indirect
	code.byted.org/gopkg/localcache/contributes/vfastcache v0.2.0 // indirect
	code.byted.org/gopkg/logid v0.0.0-20241008043456-230d03adb830 // indirect
	code.byted.org/gopkg/metainfo v0.1.4 // indirect
	code.byted.org/gopkg/metrics v1.4.25 // indirect
	code.byted.org/gopkg/metrics/v3 v3.1.35 // indirect
	code.byted.org/gopkg/metrics/v4 v4.1.4 // indirect
	code.byted.org/gopkg/metrics_core v0.0.39 // indirect
	code.byted.org/gopkg/net2 v1.5.0 // indirect
	code.byted.org/gopkg/retry v0.0.0-20220517012520-bde92e63db0a // indirect
	code.byted.org/gopkg/stats v1.2.12 // indirect
	code.byted.org/gopkg/tccclient/v3 v3.0.0 // indirect
	code.byted.org/gopkg/thrift v1.14.1 // indirect
	code.byted.org/hystrix/hystrix-go v0.0.0-20190214095017-a2a890c81cd5 // indirect
	code.byted.org/iespkg/bytedkits-go/goext v0.4.0 // indirect
	code.byted.org/iespkg/retry-go v0.1.2 // indirect
	code.byted.org/inf/infsecc v1.0.3 // indirect
	code.byted.org/kite/endpoint v3.7.5+incompatible // indirect
	code.byted.org/kite/kitc v3.10.26+incompatible // indirect
	code.byted.org/kite/kitex-overpass-suite v0.0.29 // indirect
	code.byted.org/kite/kitex/pkg/protocol/bthrift v0.0.0-20250103083202-413e97b2b0fd // indirect
	code.byted.org/kite/kitutil v3.8.8+incompatible // indirect
	code.byted.org/kite/rpal v0.1.22 // indirect
	code.byted.org/kv/backoff v0.0.0-20191031070508-5d868504e646 // indirect
	code.byted.org/kv/circuitbreaker v0.0.0-20200212034351-d3f51a5b9165 // indirect
	code.byted.org/kv/goredis v5.6.2+incompatible // indirect
	code.byted.org/kv/redis-v6 v1.1.5 // indirect
	code.byted.org/lang/tangofeatures v0.1.3-0.20240229114634-677acc3517ac // indirect
	code.byted.org/lang/trace v0.0.3 // indirect
	code.byted.org/lidar/profiler v0.4.4 // indirect
	code.byted.org/lidar/profiler/kitex v0.4.6 // indirect
	code.byted.org/log_market/gosdk v0.0.0-20230524072203-e069d8367314 // indirect
	code.byted.org/log_market/loghelper v0.1.11 // indirect
	code.byted.org/log_market/tracelog v0.1.5 // indirect
	code.byted.org/log_market/ttlogagent_gosdk v0.0.6 // indirect
	code.byted.org/log_market/ttlogagent_gosdk/v4 v4.0.51 // indirect
	code.byted.org/middleware/fic_client v0.2.8 // indirect
	code.byted.org/middleware/gocaller v0.0.6 // indirect
	code.byted.org/middleware/hertz v1.13.5 // indirect
	code.byted.org/motor/fwe_ecom_lib/tools v0.0.1 // indirect
	code.byted.org/overpass/motor_service_rpc_idl_common v0.0.0-20241223062748-f877292a6eaa // indirect
	code.byted.org/security/go-spiffe-v2 v1.0.8 // indirect
	code.byted.org/security/memfd v0.0.1 // indirect
	code.byted.org/security/sensitive_finder_engine v0.3.18 // indirect
	code.byted.org/security/zti-jwt-helper-golang v1.0.16 // indirect
	code.byted.org/service_mesh/shmipc v0.2.16 // indirect
	code.byted.org/temai/go-json v0.9.11 // indirect
	code.byted.org/trace/trace-client-go v1.3.7 // indirect
	code.byted.org/ttarch/byteconf-cel-go v0.0.3 // indirect
	code.byted.org/videoarch/vfastcache v1.0.10 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/base v0.2.1-0.20250623130229-7f8c181372d2 // indirect
	code.byted.org/webcast/libs_anycache/plugin/cache/objectcache v0.0.1 // indirect
	code.byted.org/webcast/libs_anycache/plugin/codec/base v0.1.0 // indirect
	code.byted.org/webcast/libs_sync v0.1.2 // indirect
	code.byted.org/webcast/logw v0.1.4 // indirect
	code.byted.org/xiaoganbo/stm v1.7.5 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/antonmedv/expr v1.13.0 // indirect
	github.com/apache/thrift v0.17.0 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bits-and-blooms/bitset v1.13.0 // indirect
	github.com/bits-and-blooms/bloom/v3 v3.6.0 // indirect
	github.com/blang/semver/v4 v4.0.0 // indirect
	github.com/bluele/gcache v0.0.2 // indirect
	github.com/bufbuild/protocompile v0.10.0 // indirect
	github.com/bytedance/gopkg v0.1.1 // indirect
	github.com/bytedance/sonic/loader v0.2.2 // indirect
	github.com/caarlos0/env/v6 v6.10.1 // indirect
	github.com/cespare/xxhash v1.1.0 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/cloudevents/sdk-go v1.1.2 // indirect
	github.com/cloudevents/sdk-go/v2 v2.6.0 // indirect
	github.com/cloudwego/base64x v0.1.4 // indirect
	github.com/cloudwego/configmanager v0.2.2 // indirect
	github.com/cloudwego/dynamicgo v0.4.7-0.20241220085612-55704ea4ca8f // indirect
	github.com/cloudwego/fastpb v0.0.5 // indirect
	github.com/cloudwego/frugal v0.2.3 // indirect
	github.com/cloudwego/hertz v0.9.4 // indirect
	github.com/cloudwego/iasm v0.2.0 // indirect
	github.com/cloudwego/localsession v0.1.1 // indirect
	github.com/cloudwego/netpoll v0.6.5 // indirect
	github.com/cloudwego/runtimex v0.1.0 // indirect
	github.com/cloudwego/thriftgo v0.3.18 // indirect
	github.com/coocood/freecache v1.2.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/structtag v1.2.0 // indirect
	github.com/fsnotify/fsnotify v1.6.0 // indirect
	github.com/go-jose/go-jose/v3 v3.0.3 // indirect
	github.com/go-kit/log v0.2.1 // indirect
	github.com/go-logfmt/logfmt v0.6.0 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/google/pprof v0.0.0-20240727154555-813a5fbdbec8 // indirect
	github.com/gorilla/mux v1.8.0 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/hbollon/go-edlib v1.6.0 // indirect
	github.com/iancoleman/strcase v0.2.0 // indirect
	github.com/jhump/protoreflect v1.16.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/klauspost/cpuid/v2 v2.2.7 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/gls v0.0.0-20220109145502-612d0167dce5 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/nyaruka/phonenumbers v1.0.56 // indirect
	github.com/opentracing/opentracing-go v1.2.1-0.20210205174328-3088eee7e4d2 // indirect
	github.com/philhofer/fwd v1.1.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20221212215047-62379fc7944b // indirect
	github.com/shirou/gopsutil/v3 v3.23.7 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/stretchr/testify v1.9.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tinylib/msgp v1.1.6 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/vmihailenco/msgpack/v4 v4.3.12 // indirect
	github.com/vmihailenco/msgpack/v5 v5.3.5 // indirect
	github.com/vmihailenco/tagparser v0.1.2 // indirect
	github.com/vmihailenco/tagparser/v2 v2.0.0 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	github.com/zeebo/errs v1.3.0 // indirect
	go.opencensus.io v0.24.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.8.0 // indirect
	go.uber.org/zap v1.21.0 // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20230525183740-e7c30c78aeb2 // indirect
	golang.org/x/arch v0.11.0 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sync v0.8.0 // indirect
	golang.org/x/sys v0.26.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240318140521-94a12d6c2237 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240415180920-8c6c420018be // indirect
	google.golang.org/grpc v1.63.2 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/datatypes v1.0.7 // indirect
	gorm.io/driver/mysql v1.4.4 // indirect
	gorm.io/hints v1.1.1 // indirect
)
