// Code generated by Kitex v1.20.3. DO NOT EDIT.
package fweecomcategoryservice

import (
	byted "code.byted.org/kite/kitex/byted"
	server "code.byted.org/kite/kitex/server"
	category "code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

// NewServer creates a server.Server with the given handler and options.
func NewServer(handler category.FweEcomCategoryService, opts ...server.Option) server.Server {
	var options []server.Option

	options = append(options, byted.ServerSuite(serviceInfo()))

	options = append(options, opts...)
	options = append(options, server.WithCompatibleMiddlewareForUnary())

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

// NewServerWithBytedConfig creates a server.Server with the given handler and options.
func NewServerWithBytedConfig(handler category.FweEcomCategoryService, config *byted.ServerConfig, opts ...server.Option) server.Server {
	var options []server.Option
	options = append(options, byted.ServerSuiteWithConfig(serviceInfo(), config))
	options = append(options, server.WithCompatibleMiddlewareForUnary())
	options = append(options, opts...)

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

func RegisterService(svr server.Server, handler category.FweEcomCategoryService, opts ...server.RegisterOption) error {
	return svr.RegisterService(serviceInfo(), handler, opts...)
}
