// Code generated by Kitex v1.20.3. DO NOT EDIT.

package fweecomcategoryservice

import (
	byted "code.byted.org/kite/kitex/byted"
	client "code.byted.org/kite/kitex/client"
	callopt "code.byted.org/kite/kitex/client/callopt"
	category "code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"context"
)

// Client is designed to provide IDL-compatible methods with call-option parameter for kitex framework.
type Client interface {
	MGetCategoryItem(ctx context.Context, req *category.MGetCategoryItemReq, callOptions ...callopt.Option) (r *category.MGetCategoryItemResp, err error)
	GetBizLineCategories(ctx context.Context, req *category.GetBizLineCategoriesReq, callOptions ...callopt.Option) (r *category.GetBizLineCategoriesResp, err error)
	CreateCategoryNode(ctx context.Context, req *category.CreateCategoryNodeReq, callOptions ...callopt.Option) (r *category.CreateCategoryNodeResp, err error)
	UpdateCategoryNode(ctx context.Context, req *category.UpdateCategoryNodeReq, callOptions ...callopt.Option) (r *category.UpdateCategoryNodeResp, err error)
	QueryPropertyValue(ctx context.Context, req *category.QueryPropertyValueReq, callOptions ...callopt.Option) (r *category.QueryPropertyValueResp, err error)
	QueryPropertyValueList(ctx context.Context, req *category.QueryPropertyValueListReq, callOptions ...callopt.Option) (r *category.QueryPropertyValueListResp, err error)
	MGetPropertyItem(ctx context.Context, req *category.MGetPropertyItemReq, callOptions ...callopt.Option) (r *category.MGetPropertyItemResp, err error)
	UpsertPropertyValue(ctx context.Context, req *category.UpsertPropertyValueReq, callOptions ...callopt.Option) (r *category.UpsertPropertyValueResp, err error)
	OperatePropertyValueStatus(ctx context.Context, req *category.OperatePropertyValueStatusReq, callOptions ...callopt.Option) (r *category.OperatePropertyValueStatusResp, err error)
	MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq, callOptions ...callopt.Option) (r *category.MGetAllLeafCategoryResp, err error)
	GetPropertyMeta(ctx context.Context, req *category.GetPropertyMetaReq, callOptions ...callopt.Option) (r *category.GetPropertyMetaResp, err error)
	CreateTagMeta(ctx context.Context, req *category.CreateTagMetaReq, callOptions ...callopt.Option) (r *category.CreateTagMetaResp, err error)
	UpdateTagMeta(ctx context.Context, req *category.UpdateTagMetaReq, callOptions ...callopt.Option) (r *category.UpdateTagMetaResp, err error)
	BatchDelTagMeta(ctx context.Context, req *category.BatchDelTagMetaReq, callOptions ...callopt.Option) (r *category.BatchDelTagMetaResp, err error)
	MGetTagByCode(ctx context.Context, req *category.MGetTagByCodeReq, callOptions ...callopt.Option) (r *category.MGetTagByCodeResp, err error)
	MGetTagByID(ctx context.Context, req *category.MGetTagByIDReq, callOptions ...callopt.Option) (r *category.MGetTagByIDResp, err error)
	CreateProperty(ctx context.Context, req *category.CreatePropertyReq, callOptions ...callopt.Option) (r *category.CreatePropertyRsp, err error)
	UpdateProperty(ctx context.Context, req *category.UpdatePropertyReq, callOptions ...callopt.Option) (r *category.UpdatePropertyRsp, err error)
	UpsertProperty(ctx context.Context, req *category.UpsertPropertyReq, callOptions ...callopt.Option) (r *category.UpsertPropertyRsp, err error)
	MDelProperty(ctx context.Context, req *category.MDelPropertyReq, callOptions ...callopt.Option) (r *category.MDelPropertyRsp, err error)
	MGetProperty(ctx context.Context, req *category.MGetPropertyReq, callOptions ...callopt.Option) (r *category.MGetPropertyRsp, err error)
	QueryProperty(ctx context.Context, req *category.QueryPropertyReq, callOptions ...callopt.Option) (r *category.QueryPropertyRsp, err error)
	ExportProperty(ctx context.Context, req *category.ExportPropertyReq, callOptions ...callopt.Option) (r *category.ExportPropertyRsp, err error)
	QueryCategory(ctx context.Context, req *category.QueryCategoryReq, callOptions ...callopt.Option) (r *category.QueryCategoryRsp, err error)
}

// NewClient creates a client for the service defined in IDL.
func NewClient(destService string, opts ...client.Option) (Client, error) {
	var options []client.Option
	options = append(options, client.WithDestService(destService))

	config := byted.NewClientConfig()
	config.DestService = destService
	options = append(options, byted.ClientSuiteWithConfig(serviceInfo(), config))

	options = append(options, opts...)

	kc, err := client.NewClient(serviceInfoForClient(), options...)
	if err != nil {
		return nil, err
	}
	return &kFweEcomCategoryServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClient creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClient(destService string, opts ...client.Option) Client {
	kc, err := NewClient(destService, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}

type kFweEcomCategoryServiceClient struct {
	*kClient
}

func (p *kFweEcomCategoryServiceClient) MGetCategoryItem(ctx context.Context, req *category.MGetCategoryItemReq, callOptions ...callopt.Option) (r *category.MGetCategoryItemResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetCategoryItem(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) GetBizLineCategories(ctx context.Context, req *category.GetBizLineCategoriesReq, callOptions ...callopt.Option) (r *category.GetBizLineCategoriesResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetBizLineCategories(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) CreateCategoryNode(ctx context.Context, req *category.CreateCategoryNodeReq, callOptions ...callopt.Option) (r *category.CreateCategoryNodeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateCategoryNode(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) UpdateCategoryNode(ctx context.Context, req *category.UpdateCategoryNodeReq, callOptions ...callopt.Option) (r *category.UpdateCategoryNodeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateCategoryNode(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) QueryPropertyValue(ctx context.Context, req *category.QueryPropertyValueReq, callOptions ...callopt.Option) (r *category.QueryPropertyValueResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryPropertyValue(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) QueryPropertyValueList(ctx context.Context, req *category.QueryPropertyValueListReq, callOptions ...callopt.Option) (r *category.QueryPropertyValueListResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryPropertyValueList(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) MGetPropertyItem(ctx context.Context, req *category.MGetPropertyItemReq, callOptions ...callopt.Option) (r *category.MGetPropertyItemResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetPropertyItem(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) UpsertPropertyValue(ctx context.Context, req *category.UpsertPropertyValueReq, callOptions ...callopt.Option) (r *category.UpsertPropertyValueResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpsertPropertyValue(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) OperatePropertyValueStatus(ctx context.Context, req *category.OperatePropertyValueStatusReq, callOptions ...callopt.Option) (r *category.OperatePropertyValueStatusResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.OperatePropertyValueStatus(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq, callOptions ...callopt.Option) (r *category.MGetAllLeafCategoryResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetAllLeafCategory(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) GetPropertyMeta(ctx context.Context, req *category.GetPropertyMetaReq, callOptions ...callopt.Option) (r *category.GetPropertyMetaResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.GetPropertyMeta(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) CreateTagMeta(ctx context.Context, req *category.CreateTagMetaReq, callOptions ...callopt.Option) (r *category.CreateTagMetaResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateTagMeta(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) UpdateTagMeta(ctx context.Context, req *category.UpdateTagMetaReq, callOptions ...callopt.Option) (r *category.UpdateTagMetaResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateTagMeta(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) BatchDelTagMeta(ctx context.Context, req *category.BatchDelTagMetaReq, callOptions ...callopt.Option) (r *category.BatchDelTagMetaResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.BatchDelTagMeta(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) MGetTagByCode(ctx context.Context, req *category.MGetTagByCodeReq, callOptions ...callopt.Option) (r *category.MGetTagByCodeResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetTagByCode(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) MGetTagByID(ctx context.Context, req *category.MGetTagByIDReq, callOptions ...callopt.Option) (r *category.MGetTagByIDResp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetTagByID(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) CreateProperty(ctx context.Context, req *category.CreatePropertyReq, callOptions ...callopt.Option) (r *category.CreatePropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.CreateProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) UpdateProperty(ctx context.Context, req *category.UpdatePropertyReq, callOptions ...callopt.Option) (r *category.UpdatePropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpdateProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) UpsertProperty(ctx context.Context, req *category.UpsertPropertyReq, callOptions ...callopt.Option) (r *category.UpsertPropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.UpsertProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) MDelProperty(ctx context.Context, req *category.MDelPropertyReq, callOptions ...callopt.Option) (r *category.MDelPropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MDelProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) MGetProperty(ctx context.Context, req *category.MGetPropertyReq, callOptions ...callopt.Option) (r *category.MGetPropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.MGetProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) QueryProperty(ctx context.Context, req *category.QueryPropertyReq, callOptions ...callopt.Option) (r *category.QueryPropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) ExportProperty(ctx context.Context, req *category.ExportPropertyReq, callOptions ...callopt.Option) (r *category.ExportPropertyRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.ExportProperty(ctx, req)
}

func (p *kFweEcomCategoryServiceClient) QueryCategory(ctx context.Context, req *category.QueryCategoryReq, callOptions ...callopt.Option) (r *category.QueryCategoryRsp, err error) {
	ctx = client.NewCtxWithCallOptions(ctx, callOptions)
	return p.kClient.QueryCategory(ctx, req)
}

// NewClientWithBytedConfig creates a client for the service defined in IDL.
func NewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) (Client, error) {
	if config == nil {
		config = byted.NewClientConfig()
	}
	config.DestService = destService

	var options []client.Option
	options = append(options, client.WithDestService(destService))

	clientServiceInfo := serviceInfoForClient()
	options = append(options, byted.ClientSuiteWithConfig(clientServiceInfo, config))
	options = append(options, opts...)
	kc, err := client.NewClient(clientServiceInfo, options...)
	if err != nil {
		return nil, err
	}
	return &kFweEcomCategoryServiceClient{
		kClient: newServiceClient(kc),
	}, nil
}

// MustNewClientWithBytedConfig creates a client for the service defined in IDL. It panics if any error occurs.
func MustNewClientWithBytedConfig(destService string, config *byted.ClientConfig, opts ...client.Option) Client {
	kc, err := NewClientWithBytedConfig(destService, config, opts...)
	if err != nil {
		panic(err)
	}
	return kc
}
