// Code generated by Kitex v1.15.3. DO NOT EDIT.

package fweecomcategoryservice

import (
	byted "code.byted.org/kite/kitex/byted"
	server "code.byted.org/kite/kitex/server"
	category "code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

// NewInvoker creates a server.Invoker with the given handler and options.
func NewInvoker(handler category.FweEcomCategoryService, opts ...server.Option) server.Invoker {
	var options []server.Option

	options = append(options, byted.InvokeSuite(serviceInfo()))

	options = append(options, opts...)

	s := server.NewInvoker(options...)
	if err := s.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	if err := s.Init(); err != nil {
		panic(err)
	}
	return s
}

// NewInvokerWithBytedConfig creates a server.Invoker with the given handler and options.
func NewInvokerWithBytedConfig(handler category.FweEcomCategoryService, config *byted.ServerConfig, opts ...server.Option) server.Invoker {
	var options []server.Option
	options = append(options, byted.InvokeSuiteWithConfig(serviceInfo(), config))
	options = append(options, opts...)

	s := server.NewInvoker(options...)
	if err := s.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	if err := s.Init(); err != nil {
		panic(err)
	}
	return s
}
