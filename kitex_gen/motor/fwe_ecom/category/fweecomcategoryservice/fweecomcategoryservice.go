// Code generated by Kitex v1.20.3. DO NOT EDIT.

package fweecomcategoryservice

import (
	client "code.byted.org/kite/kitex/client"
	category "code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"context"
	"errors"
	kitex "github.com/cloudwego/kitex/pkg/serviceinfo"
)

var errInvalidMessageType = errors.New("invalid message type for service method handler")

var serviceMethods = map[string]kitex.MethodInfo{
	"MGetCategoryItem": kitex.NewMethodInfo(
		mGetCategoryItemHandler,
		newFweEcomCategoryServiceMGetCategoryItemArgs,
		newFweEcomCategoryServiceMGetCategoryItemResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetBizLineCategories": kitex.NewMethodInfo(
		getBizLineCategoriesHandler,
		newFweEcomCategoryServiceGetBizLineCategoriesArgs,
		newFweEcomCategoryServiceGetBizLineCategoriesResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateCategoryNode": kitex.NewMethodInfo(
		createCategoryNodeHandler,
		newFweEcomCategoryServiceCreateCategoryNodeArgs,
		newFweEcomCategoryServiceCreateCategoryNodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateCategoryNode": kitex.NewMethodInfo(
		updateCategoryNodeHandler,
		newFweEcomCategoryServiceUpdateCategoryNodeArgs,
		newFweEcomCategoryServiceUpdateCategoryNodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryPropertyValue": kitex.NewMethodInfo(
		queryPropertyValueHandler,
		newFweEcomCategoryServiceQueryPropertyValueArgs,
		newFweEcomCategoryServiceQueryPropertyValueResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryPropertyValueList": kitex.NewMethodInfo(
		queryPropertyValueListHandler,
		newFweEcomCategoryServiceQueryPropertyValueListArgs,
		newFweEcomCategoryServiceQueryPropertyValueListResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetPropertyItem": kitex.NewMethodInfo(
		mGetPropertyItemHandler,
		newFweEcomCategoryServiceMGetPropertyItemArgs,
		newFweEcomCategoryServiceMGetPropertyItemResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpsertPropertyValue": kitex.NewMethodInfo(
		upsertPropertyValueHandler,
		newFweEcomCategoryServiceUpsertPropertyValueArgs,
		newFweEcomCategoryServiceUpsertPropertyValueResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"OperatePropertyValueStatus": kitex.NewMethodInfo(
		operatePropertyValueStatusHandler,
		newFweEcomCategoryServiceOperatePropertyValueStatusArgs,
		newFweEcomCategoryServiceOperatePropertyValueStatusResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetAllLeafCategory": kitex.NewMethodInfo(
		mGetAllLeafCategoryHandler,
		newFweEcomCategoryServiceMGetAllLeafCategoryArgs,
		newFweEcomCategoryServiceMGetAllLeafCategoryResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"GetPropertyMeta": kitex.NewMethodInfo(
		getPropertyMetaHandler,
		newFweEcomCategoryServiceGetPropertyMetaArgs,
		newFweEcomCategoryServiceGetPropertyMetaResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateTagMeta": kitex.NewMethodInfo(
		createTagMetaHandler,
		newFweEcomCategoryServiceCreateTagMetaArgs,
		newFweEcomCategoryServiceCreateTagMetaResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateTagMeta": kitex.NewMethodInfo(
		updateTagMetaHandler,
		newFweEcomCategoryServiceUpdateTagMetaArgs,
		newFweEcomCategoryServiceUpdateTagMetaResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"BatchDelTagMeta": kitex.NewMethodInfo(
		batchDelTagMetaHandler,
		newFweEcomCategoryServiceBatchDelTagMetaArgs,
		newFweEcomCategoryServiceBatchDelTagMetaResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetTagByCode": kitex.NewMethodInfo(
		mGetTagByCodeHandler,
		newFweEcomCategoryServiceMGetTagByCodeArgs,
		newFweEcomCategoryServiceMGetTagByCodeResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetTagByID": kitex.NewMethodInfo(
		mGetTagByIDHandler,
		newFweEcomCategoryServiceMGetTagByIDArgs,
		newFweEcomCategoryServiceMGetTagByIDResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"CreateProperty": kitex.NewMethodInfo(
		createPropertyHandler,
		newFweEcomCategoryServiceCreatePropertyArgs,
		newFweEcomCategoryServiceCreatePropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpdateProperty": kitex.NewMethodInfo(
		updatePropertyHandler,
		newFweEcomCategoryServiceUpdatePropertyArgs,
		newFweEcomCategoryServiceUpdatePropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"UpsertProperty": kitex.NewMethodInfo(
		upsertPropertyHandler,
		newFweEcomCategoryServiceUpsertPropertyArgs,
		newFweEcomCategoryServiceUpsertPropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MDelProperty": kitex.NewMethodInfo(
		mDelPropertyHandler,
		newFweEcomCategoryServiceMDelPropertyArgs,
		newFweEcomCategoryServiceMDelPropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"MGetProperty": kitex.NewMethodInfo(
		mGetPropertyHandler,
		newFweEcomCategoryServiceMGetPropertyArgs,
		newFweEcomCategoryServiceMGetPropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryProperty": kitex.NewMethodInfo(
		queryPropertyHandler,
		newFweEcomCategoryServiceQueryPropertyArgs,
		newFweEcomCategoryServiceQueryPropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"ExportProperty": kitex.NewMethodInfo(
		exportPropertyHandler,
		newFweEcomCategoryServiceExportPropertyArgs,
		newFweEcomCategoryServiceExportPropertyResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
	"QueryCategory": kitex.NewMethodInfo(
		queryCategoryHandler,
		newFweEcomCategoryServiceQueryCategoryArgs,
		newFweEcomCategoryServiceQueryCategoryResult,
		false,
		kitex.WithStreamingMode(kitex.StreamingNone),
	),
}

var (
	fweEcomCategoryServiceServiceInfo                = NewServiceInfo()
	fweEcomCategoryServiceServiceInfoForClient       = NewServiceInfoForClient()
	fweEcomCategoryServiceServiceInfoForStreamClient = NewServiceInfoForStreamClient()
)

// for server
func serviceInfo() *kitex.ServiceInfo {
	return fweEcomCategoryServiceServiceInfo
}

// for stream client
func serviceInfoForStreamClient() *kitex.ServiceInfo {
	return fweEcomCategoryServiceServiceInfoForStreamClient
}

// for client
func serviceInfoForClient() *kitex.ServiceInfo {
	return fweEcomCategoryServiceServiceInfoForClient
}

// NewServiceInfo creates a new ServiceInfo containing all methods
func NewServiceInfo() *kitex.ServiceInfo {
	return newServiceInfo(false, true, true)
}

// NewServiceInfo creates a new ServiceInfo containing non-streaming methods
func NewServiceInfoForClient() *kitex.ServiceInfo {
	return newServiceInfo(false, false, true)
}
func NewServiceInfoForStreamClient() *kitex.ServiceInfo {
	return newServiceInfo(true, true, false)
}

func newServiceInfo(hasStreaming bool, keepStreamingMethods bool, keepNonStreamingMethods bool) *kitex.ServiceInfo {
	serviceName := "FweEcomCategoryService"
	handlerType := (*category.FweEcomCategoryService)(nil)
	methods := map[string]kitex.MethodInfo{}
	for name, m := range serviceMethods {
		if m.IsStreaming() && !keepStreamingMethods {
			continue
		}
		if !m.IsStreaming() && !keepNonStreamingMethods {
			continue
		}
		methods[name] = m
	}
	extra := map[string]interface{}{
		"PackageName": "category",
	}
	if hasStreaming {
		extra["streaming"] = hasStreaming
	}
	svcInfo := &kitex.ServiceInfo{
		ServiceName:     serviceName,
		HandlerType:     handlerType,
		Methods:         methods,
		PayloadCodec:    kitex.Thrift,
		KiteXGenVersion: "v1.20.3",
		Extra:           extra,
	}
	return svcInfo
}

func mGetCategoryItemHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMGetCategoryItemArgs)
	realResult := result.(*category.FweEcomCategoryServiceMGetCategoryItemResult)
	success, err := handler.(category.FweEcomCategoryService).MGetCategoryItem(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMGetCategoryItemArgs() interface{} {
	return category.NewFweEcomCategoryServiceMGetCategoryItemArgs()
}

func newFweEcomCategoryServiceMGetCategoryItemResult() interface{} {
	return category.NewFweEcomCategoryServiceMGetCategoryItemResult()
}

func getBizLineCategoriesHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceGetBizLineCategoriesArgs)
	realResult := result.(*category.FweEcomCategoryServiceGetBizLineCategoriesResult)
	success, err := handler.(category.FweEcomCategoryService).GetBizLineCategories(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceGetBizLineCategoriesArgs() interface{} {
	return category.NewFweEcomCategoryServiceGetBizLineCategoriesArgs()
}

func newFweEcomCategoryServiceGetBizLineCategoriesResult() interface{} {
	return category.NewFweEcomCategoryServiceGetBizLineCategoriesResult()
}

func createCategoryNodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceCreateCategoryNodeArgs)
	realResult := result.(*category.FweEcomCategoryServiceCreateCategoryNodeResult)
	success, err := handler.(category.FweEcomCategoryService).CreateCategoryNode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceCreateCategoryNodeArgs() interface{} {
	return category.NewFweEcomCategoryServiceCreateCategoryNodeArgs()
}

func newFweEcomCategoryServiceCreateCategoryNodeResult() interface{} {
	return category.NewFweEcomCategoryServiceCreateCategoryNodeResult()
}

func updateCategoryNodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceUpdateCategoryNodeArgs)
	realResult := result.(*category.FweEcomCategoryServiceUpdateCategoryNodeResult)
	success, err := handler.(category.FweEcomCategoryService).UpdateCategoryNode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceUpdateCategoryNodeArgs() interface{} {
	return category.NewFweEcomCategoryServiceUpdateCategoryNodeArgs()
}

func newFweEcomCategoryServiceUpdateCategoryNodeResult() interface{} {
	return category.NewFweEcomCategoryServiceUpdateCategoryNodeResult()
}

func queryPropertyValueHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceQueryPropertyValueArgs)
	realResult := result.(*category.FweEcomCategoryServiceQueryPropertyValueResult)
	success, err := handler.(category.FweEcomCategoryService).QueryPropertyValue(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceQueryPropertyValueArgs() interface{} {
	return category.NewFweEcomCategoryServiceQueryPropertyValueArgs()
}

func newFweEcomCategoryServiceQueryPropertyValueResult() interface{} {
	return category.NewFweEcomCategoryServiceQueryPropertyValueResult()
}

func queryPropertyValueListHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceQueryPropertyValueListArgs)
	realResult := result.(*category.FweEcomCategoryServiceQueryPropertyValueListResult)
	success, err := handler.(category.FweEcomCategoryService).QueryPropertyValueList(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceQueryPropertyValueListArgs() interface{} {
	return category.NewFweEcomCategoryServiceQueryPropertyValueListArgs()
}

func newFweEcomCategoryServiceQueryPropertyValueListResult() interface{} {
	return category.NewFweEcomCategoryServiceQueryPropertyValueListResult()
}

func mGetPropertyItemHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMGetPropertyItemArgs)
	realResult := result.(*category.FweEcomCategoryServiceMGetPropertyItemResult)
	success, err := handler.(category.FweEcomCategoryService).MGetPropertyItem(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMGetPropertyItemArgs() interface{} {
	return category.NewFweEcomCategoryServiceMGetPropertyItemArgs()
}

func newFweEcomCategoryServiceMGetPropertyItemResult() interface{} {
	return category.NewFweEcomCategoryServiceMGetPropertyItemResult()
}

func upsertPropertyValueHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceUpsertPropertyValueArgs)
	realResult := result.(*category.FweEcomCategoryServiceUpsertPropertyValueResult)
	success, err := handler.(category.FweEcomCategoryService).UpsertPropertyValue(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceUpsertPropertyValueArgs() interface{} {
	return category.NewFweEcomCategoryServiceUpsertPropertyValueArgs()
}

func newFweEcomCategoryServiceUpsertPropertyValueResult() interface{} {
	return category.NewFweEcomCategoryServiceUpsertPropertyValueResult()
}

func operatePropertyValueStatusHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceOperatePropertyValueStatusArgs)
	realResult := result.(*category.FweEcomCategoryServiceOperatePropertyValueStatusResult)
	success, err := handler.(category.FweEcomCategoryService).OperatePropertyValueStatus(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceOperatePropertyValueStatusArgs() interface{} {
	return category.NewFweEcomCategoryServiceOperatePropertyValueStatusArgs()
}

func newFweEcomCategoryServiceOperatePropertyValueStatusResult() interface{} {
	return category.NewFweEcomCategoryServiceOperatePropertyValueStatusResult()
}

func mGetAllLeafCategoryHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMGetAllLeafCategoryArgs)
	realResult := result.(*category.FweEcomCategoryServiceMGetAllLeafCategoryResult)
	success, err := handler.(category.FweEcomCategoryService).MGetAllLeafCategory(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMGetAllLeafCategoryArgs() interface{} {
	return category.NewFweEcomCategoryServiceMGetAllLeafCategoryArgs()
}

func newFweEcomCategoryServiceMGetAllLeafCategoryResult() interface{} {
	return category.NewFweEcomCategoryServiceMGetAllLeafCategoryResult()
}

func getPropertyMetaHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceGetPropertyMetaArgs)
	realResult := result.(*category.FweEcomCategoryServiceGetPropertyMetaResult)
	success, err := handler.(category.FweEcomCategoryService).GetPropertyMeta(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceGetPropertyMetaArgs() interface{} {
	return category.NewFweEcomCategoryServiceGetPropertyMetaArgs()
}

func newFweEcomCategoryServiceGetPropertyMetaResult() interface{} {
	return category.NewFweEcomCategoryServiceGetPropertyMetaResult()
}

func createTagMetaHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceCreateTagMetaArgs)
	realResult := result.(*category.FweEcomCategoryServiceCreateTagMetaResult)
	success, err := handler.(category.FweEcomCategoryService).CreateTagMeta(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceCreateTagMetaArgs() interface{} {
	return category.NewFweEcomCategoryServiceCreateTagMetaArgs()
}

func newFweEcomCategoryServiceCreateTagMetaResult() interface{} {
	return category.NewFweEcomCategoryServiceCreateTagMetaResult()
}

func updateTagMetaHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceUpdateTagMetaArgs)
	realResult := result.(*category.FweEcomCategoryServiceUpdateTagMetaResult)
	success, err := handler.(category.FweEcomCategoryService).UpdateTagMeta(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceUpdateTagMetaArgs() interface{} {
	return category.NewFweEcomCategoryServiceUpdateTagMetaArgs()
}

func newFweEcomCategoryServiceUpdateTagMetaResult() interface{} {
	return category.NewFweEcomCategoryServiceUpdateTagMetaResult()
}

func batchDelTagMetaHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceBatchDelTagMetaArgs)
	realResult := result.(*category.FweEcomCategoryServiceBatchDelTagMetaResult)
	success, err := handler.(category.FweEcomCategoryService).BatchDelTagMeta(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceBatchDelTagMetaArgs() interface{} {
	return category.NewFweEcomCategoryServiceBatchDelTagMetaArgs()
}

func newFweEcomCategoryServiceBatchDelTagMetaResult() interface{} {
	return category.NewFweEcomCategoryServiceBatchDelTagMetaResult()
}

func mGetTagByCodeHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMGetTagByCodeArgs)
	realResult := result.(*category.FweEcomCategoryServiceMGetTagByCodeResult)
	success, err := handler.(category.FweEcomCategoryService).MGetTagByCode(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMGetTagByCodeArgs() interface{} {
	return category.NewFweEcomCategoryServiceMGetTagByCodeArgs()
}

func newFweEcomCategoryServiceMGetTagByCodeResult() interface{} {
	return category.NewFweEcomCategoryServiceMGetTagByCodeResult()
}

func mGetTagByIDHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMGetTagByIDArgs)
	realResult := result.(*category.FweEcomCategoryServiceMGetTagByIDResult)
	success, err := handler.(category.FweEcomCategoryService).MGetTagByID(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMGetTagByIDArgs() interface{} {
	return category.NewFweEcomCategoryServiceMGetTagByIDArgs()
}

func newFweEcomCategoryServiceMGetTagByIDResult() interface{} {
	return category.NewFweEcomCategoryServiceMGetTagByIDResult()
}

func createPropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceCreatePropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceCreatePropertyResult)
	success, err := handler.(category.FweEcomCategoryService).CreateProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceCreatePropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceCreatePropertyArgs()
}

func newFweEcomCategoryServiceCreatePropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceCreatePropertyResult()
}

func updatePropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceUpdatePropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceUpdatePropertyResult)
	success, err := handler.(category.FweEcomCategoryService).UpdateProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceUpdatePropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceUpdatePropertyArgs()
}

func newFweEcomCategoryServiceUpdatePropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceUpdatePropertyResult()
}

func upsertPropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceUpsertPropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceUpsertPropertyResult)
	success, err := handler.(category.FweEcomCategoryService).UpsertProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceUpsertPropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceUpsertPropertyArgs()
}

func newFweEcomCategoryServiceUpsertPropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceUpsertPropertyResult()
}

func mDelPropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMDelPropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceMDelPropertyResult)
	success, err := handler.(category.FweEcomCategoryService).MDelProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMDelPropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceMDelPropertyArgs()
}

func newFweEcomCategoryServiceMDelPropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceMDelPropertyResult()
}

func mGetPropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceMGetPropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceMGetPropertyResult)
	success, err := handler.(category.FweEcomCategoryService).MGetProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceMGetPropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceMGetPropertyArgs()
}

func newFweEcomCategoryServiceMGetPropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceMGetPropertyResult()
}

func queryPropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceQueryPropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceQueryPropertyResult)
	success, err := handler.(category.FweEcomCategoryService).QueryProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceQueryPropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceQueryPropertyArgs()
}

func newFweEcomCategoryServiceQueryPropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceQueryPropertyResult()
}

func exportPropertyHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceExportPropertyArgs)
	realResult := result.(*category.FweEcomCategoryServiceExportPropertyResult)
	success, err := handler.(category.FweEcomCategoryService).ExportProperty(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceExportPropertyArgs() interface{} {
	return category.NewFweEcomCategoryServiceExportPropertyArgs()
}

func newFweEcomCategoryServiceExportPropertyResult() interface{} {
	return category.NewFweEcomCategoryServiceExportPropertyResult()
}

func queryCategoryHandler(ctx context.Context, handler interface{}, arg, result interface{}) error {
	realArg := arg.(*category.FweEcomCategoryServiceQueryCategoryArgs)
	realResult := result.(*category.FweEcomCategoryServiceQueryCategoryResult)
	success, err := handler.(category.FweEcomCategoryService).QueryCategory(ctx, realArg.Req)
	if err != nil {
		return err
	}
	realResult.Success = success
	return nil
}
func newFweEcomCategoryServiceQueryCategoryArgs() interface{} {
	return category.NewFweEcomCategoryServiceQueryCategoryArgs()
}

func newFweEcomCategoryServiceQueryCategoryResult() interface{} {
	return category.NewFweEcomCategoryServiceQueryCategoryResult()
}

type kClient struct {
	c client.Client
}

func newServiceClient(c client.Client) *kClient {
	return &kClient{
		c: c,
	}
}

func (p *kClient) MGetCategoryItem(ctx context.Context, req *category.MGetCategoryItemReq) (r *category.MGetCategoryItemResp, err error) {
	var _args category.FweEcomCategoryServiceMGetCategoryItemArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMGetCategoryItemResult
	if err = p.c.Call(ctx, "MGetCategoryItem", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetBizLineCategories(ctx context.Context, req *category.GetBizLineCategoriesReq) (r *category.GetBizLineCategoriesResp, err error) {
	var _args category.FweEcomCategoryServiceGetBizLineCategoriesArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceGetBizLineCategoriesResult
	if err = p.c.Call(ctx, "GetBizLineCategories", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateCategoryNode(ctx context.Context, req *category.CreateCategoryNodeReq) (r *category.CreateCategoryNodeResp, err error) {
	var _args category.FweEcomCategoryServiceCreateCategoryNodeArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceCreateCategoryNodeResult
	if err = p.c.Call(ctx, "CreateCategoryNode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateCategoryNode(ctx context.Context, req *category.UpdateCategoryNodeReq) (r *category.UpdateCategoryNodeResp, err error) {
	var _args category.FweEcomCategoryServiceUpdateCategoryNodeArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceUpdateCategoryNodeResult
	if err = p.c.Call(ctx, "UpdateCategoryNode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryPropertyValue(ctx context.Context, req *category.QueryPropertyValueReq) (r *category.QueryPropertyValueResp, err error) {
	var _args category.FweEcomCategoryServiceQueryPropertyValueArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceQueryPropertyValueResult
	if err = p.c.Call(ctx, "QueryPropertyValue", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryPropertyValueList(ctx context.Context, req *category.QueryPropertyValueListReq) (r *category.QueryPropertyValueListResp, err error) {
	var _args category.FweEcomCategoryServiceQueryPropertyValueListArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceQueryPropertyValueListResult
	if err = p.c.Call(ctx, "QueryPropertyValueList", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetPropertyItem(ctx context.Context, req *category.MGetPropertyItemReq) (r *category.MGetPropertyItemResp, err error) {
	var _args category.FweEcomCategoryServiceMGetPropertyItemArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMGetPropertyItemResult
	if err = p.c.Call(ctx, "MGetPropertyItem", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpsertPropertyValue(ctx context.Context, req *category.UpsertPropertyValueReq) (r *category.UpsertPropertyValueResp, err error) {
	var _args category.FweEcomCategoryServiceUpsertPropertyValueArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceUpsertPropertyValueResult
	if err = p.c.Call(ctx, "UpsertPropertyValue", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) OperatePropertyValueStatus(ctx context.Context, req *category.OperatePropertyValueStatusReq) (r *category.OperatePropertyValueStatusResp, err error) {
	var _args category.FweEcomCategoryServiceOperatePropertyValueStatusArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceOperatePropertyValueStatusResult
	if err = p.c.Call(ctx, "OperatePropertyValueStatus", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq) (r *category.MGetAllLeafCategoryResp, err error) {
	var _args category.FweEcomCategoryServiceMGetAllLeafCategoryArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMGetAllLeafCategoryResult
	if err = p.c.Call(ctx, "MGetAllLeafCategory", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) GetPropertyMeta(ctx context.Context, req *category.GetPropertyMetaReq) (r *category.GetPropertyMetaResp, err error) {
	var _args category.FweEcomCategoryServiceGetPropertyMetaArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceGetPropertyMetaResult
	if err = p.c.Call(ctx, "GetPropertyMeta", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateTagMeta(ctx context.Context, req *category.CreateTagMetaReq) (r *category.CreateTagMetaResp, err error) {
	var _args category.FweEcomCategoryServiceCreateTagMetaArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceCreateTagMetaResult
	if err = p.c.Call(ctx, "CreateTagMeta", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateTagMeta(ctx context.Context, req *category.UpdateTagMetaReq) (r *category.UpdateTagMetaResp, err error) {
	var _args category.FweEcomCategoryServiceUpdateTagMetaArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceUpdateTagMetaResult
	if err = p.c.Call(ctx, "UpdateTagMeta", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) BatchDelTagMeta(ctx context.Context, req *category.BatchDelTagMetaReq) (r *category.BatchDelTagMetaResp, err error) {
	var _args category.FweEcomCategoryServiceBatchDelTagMetaArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceBatchDelTagMetaResult
	if err = p.c.Call(ctx, "BatchDelTagMeta", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetTagByCode(ctx context.Context, req *category.MGetTagByCodeReq) (r *category.MGetTagByCodeResp, err error) {
	var _args category.FweEcomCategoryServiceMGetTagByCodeArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMGetTagByCodeResult
	if err = p.c.Call(ctx, "MGetTagByCode", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetTagByID(ctx context.Context, req *category.MGetTagByIDReq) (r *category.MGetTagByIDResp, err error) {
	var _args category.FweEcomCategoryServiceMGetTagByIDArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMGetTagByIDResult
	if err = p.c.Call(ctx, "MGetTagByID", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) CreateProperty(ctx context.Context, req *category.CreatePropertyReq) (r *category.CreatePropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceCreatePropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceCreatePropertyResult
	if err = p.c.Call(ctx, "CreateProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpdateProperty(ctx context.Context, req *category.UpdatePropertyReq) (r *category.UpdatePropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceUpdatePropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceUpdatePropertyResult
	if err = p.c.Call(ctx, "UpdateProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) UpsertProperty(ctx context.Context, req *category.UpsertPropertyReq) (r *category.UpsertPropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceUpsertPropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceUpsertPropertyResult
	if err = p.c.Call(ctx, "UpsertProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MDelProperty(ctx context.Context, req *category.MDelPropertyReq) (r *category.MDelPropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceMDelPropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMDelPropertyResult
	if err = p.c.Call(ctx, "MDelProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) MGetProperty(ctx context.Context, req *category.MGetPropertyReq) (r *category.MGetPropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceMGetPropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceMGetPropertyResult
	if err = p.c.Call(ctx, "MGetProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryProperty(ctx context.Context, req *category.QueryPropertyReq) (r *category.QueryPropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceQueryPropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceQueryPropertyResult
	if err = p.c.Call(ctx, "QueryProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) ExportProperty(ctx context.Context, req *category.ExportPropertyReq) (r *category.ExportPropertyRsp, err error) {
	var _args category.FweEcomCategoryServiceExportPropertyArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceExportPropertyResult
	if err = p.c.Call(ctx, "ExportProperty", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}

func (p *kClient) QueryCategory(ctx context.Context, req *category.QueryCategoryReq) (r *category.QueryCategoryRsp, err error) {
	var _args category.FweEcomCategoryServiceQueryCategoryArgs
	_args.Req = req
	var _result category.FweEcomCategoryServiceQueryCategoryResult
	if err = p.c.Call(ctx, "QueryCategory", &_args, &_result); err != nil {
		return
	}
	return _result.GetSuccess(), nil
}
