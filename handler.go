package main

import (
	"code.byted.org/motor/fwe_category/internal/handler"
	"code.byted.org/motor/fwe_category/internal/handler/cpv_handler"
	"code.byted.org/motor/fwe_category/internal/handler/property_handler"
	"code.byted.org/motor/fwe_category/internal/handler/tag"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"context"
)

type FweEcomCategoryServiceImpl struct {
}

// MGetCategoryItem implements the FweCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MGetCategoryItem(ctx context.Context, req *category.MGetCategoryItemReq) (resp *category.MGetCategoryItemResp, err error) {
	return handler.NewMGetSkuItemService().MGetCategoryItem(ctx, req), nil
}

// QueryPropertyValue implements the FweCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) QueryPropertyValue(ctx context.Context, req *category.QueryPropertyValueReq) (resp *category.QueryPropertyValueResp, err error) {
	return property_handler.NewQueryPropertyValueHandler().QueryPropertyValue(ctx, req), nil
}

// QueryPropertyValueList implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) QueryPropertyValueList(ctx context.Context, req *category.QueryPropertyValueListReq) (resp *category.QueryPropertyValueListResp, err error) {
	return property_handler.NewQueryPropertyValueListHandler().QueryPropertyValue(ctx, req), nil
}

// MGetPropertyItem implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MGetPropertyItem(ctx context.Context, req *category.MGetPropertyItemReq) (resp *category.MGetPropertyItemResp, err error) {
	return property_handler.NewMGetPropertyItemHandler().MGetPropertyItem(ctx, req), nil
}

// UpsertPropertyValue implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) UpsertPropertyValue(ctx context.Context, req *category.UpsertPropertyValueReq) (resp *category.UpsertPropertyValueResp, err error) {
	return property_handler.NewUpsertPropertyValueHandler().UpsertPropertyValue(ctx, req), nil
}

// OperatePropertyValueStatus implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) OperatePropertyValueStatus(ctx context.Context, req *category.OperatePropertyValueStatusReq) (resp *category.OperatePropertyValueStatusResp, err error) {
	return handler.NewOperatePropertyValueStatusService().OperatePropertyValueStatus(ctx, req), nil
}

// GetBizLineCategories implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) GetBizLineCategories(ctx context.Context, req *category.GetBizLineCategoriesReq) (resp *category.GetBizLineCategoriesResp, err error) {
	return handler.NewGetBizLineCategoriesService().GetBizLineCategories(ctx, req), nil
}

// CreateTagMeta implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) CreateTagMeta(ctx context.Context, req *category.CreateTagMetaReq) (resp *category.CreateTagMetaResp, err error) {
	return tag.NewCreateTagMetaService().CreateTagMeta(ctx, req), nil
}

// UpdateTagMeta implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) UpdateTagMeta(ctx context.Context, req *category.UpdateTagMetaReq) (resp *category.UpdateTagMetaResp, err error) {
	return tag.NewUpdateTagMetaService().UpdateTagMeta(ctx, req), nil
}

// BatchDelTagMeta implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) BatchDelTagMeta(ctx context.Context, req *category.BatchDelTagMetaReq) (resp *category.BatchDelTagMetaResp, err error) {
	return tag.NewBatchDelTagMetaService().BatchDelTagMeta(ctx, req), nil
}

// MGetTagByCode implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MGetTagByCode(ctx context.Context, req *category.MGetTagByCodeReq) (resp *category.MGetTagByCodeResp, err error) {
	return tag.NewMGetTagByCodeService().MGetTagByCode(ctx, req), nil
}

// MGetTagByID implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MGetTagByID(ctx context.Context, req *category.MGetTagByIDReq) (resp *category.MGetTagByIDResp, err error) {
	return tag.NewMGetTagByIDService().MGetTagByID(ctx, req), nil
}

// CreateCategoryNode implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) CreateCategoryNode(ctx context.Context, req *category.CreateCategoryNodeReq) (resp *category.CreateCategoryNodeResp, err error) {
	return handler.NewCreateCategoryNodeService().Create(ctx, req), nil
}

// UpdateCategoryNode implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) UpdateCategoryNode(ctx context.Context, req *category.UpdateCategoryNodeReq) (resp *category.UpdateCategoryNodeResp, err error) {
	return handler.NewUpdateCategoryNodeService().Update(ctx, req), nil
}

// UpsertProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) UpsertProperty(ctx context.Context, req *category.UpsertPropertyReq) (resp *category.UpsertPropertyRsp, err error) { // ignore_security_alert
	return cpv_handler.PropertyHandler.UpsertProperty(ctx, req), nil
}

// MDelProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MDelProperty(ctx context.Context, req *category.MDelPropertyReq) (resp *category.MDelPropertyRsp, err error) {
	return cpv_handler.PropertyHandler.MDelProperty(ctx, req), nil
}

// MGetProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MGetProperty(ctx context.Context, req *category.MGetPropertyReq) (resp *category.MGetPropertyRsp, err error) {
	return cpv_handler.PropertyHandler.MGetProperty(ctx, req), nil
}

// QueryProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) QueryProperty(ctx context.Context, req *category.QueryPropertyReq) (resp *category.QueryPropertyRsp, err error) {
	return cpv_handler.PropertyHandler.QueryProperty(ctx, req), nil
}

// ExportProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) ExportProperty(ctx context.Context, req *category.ExportPropertyReq) (resp *category.ExportPropertyRsp, err error) {
	return cpv_handler.PropertyHandler.ExportProperty(ctx, req), nil
}

// QueryCategory implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) QueryCategory(ctx context.Context, req *category.QueryCategoryReq) (resp *category.QueryCategoryRsp, err error) {
	return cpv_handler.CategoryHandler.QueryCategory(ctx, req), nil
}

// CreateProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) CreateProperty(ctx context.Context, req *category.CreatePropertyReq) (resp *category.CreatePropertyRsp, err error) {
	return cpv_handler.PropertyHandler.CreateProperty(ctx, req), nil
}

// UpdateProperty implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) UpdateProperty(ctx context.Context, req *category.UpdatePropertyReq) (resp *category.UpdatePropertyRsp, err error) {
	return cpv_handler.PropertyHandler.UpdateProperty(ctx, req), nil
}

// MGetAllLeafCategory implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq) (resp *category.MGetAllLeafCategoryResp, err error) {
	return cpv_handler.CategoryHandler.MGetAllLeafCategory(ctx, req), nil
}

// GetPropertyMeta implements the FweEcomCategoryServiceImpl interface.
func (s *FweEcomCategoryServiceImpl) GetPropertyMeta(ctx context.Context, req *category.GetPropertyMetaReq) (resp *category.GetPropertyMetaResp, err error) {
	return cpv_handler.PropertyHandler.GetPropertyMeta(ctx, req), nil
}
