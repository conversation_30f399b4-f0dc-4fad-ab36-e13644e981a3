// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNameProperty = "motor_category_property"

// Property mapped from table <motor_category_property>
type Property struct {
	ID                    int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`                                                                             // 属性项id
	CategoryID            int64     `gorm:"column:category_id;type:bigint unsigned;not null;uniqueIndex:uk_unique_category_id_key,priority:1;index:idx_category,priority:1" json:"category_id"` // 类目id
	Name                  string    `gorm:"column:name;type:varchar(128);not null" json:"name"`                                                                                                 // 属性项名
	Alias_                string    `gorm:"column:alias;type:varchar(128);not null" json:"alias"`                                                                                               // 属性项别名
	Status                int32     `gorm:"column:status;type:int;not null" json:"status"`                                                                                                      // 属性项状态
	Source                int32     `gorm:"column:source;type:int;not null" json:"source"`                                                                                                      // 来源
	GroupKey              string    `gorm:"column:group_key;type:varchar(128);not null" json:"group_key"`                                                                                       // 属性组key
	GroupName             string    `gorm:"column:group_name;type:varchar(128);not null" json:"group_name"`                                                                                     // 属性组name
	UniqueKey             string    `gorm:"column:unique_key;type:varchar(128);not null;uniqueIndex:uk_unique_category_id_key,priority:2" json:"unique_key"`                                    // 唯一key
	UniqueConstraint      int32     `gorm:"column:unique_constraint;type:int;not null" json:"unique_constraint"`                                                                                // 唯一性约束 0-不约束 1-全局唯一 2-店铺唯一
	InputType             int32     `gorm:"column:input_type;type:int;not null" json:"input_type"`                                                                                              // 输入类型1单选 2多选, 3文本输入
	FieldType             int32     `gorm:"column:field_type;type:int;not null" json:"field_type"`                                                                                              // 属性值字段类型，0-str，1-int
	IsRequired            int32     `gorm:"column:is_required;type:int;not null" json:"is_required"`                                                                                            // 是否必选 1 必选，0 非必选
	Type                  int32     `gorm:"column:type;type:int;not null" json:"type"`                                                                                                          // 属性类型，0 绑定属性 1关键属性 2售卖属性 3 商品属性
	Sequence              int32     `gorm:"column:sequence;type:int;not null" json:"sequence"`                                                                                                  // 排序值
	ContainPropertyValues int32     `gorm:"column:contain_property_values;type:int;not null" json:"contain_property_values"`                                                                    // 是否包含属性值 true 包含 false 不包含
	CreateTime            time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"`                                                            // 创建时间
	UpdateTime            time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`                                                            // 修改时间
	CreateUID             string    `gorm:"column:create_uid;type:varchar(128);not null" json:"create_uid"`                                                                                     // 创建用户id
	UpdateUID             string    `gorm:"column:update_uid;type:varchar(128);not null" json:"update_uid"`                                                                                     // 编辑用户id
	Feature               string    `gorm:"column:feature;type:text" json:"feature"`                                                                                                            // 扩展字段
	IsDelete              int32     `gorm:"column:is_delete;type:int;not null" json:"is_delete"`                                                                                                // 是否被删除
	IsTest                int32     `gorm:"column:is_test;type:int;not null" json:"is_test"`                                                                                                    // 是否是测试
}

// TableName Property's table name
func (*Property) TableName() string {
	return TableNameProperty
}
