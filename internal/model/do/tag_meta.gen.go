// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNameTagMeta = "tag_meta"

// TagMeta mapped from table <tag_meta>
type TagMeta struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`                                                            // 主键ID
	TagCode      string    `gorm:"column:tag_code;type:varchar(64);not null;index:idx_tag_code,priority:1;uniqueIndex:uniq_tag_type_code,priority:2" json:"tag_code"` // 标签code
	Name         string    `gorm:"column:name;type:varchar(64);not null" json:"name"`                                                                                 // 标签名称
	Type         int32     `gorm:"column:type;type:int;not null;uniqueIndex:uniq_tag_type_code,priority:1" json:"type"`                                               // 标签类型
	Source       int32     `gorm:"column:source;type:int;not null" json:"source"`                                                                                     // 标签来源
	Status       int32     `gorm:"column:status;type:int;not null" json:"status"`                                                                                     // 标签状态
	TagDesc      string    `gorm:"column:tag_desc;type:text" json:"tag_desc"`                                                                                         // 标签描述
	OperatorID   string    `gorm:"column:operator_id;type:varchar(32);not null" json:"operator_id"`                                                                   // 操作人id
	OperatorName string    `gorm:"column:operator_name;type:varchar(32);not null" json:"operator_name"`                                                               // 操作人名称
	Extra        string    `gorm:"column:extra;type:text" json:"extra"`                                                                                               // 额外信息
	CreatedTime  time.Time `gorm:"column:created_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_time"`                                         // 创建时间
	UpdatedTime  time.Time `gorm:"column:updated_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_time"`                                         // 更新时间
	IsDeleted    int32     `gorm:"column:is_deleted;type:tinyint;not null" json:"is_deleted"`                                                                         // 是否删除
	TagCodeType  string    `gorm:"column:tag_code_type;type:varchar(64);not null" json:"tag_code_type"`                                                               // tag_code + type
}

// TableName TagMeta's table name
func (*TagMeta) TableName() string {
	return TableNameTagMeta
}
