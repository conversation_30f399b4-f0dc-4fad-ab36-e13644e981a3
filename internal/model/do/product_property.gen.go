// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNameProductProperty = "product_property"

// ProductProperty mapped from table <product_property>
type ProductProperty struct {
	ID             int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`                                 // id
	PropertyKey    string    `gorm:"column:property_key;type:varchar(128);not null;uniqueIndex:uniq_idx_pro,priority:1" json:"property_key"` // 属性唯一key
	PropertyType   int32     `gorm:"column:property_type;type:int;not null" json:"property_type"`                                            // 属性类型0商品属性1附着属性
	FieldType      string    `gorm:"column:field_type;type:varchar(128);not null" json:"field_type"`                                         // 属性值字段类型
	ClassType      string    `gorm:"column:class_type;type:varchar(128);not null" json:"class_type"`                                         // 字段分类
	Name           string    `gorm:"column:name;type:varchar(128);not null" json:"name"`                                                     // 属性名
	Description    string    `gorm:"column:description;type:varchar(255);not null" json:"description"`                                       // 属性描述
	Status         int32     `gorm:"column:status;type:int;not null" json:"status"`                                                          // 属性状态
	CreateUID      string    `gorm:"column:create_uid;type:varchar(128);not null" json:"create_uid"`                                         // 创建用户id
	CreateName     string    `gorm:"column:create_name;type:varchar(128);not null" json:"create_name"`                                       // 创建用户名
	UpdateUID      string    `gorm:"column:update_uid;type:varchar(128);not null" json:"update_uid"`                                         // 编辑用户id
	UpdateName     string    `gorm:"column:update_name;type:varchar(128);not null" json:"update_name"`                                       // 编辑用户名
	CreateTime     time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"`                // 创建时间
	UpdateTime     time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`                // 修改时间
	DeleteTime     int64     `gorm:"column:delete_time;type:bigint;not null;uniqueIndex:uniq_idx_pro,priority:2" json:"delete_time"`         // 删除时间
	ValueDesc      string    `gorm:"column:value_desc;type:json" json:"value_desc"`                                                          // 属性值描述
	PropertyConfig string    `gorm:"column:property_config;type:json" json:"property_config"`                                                // 属性配置项
}

// TableName ProductProperty's table name
func (*ProductProperty) TableName() string {
	return TableNameProductProperty
}
