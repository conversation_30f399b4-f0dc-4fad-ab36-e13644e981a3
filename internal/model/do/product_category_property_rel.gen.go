// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNameProductCategoryPropertyRel = "product_category_property_rel"

// ProductCategoryPropertyRel mapped from table <product_category_property_rel>
type ProductCategoryPropertyRel struct {
	ID          int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`                                                              // 主键ID
	CategoryID  int64     `gorm:"column:category_id;type:bigint unsigned;not null;uniqueIndex:uniq_idx_cat_pro,priority:1" json:"category_id"`                         // 类目id
	PropertyKey string    `gorm:"column:property_key;type:varchar(255);not null;uniqueIndex:uniq_idx_cat_pro,priority:2;index:idx_pro,priority:1" json:"property_key"` // 属性项key
	Status      int32     `gorm:"column:status;type:int;not null" json:"status"`                                                                                       // 属性项状态 1启用 2禁用
	CreateUID   string    `gorm:"column:create_uid;type:varchar(128);not null" json:"create_uid"`                                                                      // 创建用户id
	CreateName  string    `gorm:"column:create_name;type:varchar(128);not null" json:"create_name"`                                                                    // 创建用户名
	UpdateUID   string    `gorm:"column:update_uid;type:varchar(128);not null" json:"update_uid"`                                                                      // 编辑用户id
	UpdateName  string    `gorm:"column:update_name;type:varchar(128);not null" json:"update_name"`                                                                    // 编辑用户名
	CreatedTime time.Time `gorm:"column:created_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_time"`                                           // 创建时间
	UpdatedTime time.Time `gorm:"column:updated_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_time"`                                           // 修改时间
	DeleteTime  int64     `gorm:"column:delete_time;type:bigint;not null;uniqueIndex:uniq_idx_cat_pro,priority:3" json:"delete_time"`                                  // 删除时间
}

// TableName ProductCategoryPropertyRel's table name
func (*ProductCategoryPropertyRel) TableName() string {
	return TableNameProductCategoryPropertyRel
}
