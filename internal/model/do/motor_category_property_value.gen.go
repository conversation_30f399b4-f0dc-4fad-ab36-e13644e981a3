// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNamePropertyValue = "motor_category_property_value"

// PropertyValue mapped from table <motor_category_property_value>
type PropertyValue struct {
	ID            int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`                                                                                       // 属性值id
	CategoryID    int64     `gorm:"column:category_id;type:bigint unsigned;not null;index:idx_category_property_parent,priority:1;uniqueIndex:uniq_property_value,priority:1" json:"category_id"` // 类目id
	PropertyID    int64     `gorm:"column:property_id;type:bigint unsigned;not null;index:idx_category_property_parent,priority:2" json:"property_id"`                                            // 属性项id
	PropertyKey   string    `gorm:"column:property_key;type:varchar(128);not null;uniqueIndex:uniq_property_value,priority:4" json:"property_key"`                                                // 属性key
	ShopID        string    `gorm:"column:shop_id;type:varchar(128);not null;uniqueIndex:uniq_property_value,priority:2" json:"shop_id"`                                                          // 所属店铺ID, 无则所有店铺共用
	SpuID         int64     `gorm:"column:spu_id;type:bigint;not null;uniqueIndex:uniq_property_value,priority:3" json:"spu_id"`                                                                  // 所属spu_id, 无则所有spu共用
	ParentValueID int64     `gorm:"column:parent_value_id;type:bigint unsigned;not null;index:idx_category_property_parent,priority:3" json:"parent_value_id"`                                    // 父属性id，实现级联效果
	Name          string    `gorm:"column:name;type:varchar(128);not null;uniqueIndex:uniq_property_value,priority:5" json:"name"`                                                                // 属性值名称
	Alias_        string    `gorm:"column:alias;type:varchar(128);not null" json:"alias"`                                                                                                         // 属性值别名
	Status        int32     `gorm:"column:status;type:int;not null" json:"status"`                                                                                                                // 属性值状态
	OutID         string    `gorm:"column:out_id;type:varchar(128);not null" json:"out_id"`                                                                                                       // 外部id
	Sequence      int32     `gorm:"column:sequence;type:int;not null" json:"sequence"`                                                                                                            // 排序值
	Price         int64     `gorm:"column:price;type:bigint;not null" json:"price"`                                                                                                               // 价格
	MarketPrice   int64     `gorm:"column:market_price;type:bigint;not null" json:"market_price"`                                                                                                 // 划线价
	CreateTime    time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"`                                                                      // 创建时间
	UpdateTime    time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"`                                                                      // 修改时间
	CreateUID     string    `gorm:"column:create_uid;type:varchar(128);not null" json:"create_uid"`                                                                                               // 创建用户id
	UpdateUID     string    `gorm:"column:update_uid;type:varchar(128);not null" json:"update_uid"`                                                                                               // 编辑用户id
	Feature       string    `gorm:"column:feature;type:text" json:"feature"`                                                                                                                      // 扩展字段
	IsDelete      int32     `gorm:"column:is_delete;type:int;not null" json:"is_delete"`                                                                                                          // 是否被删除
	IsTest        int32     `gorm:"column:is_test;type:int;not null" json:"is_test"`                                                                                                              // 是否是测试
}

// TableName PropertyValue's table name
func (*PropertyValue) TableName() string {
	return TableNamePropertyValue
}
