// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNameCategory = "motor_category"

// Category mapped from table <motor_category>
type Category struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"` // 类目ID
	Name         string    `gorm:"column:name;type:varchar(128);not null" json:"name"`                     // 类目名称
	CategoryKey  string    `gorm:"column:category_key;type:varchar(255);not null;uniqueIndex:idx_unique_category_key,priority:1" json:"category_key"`
	ParentID     int64     `gorm:"column:parent_id;type:bigint;not null" json:"parent_id"`                                  // 上级ID，一级类目上级ID为0
	Level        int32     `gorm:"column:level;type:int;not null;default:1" json:"level"`                                   // 类目层级，最高支持4层
	IsLeaf       int32     `gorm:"column:is_leaf;type:int;not null" json:"is_leaf"`                                         // 是否为叶子节点，0非叶子节点，1叶子节点
	Sequence     int32     `gorm:"column:sequence;type:int;not null" json:"sequence"`                                       // 商品类目排序值
	Description  string    `gorm:"column:description;type:varchar(512);not null" json:"description"`                        // 类目备注说明
	Status       int32     `gorm:"column:status;type:int;not null" json:"status"`                                           // 类目状态
	TOuterID     string    `gorm:"column:t_outer_id;type:varchar(128);not null" json:"t_outer_id"`                          // 外部id
	AuditStatus  int32     `gorm:"column:audit_status;type:int;not null" json:"audit_status"`                               // 类目审核状态
	CreateTime   time.Time `gorm:"column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"create_time"` // 创建时间
	UpdateTime   time.Time `gorm:"column:update_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"update_time"` // 修改时间
	IsDelete     int32     `gorm:"column:is_delete;type:int;not null" json:"is_delete"`                                     // 是否被删除
	IsTest       int32     `gorm:"column:is_test;type:int;not null" json:"is_test"`                                         // 是否是测试
	Extra        string    `gorm:"column:extra;type:text" json:"extra"`                                                     // 额外信息
	BizLine      int32     `gorm:"column:biz_line;type:int;not null" json:"biz_line"`                                       // 业务线
	CategoryType int32     `gorm:"column:category_type;type:tinyint;not null" json:"category_type"`                         // 类目类型
}

// TableName Category's table name
func (*Category) TableName() string {
	return TableNameCategory
}
