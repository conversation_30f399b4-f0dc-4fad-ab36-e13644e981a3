// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package do

import (
	"time"
)

const TableNameTagValue = "tag_value"

// TagValue mapped from table <tag_value>
type TagValue struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`                    // 主键ID
	TagCode      string    `gorm:"column:tag_code;type:varchar(64);not null" json:"tag_code"`                                 // 标签code
	TagValue     string    `gorm:"column:tag_value;type:varchar(128);not null" json:"tag_value"`                              // 标签值
	TagDesc      string    `gorm:"column:tag_desc;type:text" json:"tag_desc"`                                                 // 标签值描述
	OperatorID   string    `gorm:"column:operator_id;type:varchar(32);not null" json:"operator_id"`                           // 操作人id
	OperatorName string    `gorm:"column:operator_name;type:varchar(32);not null" json:"operator_name"`                       // 操作人名称
	Extra        string    `gorm:"column:extra;type:text" json:"extra"`                                                       // 额外信息
	CreatedTime  time.Time `gorm:"column:created_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_time"` // 创建时间
	UpdatedTime  time.Time `gorm:"column:updated_time;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_time"` // 更新时间
	IsDeleted    int32     `gorm:"column:is_deleted;type:tinyint;not null" json:"is_deleted"`                                 // 是否删除
	TagCodeType  string    `gorm:"column:tag_code_type;type:varchar(64);not null" json:"tag_code_type"`                       // tag_code + type
}

// TableName TagValue's table name
func (*TagValue) TableName() string {
	return TableNameTagValue
}
