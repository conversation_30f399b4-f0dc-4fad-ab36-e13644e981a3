// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newPropertyValue(db *gorm.DB, opts ...gen.DOOption) propertyValue {
	_propertyValue := propertyValue{}

	_propertyValue.propertyValueDo.UseDB(db, opts...)
	_propertyValue.propertyValueDo.UseModel(&do.PropertyValue{})

	tableName := _propertyValue.propertyValueDo.TableName()
	_propertyValue.ALL = field.NewAsterisk(tableName)
	_propertyValue.ID = field.NewInt64(tableName, "id")
	_propertyValue.CategoryID = field.NewInt64(tableName, "category_id")
	_propertyValue.PropertyID = field.NewInt64(tableName, "property_id")
	_propertyValue.PropertyKey = field.NewString(tableName, "property_key")
	_propertyValue.ShopID = field.NewString(tableName, "shop_id")
	_propertyValue.SpuID = field.NewInt64(tableName, "spu_id")
	_propertyValue.ParentValueID = field.NewInt64(tableName, "parent_value_id")
	_propertyValue.Name = field.NewString(tableName, "name")
	_propertyValue.Alias_ = field.NewString(tableName, "alias")
	_propertyValue.Status = field.NewInt32(tableName, "status")
	_propertyValue.OutID = field.NewString(tableName, "out_id")
	_propertyValue.Sequence = field.NewInt32(tableName, "sequence")
	_propertyValue.Price = field.NewInt64(tableName, "price")
	_propertyValue.MarketPrice = field.NewInt64(tableName, "market_price")
	_propertyValue.CreateTime = field.NewTime(tableName, "create_time")
	_propertyValue.UpdateTime = field.NewTime(tableName, "update_time")
	_propertyValue.CreateUID = field.NewString(tableName, "create_uid")
	_propertyValue.UpdateUID = field.NewString(tableName, "update_uid")
	_propertyValue.Feature = field.NewString(tableName, "feature")
	_propertyValue.IsDelete = field.NewInt32(tableName, "is_delete")
	_propertyValue.IsTest = field.NewInt32(tableName, "is_test")

	_propertyValue.fillFieldMap()

	return _propertyValue
}

type propertyValue struct {
	propertyValueDo propertyValueDo

	ALL           field.Asterisk
	ID            field.Int64  // 属性值id
	CategoryID    field.Int64  // 类目id
	PropertyID    field.Int64  // 属性项id
	PropertyKey   field.String // 属性key
	ShopID        field.String // 所属店铺ID, 无则所有店铺共用
	SpuID         field.Int64  // 所属spu_id, 无则所有spu共用
	ParentValueID field.Int64  // 父属性id，实现级联效果
	Name          field.String // 属性值名称
	Alias_        field.String // 属性值别名
	Status        field.Int32  // 属性值状态
	OutID         field.String // 外部id
	Sequence      field.Int32  // 排序值
	Price         field.Int64  // 价格
	MarketPrice   field.Int64  // 划线价
	CreateTime    field.Time   // 创建时间
	UpdateTime    field.Time   // 修改时间
	CreateUID     field.String // 创建用户id
	UpdateUID     field.String // 编辑用户id
	Feature       field.String // 扩展字段
	IsDelete      field.Int32  // 是否被删除
	IsTest        field.Int32  // 是否是测试

	fieldMap map[string]field.Expr
}

func (p propertyValue) Table(newTableName string) *propertyValue {
	p.propertyValueDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p propertyValue) As(alias string) *propertyValue {
	p.propertyValueDo.DO = *(p.propertyValueDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *propertyValue) updateTableName(table string) *propertyValue {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.CategoryID = field.NewInt64(table, "category_id")
	p.PropertyID = field.NewInt64(table, "property_id")
	p.PropertyKey = field.NewString(table, "property_key")
	p.ShopID = field.NewString(table, "shop_id")
	p.SpuID = field.NewInt64(table, "spu_id")
	p.ParentValueID = field.NewInt64(table, "parent_value_id")
	p.Name = field.NewString(table, "name")
	p.Alias_ = field.NewString(table, "alias")
	p.Status = field.NewInt32(table, "status")
	p.OutID = field.NewString(table, "out_id")
	p.Sequence = field.NewInt32(table, "sequence")
	p.Price = field.NewInt64(table, "price")
	p.MarketPrice = field.NewInt64(table, "market_price")
	p.CreateTime = field.NewTime(table, "create_time")
	p.UpdateTime = field.NewTime(table, "update_time")
	p.CreateUID = field.NewString(table, "create_uid")
	p.UpdateUID = field.NewString(table, "update_uid")
	p.Feature = field.NewString(table, "feature")
	p.IsDelete = field.NewInt32(table, "is_delete")
	p.IsTest = field.NewInt32(table, "is_test")

	p.fillFieldMap()

	return p
}

func (p *propertyValue) WithContext(ctx context.Context) *propertyValueDo {
	return p.propertyValueDo.WithContext(ctx)
}

func (p propertyValue) TableName() string { return p.propertyValueDo.TableName() }

func (p propertyValue) Alias() string { return p.propertyValueDo.Alias() }

func (p *propertyValue) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *propertyValue) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 21)
	p.fieldMap["id"] = p.ID
	p.fieldMap["category_id"] = p.CategoryID
	p.fieldMap["property_id"] = p.PropertyID
	p.fieldMap["property_key"] = p.PropertyKey
	p.fieldMap["shop_id"] = p.ShopID
	p.fieldMap["spu_id"] = p.SpuID
	p.fieldMap["parent_value_id"] = p.ParentValueID
	p.fieldMap["name"] = p.Name
	p.fieldMap["alias"] = p.Alias_
	p.fieldMap["status"] = p.Status
	p.fieldMap["out_id"] = p.OutID
	p.fieldMap["sequence"] = p.Sequence
	p.fieldMap["price"] = p.Price
	p.fieldMap["market_price"] = p.MarketPrice
	p.fieldMap["create_time"] = p.CreateTime
	p.fieldMap["update_time"] = p.UpdateTime
	p.fieldMap["create_uid"] = p.CreateUID
	p.fieldMap["update_uid"] = p.UpdateUID
	p.fieldMap["feature"] = p.Feature
	p.fieldMap["is_delete"] = p.IsDelete
	p.fieldMap["is_test"] = p.IsTest
}

func (p propertyValue) clone(db *gorm.DB) propertyValue {
	p.propertyValueDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p propertyValue) replaceDB(db *gorm.DB) propertyValue {
	p.propertyValueDo.ReplaceDB(db)
	return p
}

type propertyValueDo struct{ gen.DO }

func (p propertyValueDo) Debug() *propertyValueDo {
	return p.withDO(p.DO.Debug())
}

func (p propertyValueDo) WithContext(ctx context.Context) *propertyValueDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p propertyValueDo) ReadDB() *propertyValueDo {
	return p.Clauses(dbresolver.Read)
}

func (p propertyValueDo) WriteDB() *propertyValueDo {
	return p.Clauses(dbresolver.Write)
}

func (p propertyValueDo) Session(config *gorm.Session) *propertyValueDo {
	return p.withDO(p.DO.Session(config))
}

func (p propertyValueDo) Clauses(conds ...clause.Expression) *propertyValueDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p propertyValueDo) Returning(value interface{}, columns ...string) *propertyValueDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p propertyValueDo) Not(conds ...gen.Condition) *propertyValueDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p propertyValueDo) Or(conds ...gen.Condition) *propertyValueDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p propertyValueDo) Select(conds ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p propertyValueDo) Where(conds ...gen.Condition) *propertyValueDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p propertyValueDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *propertyValueDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p propertyValueDo) Order(conds ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p propertyValueDo) Distinct(cols ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p propertyValueDo) Omit(cols ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p propertyValueDo) Join(table schema.Tabler, on ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p propertyValueDo) LeftJoin(table schema.Tabler, on ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p propertyValueDo) RightJoin(table schema.Tabler, on ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p propertyValueDo) Group(cols ...field.Expr) *propertyValueDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p propertyValueDo) Having(conds ...gen.Condition) *propertyValueDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p propertyValueDo) Limit(limit int) *propertyValueDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p propertyValueDo) Offset(offset int) *propertyValueDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p propertyValueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *propertyValueDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p propertyValueDo) Unscoped() *propertyValueDo {
	return p.withDO(p.DO.Unscoped())
}

func (p propertyValueDo) Create(values ...*do.PropertyValue) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p propertyValueDo) CreateInBatches(values []*do.PropertyValue, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p propertyValueDo) Save(values ...*do.PropertyValue) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p propertyValueDo) First() (*do.PropertyValue, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.PropertyValue), nil
	}
}

func (p propertyValueDo) Take() (*do.PropertyValue, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.PropertyValue), nil
	}
}

func (p propertyValueDo) Last() (*do.PropertyValue, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.PropertyValue), nil
	}
}

func (p propertyValueDo) Find() ([]*do.PropertyValue, error) {
	result, err := p.DO.Find()
	return result.([]*do.PropertyValue), err
}

func (p propertyValueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.PropertyValue, err error) {
	buf := make([]*do.PropertyValue, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p propertyValueDo) FindInBatches(result *[]*do.PropertyValue, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p propertyValueDo) Attrs(attrs ...field.AssignExpr) *propertyValueDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p propertyValueDo) Assign(attrs ...field.AssignExpr) *propertyValueDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p propertyValueDo) Joins(fields ...field.RelationField) *propertyValueDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p propertyValueDo) Preload(fields ...field.RelationField) *propertyValueDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p propertyValueDo) FirstOrInit() (*do.PropertyValue, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.PropertyValue), nil
	}
}

func (p propertyValueDo) FirstOrCreate() (*do.PropertyValue, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.PropertyValue), nil
	}
}

func (p propertyValueDo) FindByPage(offset int, limit int) (result []*do.PropertyValue, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p propertyValueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p propertyValueDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p propertyValueDo) Delete(models ...*do.PropertyValue) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *propertyValueDo) withDO(do gen.Dao) *propertyValueDo {
	p.DO = *do.(*gen.DO)
	return p
}
