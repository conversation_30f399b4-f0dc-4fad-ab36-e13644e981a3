// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newProperty(db *gorm.DB, opts ...gen.DOOption) property {
	_property := property{}

	_property.propertyDo.UseDB(db, opts...)
	_property.propertyDo.UseModel(&do.Property{})

	tableName := _property.propertyDo.TableName()
	_property.ALL = field.NewAsterisk(tableName)
	_property.ID = field.NewInt64(tableName, "id")
	_property.CategoryID = field.NewInt64(tableName, "category_id")
	_property.Name = field.NewString(tableName, "name")
	_property.Alias_ = field.NewString(tableName, "alias")
	_property.Status = field.NewInt32(tableName, "status")
	_property.Source = field.NewInt32(tableName, "source")
	_property.GroupKey = field.NewString(tableName, "group_key")
	_property.GroupName = field.NewString(tableName, "group_name")
	_property.UniqueKey = field.NewString(tableName, "unique_key")
	_property.UniqueConstraint = field.NewInt32(tableName, "unique_constraint")
	_property.InputType = field.NewInt32(tableName, "input_type")
	_property.FieldType = field.NewInt32(tableName, "field_type")
	_property.IsRequired = field.NewInt32(tableName, "is_required")
	_property.Type = field.NewInt32(tableName, "type")
	_property.Sequence = field.NewInt32(tableName, "sequence")
	_property.ContainPropertyValues = field.NewInt32(tableName, "contain_property_values")
	_property.CreateTime = field.NewTime(tableName, "create_time")
	_property.UpdateTime = field.NewTime(tableName, "update_time")
	_property.CreateUID = field.NewString(tableName, "create_uid")
	_property.UpdateUID = field.NewString(tableName, "update_uid")
	_property.Feature = field.NewString(tableName, "feature")
	_property.IsDelete = field.NewInt32(tableName, "is_delete")
	_property.IsTest = field.NewInt32(tableName, "is_test")

	_property.fillFieldMap()

	return _property
}

type property struct {
	propertyDo propertyDo

	ALL                   field.Asterisk
	ID                    field.Int64  // 属性项id
	CategoryID            field.Int64  // 类目id
	Name                  field.String // 属性项名
	Alias_                field.String // 属性项别名
	Status                field.Int32  // 属性项状态
	Source                field.Int32  // 来源
	GroupKey              field.String // 属性组key
	GroupName             field.String // 属性组name
	UniqueKey             field.String // 唯一key
	UniqueConstraint      field.Int32  // 唯一性约束 0-不约束 1-全局唯一 2-店铺唯一
	InputType             field.Int32  // 输入类型1单选 2多选, 3文本输入
	FieldType             field.Int32  // 属性值字段类型，0-str，1-int
	IsRequired            field.Int32  // 是否必选 1 必选，0 非必选
	Type                  field.Int32  // 属性类型，0 绑定属性 1关键属性 2售卖属性 3 商品属性
	Sequence              field.Int32  // 排序值
	ContainPropertyValues field.Int32  // 是否包含属性值 true 包含 false 不包含
	CreateTime            field.Time   // 创建时间
	UpdateTime            field.Time   // 修改时间
	CreateUID             field.String // 创建用户id
	UpdateUID             field.String // 编辑用户id
	Feature               field.String // 扩展字段
	IsDelete              field.Int32  // 是否被删除
	IsTest                field.Int32  // 是否是测试

	fieldMap map[string]field.Expr
}

func (p property) Table(newTableName string) *property {
	p.propertyDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p property) As(alias string) *property {
	p.propertyDo.DO = *(p.propertyDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *property) updateTableName(table string) *property {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.CategoryID = field.NewInt64(table, "category_id")
	p.Name = field.NewString(table, "name")
	p.Alias_ = field.NewString(table, "alias")
	p.Status = field.NewInt32(table, "status")
	p.Source = field.NewInt32(table, "source")
	p.GroupKey = field.NewString(table, "group_key")
	p.GroupName = field.NewString(table, "group_name")
	p.UniqueKey = field.NewString(table, "unique_key")
	p.UniqueConstraint = field.NewInt32(table, "unique_constraint")
	p.InputType = field.NewInt32(table, "input_type")
	p.FieldType = field.NewInt32(table, "field_type")
	p.IsRequired = field.NewInt32(table, "is_required")
	p.Type = field.NewInt32(table, "type")
	p.Sequence = field.NewInt32(table, "sequence")
	p.ContainPropertyValues = field.NewInt32(table, "contain_property_values")
	p.CreateTime = field.NewTime(table, "create_time")
	p.UpdateTime = field.NewTime(table, "update_time")
	p.CreateUID = field.NewString(table, "create_uid")
	p.UpdateUID = field.NewString(table, "update_uid")
	p.Feature = field.NewString(table, "feature")
	p.IsDelete = field.NewInt32(table, "is_delete")
	p.IsTest = field.NewInt32(table, "is_test")

	p.fillFieldMap()

	return p
}

func (p *property) WithContext(ctx context.Context) *propertyDo { return p.propertyDo.WithContext(ctx) }

func (p property) TableName() string { return p.propertyDo.TableName() }

func (p property) Alias() string { return p.propertyDo.Alias() }

func (p *property) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *property) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 23)
	p.fieldMap["id"] = p.ID
	p.fieldMap["category_id"] = p.CategoryID
	p.fieldMap["name"] = p.Name
	p.fieldMap["alias"] = p.Alias_
	p.fieldMap["status"] = p.Status
	p.fieldMap["source"] = p.Source
	p.fieldMap["group_key"] = p.GroupKey
	p.fieldMap["group_name"] = p.GroupName
	p.fieldMap["unique_key"] = p.UniqueKey
	p.fieldMap["unique_constraint"] = p.UniqueConstraint
	p.fieldMap["input_type"] = p.InputType
	p.fieldMap["field_type"] = p.FieldType
	p.fieldMap["is_required"] = p.IsRequired
	p.fieldMap["type"] = p.Type
	p.fieldMap["sequence"] = p.Sequence
	p.fieldMap["contain_property_values"] = p.ContainPropertyValues
	p.fieldMap["create_time"] = p.CreateTime
	p.fieldMap["update_time"] = p.UpdateTime
	p.fieldMap["create_uid"] = p.CreateUID
	p.fieldMap["update_uid"] = p.UpdateUID
	p.fieldMap["feature"] = p.Feature
	p.fieldMap["is_delete"] = p.IsDelete
	p.fieldMap["is_test"] = p.IsTest
}

func (p property) clone(db *gorm.DB) property {
	p.propertyDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p property) replaceDB(db *gorm.DB) property {
	p.propertyDo.ReplaceDB(db)
	return p
}

type propertyDo struct{ gen.DO }

func (p propertyDo) Debug() *propertyDo {
	return p.withDO(p.DO.Debug())
}

func (p propertyDo) WithContext(ctx context.Context) *propertyDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p propertyDo) ReadDB() *propertyDo {
	return p.Clauses(dbresolver.Read)
}

func (p propertyDo) WriteDB() *propertyDo {
	return p.Clauses(dbresolver.Write)
}

func (p propertyDo) Session(config *gorm.Session) *propertyDo {
	return p.withDO(p.DO.Session(config))
}

func (p propertyDo) Clauses(conds ...clause.Expression) *propertyDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p propertyDo) Returning(value interface{}, columns ...string) *propertyDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p propertyDo) Not(conds ...gen.Condition) *propertyDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p propertyDo) Or(conds ...gen.Condition) *propertyDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p propertyDo) Select(conds ...field.Expr) *propertyDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p propertyDo) Where(conds ...gen.Condition) *propertyDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p propertyDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *propertyDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p propertyDo) Order(conds ...field.Expr) *propertyDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p propertyDo) Distinct(cols ...field.Expr) *propertyDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p propertyDo) Omit(cols ...field.Expr) *propertyDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p propertyDo) Join(table schema.Tabler, on ...field.Expr) *propertyDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p propertyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *propertyDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p propertyDo) RightJoin(table schema.Tabler, on ...field.Expr) *propertyDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p propertyDo) Group(cols ...field.Expr) *propertyDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p propertyDo) Having(conds ...gen.Condition) *propertyDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p propertyDo) Limit(limit int) *propertyDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p propertyDo) Offset(offset int) *propertyDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p propertyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *propertyDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p propertyDo) Unscoped() *propertyDo {
	return p.withDO(p.DO.Unscoped())
}

func (p propertyDo) Create(values ...*do.Property) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p propertyDo) CreateInBatches(values []*do.Property, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p propertyDo) Save(values ...*do.Property) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p propertyDo) First() (*do.Property, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.Property), nil
	}
}

func (p propertyDo) Take() (*do.Property, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.Property), nil
	}
}

func (p propertyDo) Last() (*do.Property, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.Property), nil
	}
}

func (p propertyDo) Find() ([]*do.Property, error) {
	result, err := p.DO.Find()
	return result.([]*do.Property), err
}

func (p propertyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.Property, err error) {
	buf := make([]*do.Property, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p propertyDo) FindInBatches(result *[]*do.Property, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p propertyDo) Attrs(attrs ...field.AssignExpr) *propertyDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p propertyDo) Assign(attrs ...field.AssignExpr) *propertyDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p propertyDo) Joins(fields ...field.RelationField) *propertyDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p propertyDo) Preload(fields ...field.RelationField) *propertyDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p propertyDo) FirstOrInit() (*do.Property, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.Property), nil
	}
}

func (p propertyDo) FirstOrCreate() (*do.Property, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.Property), nil
	}
}

func (p propertyDo) FindByPage(offset int, limit int) (result []*do.Property, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p propertyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p propertyDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p propertyDo) Delete(models ...*do.Property) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *propertyDo) withDO(do gen.Dao) *propertyDo {
	p.DO = *do.(*gen.DO)
	return p
}
