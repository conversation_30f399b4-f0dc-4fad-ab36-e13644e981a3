// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newProductTenantPropertyRel(db *gorm.DB, opts ...gen.DOOption) productTenantPropertyRel {
	_productTenantPropertyRel := productTenantPropertyRel{}

	_productTenantPropertyRel.productTenantPropertyRelDo.UseDB(db, opts...)
	_productTenantPropertyRel.productTenantPropertyRelDo.UseModel(&do.ProductTenantPropertyRel{})

	tableName := _productTenantPropertyRel.productTenantPropertyRelDo.TableName()
	_productTenantPropertyRel.ALL = field.NewAsterisk(tableName)
	_productTenantPropertyRel.ID = field.NewInt64(tableName, "id")
	_productTenantPropertyRel.TenantType = field.NewInt64(tableName, "tenant_type")
	_productTenantPropertyRel.PropertyKey = field.NewString(tableName, "property_key")
	_productTenantPropertyRel.Status = field.NewInt32(tableName, "status")
	_productTenantPropertyRel.CreateUID = field.NewString(tableName, "create_uid")
	_productTenantPropertyRel.CreateName = field.NewString(tableName, "create_name")
	_productTenantPropertyRel.UpdateUID = field.NewString(tableName, "update_uid")
	_productTenantPropertyRel.UpdateName = field.NewString(tableName, "update_name")
	_productTenantPropertyRel.CreatedTime = field.NewTime(tableName, "created_time")
	_productTenantPropertyRel.UpdatedTime = field.NewTime(tableName, "updated_time")
	_productTenantPropertyRel.DeleteTime = field.NewInt64(tableName, "delete_time")

	_productTenantPropertyRel.fillFieldMap()

	return _productTenantPropertyRel
}

type productTenantPropertyRel struct {
	productTenantPropertyRelDo productTenantPropertyRelDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键ID
	TenantType  field.Int64  // 租户type
	PropertyKey field.String // 属性项key
	Status      field.Int32  // 属性项状态 1启用 2禁用
	CreateUID   field.String // 创建用户id
	CreateName  field.String // 创建用户名
	UpdateUID   field.String // 编辑用户id
	UpdateName  field.String // 编辑用户名
	CreatedTime field.Time   // 创建时间
	UpdatedTime field.Time   // 修改时间
	DeleteTime  field.Int64  // 删除时间

	fieldMap map[string]field.Expr
}

func (p productTenantPropertyRel) Table(newTableName string) *productTenantPropertyRel {
	p.productTenantPropertyRelDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productTenantPropertyRel) As(alias string) *productTenantPropertyRel {
	p.productTenantPropertyRelDo.DO = *(p.productTenantPropertyRelDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productTenantPropertyRel) updateTableName(table string) *productTenantPropertyRel {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.TenantType = field.NewInt64(table, "tenant_type")
	p.PropertyKey = field.NewString(table, "property_key")
	p.Status = field.NewInt32(table, "status")
	p.CreateUID = field.NewString(table, "create_uid")
	p.CreateName = field.NewString(table, "create_name")
	p.UpdateUID = field.NewString(table, "update_uid")
	p.UpdateName = field.NewString(table, "update_name")
	p.CreatedTime = field.NewTime(table, "created_time")
	p.UpdatedTime = field.NewTime(table, "updated_time")
	p.DeleteTime = field.NewInt64(table, "delete_time")

	p.fillFieldMap()

	return p
}

func (p *productTenantPropertyRel) WithContext(ctx context.Context) *productTenantPropertyRelDo {
	return p.productTenantPropertyRelDo.WithContext(ctx)
}

func (p productTenantPropertyRel) TableName() string { return p.productTenantPropertyRelDo.TableName() }

func (p productTenantPropertyRel) Alias() string { return p.productTenantPropertyRelDo.Alias() }

func (p *productTenantPropertyRel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productTenantPropertyRel) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 11)
	p.fieldMap["id"] = p.ID
	p.fieldMap["tenant_type"] = p.TenantType
	p.fieldMap["property_key"] = p.PropertyKey
	p.fieldMap["status"] = p.Status
	p.fieldMap["create_uid"] = p.CreateUID
	p.fieldMap["create_name"] = p.CreateName
	p.fieldMap["update_uid"] = p.UpdateUID
	p.fieldMap["update_name"] = p.UpdateName
	p.fieldMap["created_time"] = p.CreatedTime
	p.fieldMap["updated_time"] = p.UpdatedTime
	p.fieldMap["delete_time"] = p.DeleteTime
}

func (p productTenantPropertyRel) clone(db *gorm.DB) productTenantPropertyRel {
	p.productTenantPropertyRelDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p productTenantPropertyRel) replaceDB(db *gorm.DB) productTenantPropertyRel {
	p.productTenantPropertyRelDo.ReplaceDB(db)
	return p
}

type productTenantPropertyRelDo struct{ gen.DO }

func (p productTenantPropertyRelDo) Debug() *productTenantPropertyRelDo {
	return p.withDO(p.DO.Debug())
}

func (p productTenantPropertyRelDo) WithContext(ctx context.Context) *productTenantPropertyRelDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productTenantPropertyRelDo) ReadDB() *productTenantPropertyRelDo {
	return p.Clauses(dbresolver.Read)
}

func (p productTenantPropertyRelDo) WriteDB() *productTenantPropertyRelDo {
	return p.Clauses(dbresolver.Write)
}

func (p productTenantPropertyRelDo) Session(config *gorm.Session) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Session(config))
}

func (p productTenantPropertyRelDo) Clauses(conds ...clause.Expression) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productTenantPropertyRelDo) Returning(value interface{}, columns ...string) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productTenantPropertyRelDo) Not(conds ...gen.Condition) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productTenantPropertyRelDo) Or(conds ...gen.Condition) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productTenantPropertyRelDo) Select(conds ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productTenantPropertyRelDo) Where(conds ...gen.Condition) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productTenantPropertyRelDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *productTenantPropertyRelDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p productTenantPropertyRelDo) Order(conds ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productTenantPropertyRelDo) Distinct(cols ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productTenantPropertyRelDo) Omit(cols ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productTenantPropertyRelDo) Join(table schema.Tabler, on ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productTenantPropertyRelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productTenantPropertyRelDo) RightJoin(table schema.Tabler, on ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productTenantPropertyRelDo) Group(cols ...field.Expr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productTenantPropertyRelDo) Having(conds ...gen.Condition) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productTenantPropertyRelDo) Limit(limit int) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productTenantPropertyRelDo) Offset(offset int) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productTenantPropertyRelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productTenantPropertyRelDo) Unscoped() *productTenantPropertyRelDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productTenantPropertyRelDo) Create(values ...*do.ProductTenantPropertyRel) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productTenantPropertyRelDo) CreateInBatches(values []*do.ProductTenantPropertyRel, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productTenantPropertyRelDo) Save(values ...*do.ProductTenantPropertyRel) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productTenantPropertyRelDo) First() (*do.ProductTenantPropertyRel, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductTenantPropertyRel), nil
	}
}

func (p productTenantPropertyRelDo) Take() (*do.ProductTenantPropertyRel, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductTenantPropertyRel), nil
	}
}

func (p productTenantPropertyRelDo) Last() (*do.ProductTenantPropertyRel, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductTenantPropertyRel), nil
	}
}

func (p productTenantPropertyRelDo) Find() ([]*do.ProductTenantPropertyRel, error) {
	result, err := p.DO.Find()
	return result.([]*do.ProductTenantPropertyRel), err
}

func (p productTenantPropertyRelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.ProductTenantPropertyRel, err error) {
	buf := make([]*do.ProductTenantPropertyRel, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productTenantPropertyRelDo) FindInBatches(result *[]*do.ProductTenantPropertyRel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productTenantPropertyRelDo) Attrs(attrs ...field.AssignExpr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productTenantPropertyRelDo) Assign(attrs ...field.AssignExpr) *productTenantPropertyRelDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productTenantPropertyRelDo) Joins(fields ...field.RelationField) *productTenantPropertyRelDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productTenantPropertyRelDo) Preload(fields ...field.RelationField) *productTenantPropertyRelDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productTenantPropertyRelDo) FirstOrInit() (*do.ProductTenantPropertyRel, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductTenantPropertyRel), nil
	}
}

func (p productTenantPropertyRelDo) FirstOrCreate() (*do.ProductTenantPropertyRel, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductTenantPropertyRel), nil
	}
}

func (p productTenantPropertyRelDo) FindByPage(offset int, limit int) (result []*do.ProductTenantPropertyRel, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productTenantPropertyRelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productTenantPropertyRelDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productTenantPropertyRelDo) Delete(models ...*do.ProductTenantPropertyRel) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productTenantPropertyRelDo) withDO(do gen.Dao) *productTenantPropertyRelDo {
	p.DO = *do.(*gen.DO)
	return p
}
