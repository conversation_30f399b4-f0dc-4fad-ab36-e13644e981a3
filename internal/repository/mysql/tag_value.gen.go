// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newTagValue(db *gorm.DB, opts ...gen.DOOption) tagValue {
	_tagValue := tagValue{}

	_tagValue.tagValueDo.UseDB(db, opts...)
	_tagValue.tagValueDo.UseModel(&do.TagValue{})

	tableName := _tagValue.tagValueDo.TableName()
	_tagValue.ALL = field.NewAsterisk(tableName)
	_tagValue.ID = field.NewInt64(tableName, "id")
	_tagValue.TagCode = field.NewString(tableName, "tag_code")
	_tagValue.TagValue = field.NewString(tableName, "tag_value")
	_tagValue.TagDesc = field.NewString(tableName, "tag_desc")
	_tagValue.OperatorID = field.NewString(tableName, "operator_id")
	_tagValue.OperatorName = field.NewString(tableName, "operator_name")
	_tagValue.Extra = field.NewString(tableName, "extra")
	_tagValue.CreatedTime = field.NewTime(tableName, "created_time")
	_tagValue.UpdatedTime = field.NewTime(tableName, "updated_time")
	_tagValue.IsDeleted = field.NewInt32(tableName, "is_deleted")
	_tagValue.TagCodeType = field.NewString(tableName, "tag_code_type")

	_tagValue.fillFieldMap()

	return _tagValue
}

type tagValue struct {
	tagValueDo tagValueDo

	ALL          field.Asterisk
	ID           field.Int64  // 主键ID
	TagCode      field.String // 标签code
	TagValue     field.String // 标签值
	TagDesc      field.String // 标签值描述
	OperatorID   field.String // 操作人id
	OperatorName field.String // 操作人名称
	Extra        field.String // 额外信息
	CreatedTime  field.Time   // 创建时间
	UpdatedTime  field.Time   // 更新时间
	IsDeleted    field.Int32  // 是否删除
	TagCodeType  field.String // tag_code + type

	fieldMap map[string]field.Expr
}

func (t tagValue) Table(newTableName string) *tagValue {
	t.tagValueDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tagValue) As(alias string) *tagValue {
	t.tagValueDo.DO = *(t.tagValueDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tagValue) updateTableName(table string) *tagValue {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.TagCode = field.NewString(table, "tag_code")
	t.TagValue = field.NewString(table, "tag_value")
	t.TagDesc = field.NewString(table, "tag_desc")
	t.OperatorID = field.NewString(table, "operator_id")
	t.OperatorName = field.NewString(table, "operator_name")
	t.Extra = field.NewString(table, "extra")
	t.CreatedTime = field.NewTime(table, "created_time")
	t.UpdatedTime = field.NewTime(table, "updated_time")
	t.IsDeleted = field.NewInt32(table, "is_deleted")
	t.TagCodeType = field.NewString(table, "tag_code_type")

	t.fillFieldMap()

	return t
}

func (t *tagValue) WithContext(ctx context.Context) *tagValueDo { return t.tagValueDo.WithContext(ctx) }

func (t tagValue) TableName() string { return t.tagValueDo.TableName() }

func (t tagValue) Alias() string { return t.tagValueDo.Alias() }

func (t *tagValue) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tagValue) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 11)
	t.fieldMap["id"] = t.ID
	t.fieldMap["tag_code"] = t.TagCode
	t.fieldMap["tag_value"] = t.TagValue
	t.fieldMap["tag_desc"] = t.TagDesc
	t.fieldMap["operator_id"] = t.OperatorID
	t.fieldMap["operator_name"] = t.OperatorName
	t.fieldMap["extra"] = t.Extra
	t.fieldMap["created_time"] = t.CreatedTime
	t.fieldMap["updated_time"] = t.UpdatedTime
	t.fieldMap["is_deleted"] = t.IsDeleted
	t.fieldMap["tag_code_type"] = t.TagCodeType
}

func (t tagValue) clone(db *gorm.DB) tagValue {
	t.tagValueDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tagValue) replaceDB(db *gorm.DB) tagValue {
	t.tagValueDo.ReplaceDB(db)
	return t
}

type tagValueDo struct{ gen.DO }

func (t tagValueDo) Debug() *tagValueDo {
	return t.withDO(t.DO.Debug())
}

func (t tagValueDo) WithContext(ctx context.Context) *tagValueDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tagValueDo) ReadDB() *tagValueDo {
	return t.Clauses(dbresolver.Read)
}

func (t tagValueDo) WriteDB() *tagValueDo {
	return t.Clauses(dbresolver.Write)
}

func (t tagValueDo) Session(config *gorm.Session) *tagValueDo {
	return t.withDO(t.DO.Session(config))
}

func (t tagValueDo) Clauses(conds ...clause.Expression) *tagValueDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tagValueDo) Returning(value interface{}, columns ...string) *tagValueDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tagValueDo) Not(conds ...gen.Condition) *tagValueDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tagValueDo) Or(conds ...gen.Condition) *tagValueDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tagValueDo) Select(conds ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tagValueDo) Where(conds ...gen.Condition) *tagValueDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tagValueDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tagValueDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tagValueDo) Order(conds ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tagValueDo) Distinct(cols ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tagValueDo) Omit(cols ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tagValueDo) Join(table schema.Tabler, on ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tagValueDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tagValueDo) RightJoin(table schema.Tabler, on ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tagValueDo) Group(cols ...field.Expr) *tagValueDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tagValueDo) Having(conds ...gen.Condition) *tagValueDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tagValueDo) Limit(limit int) *tagValueDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tagValueDo) Offset(offset int) *tagValueDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tagValueDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tagValueDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tagValueDo) Unscoped() *tagValueDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tagValueDo) Create(values ...*do.TagValue) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tagValueDo) CreateInBatches(values []*do.TagValue, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tagValueDo) Save(values ...*do.TagValue) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tagValueDo) First() (*do.TagValue, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagValue), nil
	}
}

func (t tagValueDo) Take() (*do.TagValue, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagValue), nil
	}
}

func (t tagValueDo) Last() (*do.TagValue, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagValue), nil
	}
}

func (t tagValueDo) Find() ([]*do.TagValue, error) {
	result, err := t.DO.Find()
	return result.([]*do.TagValue), err
}

func (t tagValueDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.TagValue, err error) {
	buf := make([]*do.TagValue, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tagValueDo) FindInBatches(result *[]*do.TagValue, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tagValueDo) Attrs(attrs ...field.AssignExpr) *tagValueDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tagValueDo) Assign(attrs ...field.AssignExpr) *tagValueDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tagValueDo) Joins(fields ...field.RelationField) *tagValueDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tagValueDo) Preload(fields ...field.RelationField) *tagValueDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tagValueDo) FirstOrInit() (*do.TagValue, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagValue), nil
	}
}

func (t tagValueDo) FirstOrCreate() (*do.TagValue, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagValue), nil
	}
}

func (t tagValueDo) FindByPage(offset int, limit int) (result []*do.TagValue, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tagValueDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tagValueDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tagValueDo) Delete(models ...*do.TagValue) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tagValueDo) withDO(do gen.Dao) *tagValueDo {
	t.DO = *do.(*gen.DO)
	return t
}
