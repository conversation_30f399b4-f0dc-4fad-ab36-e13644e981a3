// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newCategory(db *gorm.DB, opts ...gen.DOOption) category {
	_category := category{}

	_category.categoryDo.UseDB(db, opts...)
	_category.categoryDo.UseModel(&do.Category{})

	tableName := _category.categoryDo.TableName()
	_category.ALL = field.NewAsterisk(tableName)
	_category.ID = field.NewInt64(tableName, "id")
	_category.Name = field.NewString(tableName, "name")
	_category.CategoryKey = field.NewString(tableName, "category_key")
	_category.ParentID = field.NewInt64(tableName, "parent_id")
	_category.Level = field.NewInt32(tableName, "level")
	_category.IsLeaf = field.NewInt32(tableName, "is_leaf")
	_category.Sequence = field.NewInt32(tableName, "sequence")
	_category.Description = field.NewString(tableName, "description")
	_category.Status = field.NewInt32(tableName, "status")
	_category.TOuterID = field.NewString(tableName, "t_outer_id")
	_category.AuditStatus = field.NewInt32(tableName, "audit_status")
	_category.CreateTime = field.NewTime(tableName, "create_time")
	_category.UpdateTime = field.NewTime(tableName, "update_time")
	_category.IsDelete = field.NewInt32(tableName, "is_delete")
	_category.IsTest = field.NewInt32(tableName, "is_test")
	_category.Extra = field.NewString(tableName, "extra")
	_category.BizLine = field.NewInt32(tableName, "biz_line")
	_category.CategoryType = field.NewInt32(tableName, "category_type")

	_category.fillFieldMap()

	return _category
}

type category struct {
	categoryDo categoryDo

	ALL          field.Asterisk
	ID           field.Int64  // 类目ID
	Name         field.String // 类目名称
	CategoryKey  field.String
	ParentID     field.Int64  // 上级ID，一级类目上级ID为0
	Level        field.Int32  // 类目层级，最高支持4层
	IsLeaf       field.Int32  // 是否为叶子节点，0非叶子节点，1叶子节点
	Sequence     field.Int32  // 商品类目排序值
	Description  field.String // 类目备注说明
	Status       field.Int32  // 类目状态
	TOuterID     field.String // 外部id
	AuditStatus  field.Int32  // 类目审核状态
	CreateTime   field.Time   // 创建时间
	UpdateTime   field.Time   // 修改时间
	IsDelete     field.Int32  // 是否被删除
	IsTest       field.Int32  // 是否是测试
	Extra        field.String // 额外信息
	BizLine      field.Int32  // 业务线
	CategoryType field.Int32  // 类目类型

	fieldMap map[string]field.Expr
}

func (c category) Table(newTableName string) *category {
	c.categoryDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c category) As(alias string) *category {
	c.categoryDo.DO = *(c.categoryDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *category) updateTableName(table string) *category {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.Name = field.NewString(table, "name")
	c.CategoryKey = field.NewString(table, "category_key")
	c.ParentID = field.NewInt64(table, "parent_id")
	c.Level = field.NewInt32(table, "level")
	c.IsLeaf = field.NewInt32(table, "is_leaf")
	c.Sequence = field.NewInt32(table, "sequence")
	c.Description = field.NewString(table, "description")
	c.Status = field.NewInt32(table, "status")
	c.TOuterID = field.NewString(table, "t_outer_id")
	c.AuditStatus = field.NewInt32(table, "audit_status")
	c.CreateTime = field.NewTime(table, "create_time")
	c.UpdateTime = field.NewTime(table, "update_time")
	c.IsDelete = field.NewInt32(table, "is_delete")
	c.IsTest = field.NewInt32(table, "is_test")
	c.Extra = field.NewString(table, "extra")
	c.BizLine = field.NewInt32(table, "biz_line")
	c.CategoryType = field.NewInt32(table, "category_type")

	c.fillFieldMap()

	return c
}

func (c *category) WithContext(ctx context.Context) *categoryDo { return c.categoryDo.WithContext(ctx) }

func (c category) TableName() string { return c.categoryDo.TableName() }

func (c category) Alias() string { return c.categoryDo.Alias() }

func (c *category) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *category) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 18)
	c.fieldMap["id"] = c.ID
	c.fieldMap["name"] = c.Name
	c.fieldMap["category_key"] = c.CategoryKey
	c.fieldMap["parent_id"] = c.ParentID
	c.fieldMap["level"] = c.Level
	c.fieldMap["is_leaf"] = c.IsLeaf
	c.fieldMap["sequence"] = c.Sequence
	c.fieldMap["description"] = c.Description
	c.fieldMap["status"] = c.Status
	c.fieldMap["t_outer_id"] = c.TOuterID
	c.fieldMap["audit_status"] = c.AuditStatus
	c.fieldMap["create_time"] = c.CreateTime
	c.fieldMap["update_time"] = c.UpdateTime
	c.fieldMap["is_delete"] = c.IsDelete
	c.fieldMap["is_test"] = c.IsTest
	c.fieldMap["extra"] = c.Extra
	c.fieldMap["biz_line"] = c.BizLine
	c.fieldMap["category_type"] = c.CategoryType
}

func (c category) clone(db *gorm.DB) category {
	c.categoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c category) replaceDB(db *gorm.DB) category {
	c.categoryDo.ReplaceDB(db)
	return c
}

type categoryDo struct{ gen.DO }

func (c categoryDo) Debug() *categoryDo {
	return c.withDO(c.DO.Debug())
}

func (c categoryDo) WithContext(ctx context.Context) *categoryDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c categoryDo) ReadDB() *categoryDo {
	return c.Clauses(dbresolver.Read)
}

func (c categoryDo) WriteDB() *categoryDo {
	return c.Clauses(dbresolver.Write)
}

func (c categoryDo) Session(config *gorm.Session) *categoryDo {
	return c.withDO(c.DO.Session(config))
}

func (c categoryDo) Clauses(conds ...clause.Expression) *categoryDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c categoryDo) Returning(value interface{}, columns ...string) *categoryDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c categoryDo) Not(conds ...gen.Condition) *categoryDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c categoryDo) Or(conds ...gen.Condition) *categoryDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c categoryDo) Select(conds ...field.Expr) *categoryDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c categoryDo) Where(conds ...gen.Condition) *categoryDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c categoryDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *categoryDo {
	return c.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (c categoryDo) Order(conds ...field.Expr) *categoryDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c categoryDo) Distinct(cols ...field.Expr) *categoryDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c categoryDo) Omit(cols ...field.Expr) *categoryDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c categoryDo) Join(table schema.Tabler, on ...field.Expr) *categoryDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c categoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *categoryDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c categoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *categoryDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c categoryDo) Group(cols ...field.Expr) *categoryDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c categoryDo) Having(conds ...gen.Condition) *categoryDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c categoryDo) Limit(limit int) *categoryDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c categoryDo) Offset(offset int) *categoryDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c categoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *categoryDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c categoryDo) Unscoped() *categoryDo {
	return c.withDO(c.DO.Unscoped())
}

func (c categoryDo) Create(values ...*do.Category) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c categoryDo) CreateInBatches(values []*do.Category, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c categoryDo) Save(values ...*do.Category) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c categoryDo) First() (*do.Category, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.Category), nil
	}
}

func (c categoryDo) Take() (*do.Category, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.Category), nil
	}
}

func (c categoryDo) Last() (*do.Category, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.Category), nil
	}
}

func (c categoryDo) Find() ([]*do.Category, error) {
	result, err := c.DO.Find()
	return result.([]*do.Category), err
}

func (c categoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.Category, err error) {
	buf := make([]*do.Category, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c categoryDo) FindInBatches(result *[]*do.Category, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c categoryDo) Attrs(attrs ...field.AssignExpr) *categoryDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c categoryDo) Assign(attrs ...field.AssignExpr) *categoryDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c categoryDo) Joins(fields ...field.RelationField) *categoryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c categoryDo) Preload(fields ...field.RelationField) *categoryDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c categoryDo) FirstOrInit() (*do.Category, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.Category), nil
	}
}

func (c categoryDo) FirstOrCreate() (*do.Category, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.Category), nil
	}
}

func (c categoryDo) FindByPage(offset int, limit int) (result []*do.Category, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c categoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c categoryDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c categoryDo) Delete(models ...*do.Category) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *categoryDo) withDO(do gen.Dao) *categoryDo {
	c.DO = *do.(*gen.DO)
	return c
}
