// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                          = new(Query)
	Category                   *category
	ProductCategoryPropertyRel *productCategoryPropertyRel
	ProductProperty            *productProperty
	ProductTenantPropertyRel   *productTenantPropertyRel
	Property                   *property
	PropertyValue              *propertyValue
	TagMeta                    *tagMeta
	TagValue                   *tagValue
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Category = &Q.Category
	ProductCategoryPropertyRel = &Q.ProductCategoryPropertyRel
	ProductProperty = &Q.ProductProperty
	ProductTenantPropertyRel = &Q.ProductTenantPropertyRel
	Property = &Q.Property
	PropertyValue = &Q.PropertyValue
	TagMeta = &Q.TagMeta
	TagValue = &Q.TagValue
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                         db,
		Category:                   newCategory(db, opts...),
		ProductCategoryPropertyRel: newProductCategoryPropertyRel(db, opts...),
		ProductProperty:            newProductProperty(db, opts...),
		ProductTenantPropertyRel:   newProductTenantPropertyRel(db, opts...),
		Property:                   newProperty(db, opts...),
		PropertyValue:              newPropertyValue(db, opts...),
		TagMeta:                    newTagMeta(db, opts...),
		TagValue:                   newTagValue(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Category                   category
	ProductCategoryPropertyRel productCategoryPropertyRel
	ProductProperty            productProperty
	ProductTenantPropertyRel   productTenantPropertyRel
	Property                   property
	PropertyValue              propertyValue
	TagMeta                    tagMeta
	TagValue                   tagValue
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                         db,
		Category:                   q.Category.clone(db),
		ProductCategoryPropertyRel: q.ProductCategoryPropertyRel.clone(db),
		ProductProperty:            q.ProductProperty.clone(db),
		ProductTenantPropertyRel:   q.ProductTenantPropertyRel.clone(db),
		Property:                   q.Property.clone(db),
		PropertyValue:              q.PropertyValue.clone(db),
		TagMeta:                    q.TagMeta.clone(db),
		TagValue:                   q.TagValue.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                         db,
		Category:                   q.Category.replaceDB(db),
		ProductCategoryPropertyRel: q.ProductCategoryPropertyRel.replaceDB(db),
		ProductProperty:            q.ProductProperty.replaceDB(db),
		ProductTenantPropertyRel:   q.ProductTenantPropertyRel.replaceDB(db),
		Property:                   q.Property.replaceDB(db),
		PropertyValue:              q.PropertyValue.replaceDB(db),
		TagMeta:                    q.TagMeta.replaceDB(db),
		TagValue:                   q.TagValue.replaceDB(db),
	}
}

type queryCtx struct {
	Category                   *categoryDo
	ProductCategoryPropertyRel *productCategoryPropertyRelDo
	ProductProperty            *productPropertyDo
	ProductTenantPropertyRel   *productTenantPropertyRelDo
	Property                   *propertyDo
	PropertyValue              *propertyValueDo
	TagMeta                    *tagMetaDo
	TagValue                   *tagValueDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Category:                   q.Category.WithContext(ctx),
		ProductCategoryPropertyRel: q.ProductCategoryPropertyRel.WithContext(ctx),
		ProductProperty:            q.ProductProperty.WithContext(ctx),
		ProductTenantPropertyRel:   q.ProductTenantPropertyRel.WithContext(ctx),
		Property:                   q.Property.WithContext(ctx),
		PropertyValue:              q.PropertyValue.WithContext(ctx),
		TagMeta:                    q.TagMeta.WithContext(ctx),
		TagValue:                   q.TagValue.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	return &QueryTx{q.clone(q.db.Begin(opts...))}
}

type QueryTx struct{ *Query }

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
