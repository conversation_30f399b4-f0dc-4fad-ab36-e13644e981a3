// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newTagMeta(db *gorm.DB, opts ...gen.DOOption) tagMeta {
	_tagMeta := tagMeta{}

	_tagMeta.tagMetaDo.UseDB(db, opts...)
	_tagMeta.tagMetaDo.UseModel(&do.TagMeta{})

	tableName := _tagMeta.tagMetaDo.TableName()
	_tagMeta.ALL = field.NewAsterisk(tableName)
	_tagMeta.ID = field.NewInt64(tableName, "id")
	_tagMeta.TagCode = field.NewString(tableName, "tag_code")
	_tagMeta.Name = field.NewString(tableName, "name")
	_tagMeta.Type = field.NewInt32(tableName, "type")
	_tagMeta.Source = field.NewInt32(tableName, "source")
	_tagMeta.Status = field.NewInt32(tableName, "status")
	_tagMeta.TagDesc = field.NewString(tableName, "tag_desc")
	_tagMeta.OperatorID = field.NewString(tableName, "operator_id")
	_tagMeta.OperatorName = field.NewString(tableName, "operator_name")
	_tagMeta.Extra = field.NewString(tableName, "extra")
	_tagMeta.CreatedTime = field.NewTime(tableName, "created_time")
	_tagMeta.UpdatedTime = field.NewTime(tableName, "updated_time")
	_tagMeta.IsDeleted = field.NewInt32(tableName, "is_deleted")
	_tagMeta.TagCodeType = field.NewString(tableName, "tag_code_type")

	_tagMeta.fillFieldMap()

	return _tagMeta
}

type tagMeta struct {
	tagMetaDo tagMetaDo

	ALL          field.Asterisk
	ID           field.Int64  // 主键ID
	TagCode      field.String // 标签code
	Name         field.String // 标签名称
	Type         field.Int32  // 标签类型
	Source       field.Int32  // 标签来源
	Status       field.Int32  // 标签状态
	TagDesc      field.String // 标签描述
	OperatorID   field.String // 操作人id
	OperatorName field.String // 操作人名称
	Extra        field.String // 额外信息
	CreatedTime  field.Time   // 创建时间
	UpdatedTime  field.Time   // 更新时间
	IsDeleted    field.Int32  // 是否删除
	TagCodeType  field.String // tag_code + type

	fieldMap map[string]field.Expr
}

func (t tagMeta) Table(newTableName string) *tagMeta {
	t.tagMetaDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tagMeta) As(alias string) *tagMeta {
	t.tagMetaDo.DO = *(t.tagMetaDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tagMeta) updateTableName(table string) *tagMeta {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.TagCode = field.NewString(table, "tag_code")
	t.Name = field.NewString(table, "name")
	t.Type = field.NewInt32(table, "type")
	t.Source = field.NewInt32(table, "source")
	t.Status = field.NewInt32(table, "status")
	t.TagDesc = field.NewString(table, "tag_desc")
	t.OperatorID = field.NewString(table, "operator_id")
	t.OperatorName = field.NewString(table, "operator_name")
	t.Extra = field.NewString(table, "extra")
	t.CreatedTime = field.NewTime(table, "created_time")
	t.UpdatedTime = field.NewTime(table, "updated_time")
	t.IsDeleted = field.NewInt32(table, "is_deleted")
	t.TagCodeType = field.NewString(table, "tag_code_type")

	t.fillFieldMap()

	return t
}

func (t *tagMeta) WithContext(ctx context.Context) *tagMetaDo { return t.tagMetaDo.WithContext(ctx) }

func (t tagMeta) TableName() string { return t.tagMetaDo.TableName() }

func (t tagMeta) Alias() string { return t.tagMetaDo.Alias() }

func (t *tagMeta) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tagMeta) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 14)
	t.fieldMap["id"] = t.ID
	t.fieldMap["tag_code"] = t.TagCode
	t.fieldMap["name"] = t.Name
	t.fieldMap["type"] = t.Type
	t.fieldMap["source"] = t.Source
	t.fieldMap["status"] = t.Status
	t.fieldMap["tag_desc"] = t.TagDesc
	t.fieldMap["operator_id"] = t.OperatorID
	t.fieldMap["operator_name"] = t.OperatorName
	t.fieldMap["extra"] = t.Extra
	t.fieldMap["created_time"] = t.CreatedTime
	t.fieldMap["updated_time"] = t.UpdatedTime
	t.fieldMap["is_deleted"] = t.IsDeleted
	t.fieldMap["tag_code_type"] = t.TagCodeType
}

func (t tagMeta) clone(db *gorm.DB) tagMeta {
	t.tagMetaDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tagMeta) replaceDB(db *gorm.DB) tagMeta {
	t.tagMetaDo.ReplaceDB(db)
	return t
}

type tagMetaDo struct{ gen.DO }

func (t tagMetaDo) Debug() *tagMetaDo {
	return t.withDO(t.DO.Debug())
}

func (t tagMetaDo) WithContext(ctx context.Context) *tagMetaDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tagMetaDo) ReadDB() *tagMetaDo {
	return t.Clauses(dbresolver.Read)
}

func (t tagMetaDo) WriteDB() *tagMetaDo {
	return t.Clauses(dbresolver.Write)
}

func (t tagMetaDo) Session(config *gorm.Session) *tagMetaDo {
	return t.withDO(t.DO.Session(config))
}

func (t tagMetaDo) Clauses(conds ...clause.Expression) *tagMetaDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tagMetaDo) Returning(value interface{}, columns ...string) *tagMetaDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tagMetaDo) Not(conds ...gen.Condition) *tagMetaDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tagMetaDo) Or(conds ...gen.Condition) *tagMetaDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tagMetaDo) Select(conds ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tagMetaDo) Where(conds ...gen.Condition) *tagMetaDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tagMetaDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *tagMetaDo {
	return t.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (t tagMetaDo) Order(conds ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tagMetaDo) Distinct(cols ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tagMetaDo) Omit(cols ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tagMetaDo) Join(table schema.Tabler, on ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tagMetaDo) LeftJoin(table schema.Tabler, on ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tagMetaDo) RightJoin(table schema.Tabler, on ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tagMetaDo) Group(cols ...field.Expr) *tagMetaDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tagMetaDo) Having(conds ...gen.Condition) *tagMetaDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tagMetaDo) Limit(limit int) *tagMetaDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tagMetaDo) Offset(offset int) *tagMetaDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tagMetaDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *tagMetaDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tagMetaDo) Unscoped() *tagMetaDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tagMetaDo) Create(values ...*do.TagMeta) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tagMetaDo) CreateInBatches(values []*do.TagMeta, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tagMetaDo) Save(values ...*do.TagMeta) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tagMetaDo) First() (*do.TagMeta, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagMeta), nil
	}
}

func (t tagMetaDo) Take() (*do.TagMeta, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagMeta), nil
	}
}

func (t tagMetaDo) Last() (*do.TagMeta, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagMeta), nil
	}
}

func (t tagMetaDo) Find() ([]*do.TagMeta, error) {
	result, err := t.DO.Find()
	return result.([]*do.TagMeta), err
}

func (t tagMetaDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.TagMeta, err error) {
	buf := make([]*do.TagMeta, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tagMetaDo) FindInBatches(result *[]*do.TagMeta, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tagMetaDo) Attrs(attrs ...field.AssignExpr) *tagMetaDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tagMetaDo) Assign(attrs ...field.AssignExpr) *tagMetaDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tagMetaDo) Joins(fields ...field.RelationField) *tagMetaDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tagMetaDo) Preload(fields ...field.RelationField) *tagMetaDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tagMetaDo) FirstOrInit() (*do.TagMeta, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagMeta), nil
	}
}

func (t tagMetaDo) FirstOrCreate() (*do.TagMeta, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.TagMeta), nil
	}
}

func (t tagMetaDo) FindByPage(offset int, limit int) (result []*do.TagMeta, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tagMetaDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tagMetaDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tagMetaDo) Delete(models ...*do.TagMeta) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tagMetaDo) withDO(do gen.Dao) *tagMetaDo {
	t.DO = *do.(*gen.DO)
	return t
}
