// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newProductCategoryPropertyRel(db *gorm.DB, opts ...gen.DOOption) productCategoryPropertyRel {
	_productCategoryPropertyRel := productCategoryPropertyRel{}

	_productCategoryPropertyRel.productCategoryPropertyRelDo.UseDB(db, opts...)
	_productCategoryPropertyRel.productCategoryPropertyRelDo.UseModel(&do.ProductCategoryPropertyRel{})

	tableName := _productCategoryPropertyRel.productCategoryPropertyRelDo.TableName()
	_productCategoryPropertyRel.ALL = field.NewAsterisk(tableName)
	_productCategoryPropertyRel.ID = field.NewInt64(tableName, "id")
	_productCategoryPropertyRel.CategoryID = field.NewInt64(tableName, "category_id")
	_productCategoryPropertyRel.PropertyKey = field.NewString(tableName, "property_key")
	_productCategoryPropertyRel.Status = field.NewInt32(tableName, "status")
	_productCategoryPropertyRel.CreateUID = field.NewString(tableName, "create_uid")
	_productCategoryPropertyRel.CreateName = field.NewString(tableName, "create_name")
	_productCategoryPropertyRel.UpdateUID = field.NewString(tableName, "update_uid")
	_productCategoryPropertyRel.UpdateName = field.NewString(tableName, "update_name")
	_productCategoryPropertyRel.CreatedTime = field.NewTime(tableName, "created_time")
	_productCategoryPropertyRel.UpdatedTime = field.NewTime(tableName, "updated_time")
	_productCategoryPropertyRel.DeleteTime = field.NewInt64(tableName, "delete_time")

	_productCategoryPropertyRel.fillFieldMap()

	return _productCategoryPropertyRel
}

type productCategoryPropertyRel struct {
	productCategoryPropertyRelDo productCategoryPropertyRelDo

	ALL         field.Asterisk
	ID          field.Int64  // 主键ID
	CategoryID  field.Int64  // 类目id
	PropertyKey field.String // 属性项key
	Status      field.Int32  // 属性项状态 1启用 2禁用
	CreateUID   field.String // 创建用户id
	CreateName  field.String // 创建用户名
	UpdateUID   field.String // 编辑用户id
	UpdateName  field.String // 编辑用户名
	CreatedTime field.Time   // 创建时间
	UpdatedTime field.Time   // 修改时间
	DeleteTime  field.Int64  // 删除时间

	fieldMap map[string]field.Expr
}

func (p productCategoryPropertyRel) Table(newTableName string) *productCategoryPropertyRel {
	p.productCategoryPropertyRelDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productCategoryPropertyRel) As(alias string) *productCategoryPropertyRel {
	p.productCategoryPropertyRelDo.DO = *(p.productCategoryPropertyRelDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productCategoryPropertyRel) updateTableName(table string) *productCategoryPropertyRel {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.CategoryID = field.NewInt64(table, "category_id")
	p.PropertyKey = field.NewString(table, "property_key")
	p.Status = field.NewInt32(table, "status")
	p.CreateUID = field.NewString(table, "create_uid")
	p.CreateName = field.NewString(table, "create_name")
	p.UpdateUID = field.NewString(table, "update_uid")
	p.UpdateName = field.NewString(table, "update_name")
	p.CreatedTime = field.NewTime(table, "created_time")
	p.UpdatedTime = field.NewTime(table, "updated_time")
	p.DeleteTime = field.NewInt64(table, "delete_time")

	p.fillFieldMap()

	return p
}

func (p *productCategoryPropertyRel) WithContext(ctx context.Context) *productCategoryPropertyRelDo {
	return p.productCategoryPropertyRelDo.WithContext(ctx)
}

func (p productCategoryPropertyRel) TableName() string {
	return p.productCategoryPropertyRelDo.TableName()
}

func (p productCategoryPropertyRel) Alias() string { return p.productCategoryPropertyRelDo.Alias() }

func (p *productCategoryPropertyRel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productCategoryPropertyRel) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 11)
	p.fieldMap["id"] = p.ID
	p.fieldMap["category_id"] = p.CategoryID
	p.fieldMap["property_key"] = p.PropertyKey
	p.fieldMap["status"] = p.Status
	p.fieldMap["create_uid"] = p.CreateUID
	p.fieldMap["create_name"] = p.CreateName
	p.fieldMap["update_uid"] = p.UpdateUID
	p.fieldMap["update_name"] = p.UpdateName
	p.fieldMap["created_time"] = p.CreatedTime
	p.fieldMap["updated_time"] = p.UpdatedTime
	p.fieldMap["delete_time"] = p.DeleteTime
}

func (p productCategoryPropertyRel) clone(db *gorm.DB) productCategoryPropertyRel {
	p.productCategoryPropertyRelDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p productCategoryPropertyRel) replaceDB(db *gorm.DB) productCategoryPropertyRel {
	p.productCategoryPropertyRelDo.ReplaceDB(db)
	return p
}

type productCategoryPropertyRelDo struct{ gen.DO }

func (p productCategoryPropertyRelDo) Debug() *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Debug())
}

func (p productCategoryPropertyRelDo) WithContext(ctx context.Context) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productCategoryPropertyRelDo) ReadDB() *productCategoryPropertyRelDo {
	return p.Clauses(dbresolver.Read)
}

func (p productCategoryPropertyRelDo) WriteDB() *productCategoryPropertyRelDo {
	return p.Clauses(dbresolver.Write)
}

func (p productCategoryPropertyRelDo) Session(config *gorm.Session) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Session(config))
}

func (p productCategoryPropertyRelDo) Clauses(conds ...clause.Expression) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productCategoryPropertyRelDo) Returning(value interface{}, columns ...string) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productCategoryPropertyRelDo) Not(conds ...gen.Condition) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productCategoryPropertyRelDo) Or(conds ...gen.Condition) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productCategoryPropertyRelDo) Select(conds ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productCategoryPropertyRelDo) Where(conds ...gen.Condition) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productCategoryPropertyRelDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *productCategoryPropertyRelDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p productCategoryPropertyRelDo) Order(conds ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productCategoryPropertyRelDo) Distinct(cols ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productCategoryPropertyRelDo) Omit(cols ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productCategoryPropertyRelDo) Join(table schema.Tabler, on ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productCategoryPropertyRelDo) LeftJoin(table schema.Tabler, on ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productCategoryPropertyRelDo) RightJoin(table schema.Tabler, on ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productCategoryPropertyRelDo) Group(cols ...field.Expr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productCategoryPropertyRelDo) Having(conds ...gen.Condition) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productCategoryPropertyRelDo) Limit(limit int) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productCategoryPropertyRelDo) Offset(offset int) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productCategoryPropertyRelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productCategoryPropertyRelDo) Unscoped() *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productCategoryPropertyRelDo) Create(values ...*do.ProductCategoryPropertyRel) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productCategoryPropertyRelDo) CreateInBatches(values []*do.ProductCategoryPropertyRel, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productCategoryPropertyRelDo) Save(values ...*do.ProductCategoryPropertyRel) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productCategoryPropertyRelDo) First() (*do.ProductCategoryPropertyRel, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductCategoryPropertyRel), nil
	}
}

func (p productCategoryPropertyRelDo) Take() (*do.ProductCategoryPropertyRel, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductCategoryPropertyRel), nil
	}
}

func (p productCategoryPropertyRelDo) Last() (*do.ProductCategoryPropertyRel, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductCategoryPropertyRel), nil
	}
}

func (p productCategoryPropertyRelDo) Find() ([]*do.ProductCategoryPropertyRel, error) {
	result, err := p.DO.Find()
	return result.([]*do.ProductCategoryPropertyRel), err
}

func (p productCategoryPropertyRelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.ProductCategoryPropertyRel, err error) {
	buf := make([]*do.ProductCategoryPropertyRel, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productCategoryPropertyRelDo) FindInBatches(result *[]*do.ProductCategoryPropertyRel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productCategoryPropertyRelDo) Attrs(attrs ...field.AssignExpr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productCategoryPropertyRelDo) Assign(attrs ...field.AssignExpr) *productCategoryPropertyRelDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productCategoryPropertyRelDo) Joins(fields ...field.RelationField) *productCategoryPropertyRelDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productCategoryPropertyRelDo) Preload(fields ...field.RelationField) *productCategoryPropertyRelDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productCategoryPropertyRelDo) FirstOrInit() (*do.ProductCategoryPropertyRel, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductCategoryPropertyRel), nil
	}
}

func (p productCategoryPropertyRelDo) FirstOrCreate() (*do.ProductCategoryPropertyRel, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductCategoryPropertyRel), nil
	}
}

func (p productCategoryPropertyRelDo) FindByPage(offset int, limit int) (result []*do.ProductCategoryPropertyRel, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productCategoryPropertyRelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productCategoryPropertyRelDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productCategoryPropertyRelDo) Delete(models ...*do.ProductCategoryPropertyRel) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productCategoryPropertyRelDo) withDO(do gen.Dao) *productCategoryPropertyRelDo {
	p.DO = *do.(*gen.DO)
	return p
}
