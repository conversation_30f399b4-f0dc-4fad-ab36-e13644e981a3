// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package mysql

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/motor/fwe_category/internal/model/do"
)

func newProductProperty(db *gorm.DB, opts ...gen.DOOption) productProperty {
	_productProperty := productProperty{}

	_productProperty.productPropertyDo.UseDB(db, opts...)
	_productProperty.productPropertyDo.UseModel(&do.ProductProperty{})

	tableName := _productProperty.productPropertyDo.TableName()
	_productProperty.ALL = field.NewAsterisk(tableName)
	_productProperty.ID = field.NewInt64(tableName, "id")
	_productProperty.PropertyKey = field.NewString(tableName, "property_key")
	_productProperty.PropertyType = field.NewInt32(tableName, "property_type")
	_productProperty.FieldType = field.NewString(tableName, "field_type")
	_productProperty.ClassType = field.NewString(tableName, "class_type")
	_productProperty.Name = field.NewString(tableName, "name")
	_productProperty.Description = field.NewString(tableName, "description")
	_productProperty.Status = field.NewInt32(tableName, "status")
	_productProperty.CreateUID = field.NewString(tableName, "create_uid")
	_productProperty.CreateName = field.NewString(tableName, "create_name")
	_productProperty.UpdateUID = field.NewString(tableName, "update_uid")
	_productProperty.UpdateName = field.NewString(tableName, "update_name")
	_productProperty.CreateTime = field.NewTime(tableName, "create_time")
	_productProperty.UpdateTime = field.NewTime(tableName, "update_time")
	_productProperty.DeleteTime = field.NewInt64(tableName, "delete_time")
	_productProperty.ValueDesc = field.NewString(tableName, "value_desc")
	_productProperty.PropertyConfig = field.NewString(tableName, "property_config")

	_productProperty.fillFieldMap()

	return _productProperty
}

type productProperty struct {
	productPropertyDo productPropertyDo

	ALL            field.Asterisk
	ID             field.Int64  // id
	PropertyKey    field.String // 属性唯一key
	PropertyType   field.Int32  // 属性类型0商品属性1附着属性
	FieldType      field.String // 属性值字段类型
	ClassType      field.String // 字段分类
	Name           field.String // 属性名
	Description    field.String // 属性描述
	Status         field.Int32  // 属性状态
	CreateUID      field.String // 创建用户id
	CreateName     field.String // 创建用户名
	UpdateUID      field.String // 编辑用户id
	UpdateName     field.String // 编辑用户名
	CreateTime     field.Time   // 创建时间
	UpdateTime     field.Time   // 修改时间
	DeleteTime     field.Int64  // 删除时间
	ValueDesc      field.String // 属性值描述
	PropertyConfig field.String // 属性配置项

	fieldMap map[string]field.Expr
}

func (p productProperty) Table(newTableName string) *productProperty {
	p.productPropertyDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productProperty) As(alias string) *productProperty {
	p.productPropertyDo.DO = *(p.productPropertyDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productProperty) updateTableName(table string) *productProperty {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.PropertyKey = field.NewString(table, "property_key")
	p.PropertyType = field.NewInt32(table, "property_type")
	p.FieldType = field.NewString(table, "field_type")
	p.ClassType = field.NewString(table, "class_type")
	p.Name = field.NewString(table, "name")
	p.Description = field.NewString(table, "description")
	p.Status = field.NewInt32(table, "status")
	p.CreateUID = field.NewString(table, "create_uid")
	p.CreateName = field.NewString(table, "create_name")
	p.UpdateUID = field.NewString(table, "update_uid")
	p.UpdateName = field.NewString(table, "update_name")
	p.CreateTime = field.NewTime(table, "create_time")
	p.UpdateTime = field.NewTime(table, "update_time")
	p.DeleteTime = field.NewInt64(table, "delete_time")
	p.ValueDesc = field.NewString(table, "value_desc")
	p.PropertyConfig = field.NewString(table, "property_config")

	p.fillFieldMap()

	return p
}

func (p *productProperty) WithContext(ctx context.Context) *productPropertyDo {
	return p.productPropertyDo.WithContext(ctx)
}

func (p productProperty) TableName() string { return p.productPropertyDo.TableName() }

func (p productProperty) Alias() string { return p.productPropertyDo.Alias() }

func (p *productProperty) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productProperty) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 17)
	p.fieldMap["id"] = p.ID
	p.fieldMap["property_key"] = p.PropertyKey
	p.fieldMap["property_type"] = p.PropertyType
	p.fieldMap["field_type"] = p.FieldType
	p.fieldMap["class_type"] = p.ClassType
	p.fieldMap["name"] = p.Name
	p.fieldMap["description"] = p.Description
	p.fieldMap["status"] = p.Status
	p.fieldMap["create_uid"] = p.CreateUID
	p.fieldMap["create_name"] = p.CreateName
	p.fieldMap["update_uid"] = p.UpdateUID
	p.fieldMap["update_name"] = p.UpdateName
	p.fieldMap["create_time"] = p.CreateTime
	p.fieldMap["update_time"] = p.UpdateTime
	p.fieldMap["delete_time"] = p.DeleteTime
	p.fieldMap["value_desc"] = p.ValueDesc
	p.fieldMap["property_config"] = p.PropertyConfig
}

func (p productProperty) clone(db *gorm.DB) productProperty {
	p.productPropertyDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p productProperty) replaceDB(db *gorm.DB) productProperty {
	p.productPropertyDo.ReplaceDB(db)
	return p
}

type productPropertyDo struct{ gen.DO }

func (p productPropertyDo) Debug() *productPropertyDo {
	return p.withDO(p.DO.Debug())
}

func (p productPropertyDo) WithContext(ctx context.Context) *productPropertyDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productPropertyDo) ReadDB() *productPropertyDo {
	return p.Clauses(dbresolver.Read)
}

func (p productPropertyDo) WriteDB() *productPropertyDo {
	return p.Clauses(dbresolver.Write)
}

func (p productPropertyDo) Session(config *gorm.Session) *productPropertyDo {
	return p.withDO(p.DO.Session(config))
}

func (p productPropertyDo) Clauses(conds ...clause.Expression) *productPropertyDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productPropertyDo) Returning(value interface{}, columns ...string) *productPropertyDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productPropertyDo) Not(conds ...gen.Condition) *productPropertyDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productPropertyDo) Or(conds ...gen.Condition) *productPropertyDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productPropertyDo) Select(conds ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productPropertyDo) Where(conds ...gen.Condition) *productPropertyDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productPropertyDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *productPropertyDo {
	return p.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (p productPropertyDo) Order(conds ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productPropertyDo) Distinct(cols ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productPropertyDo) Omit(cols ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productPropertyDo) Join(table schema.Tabler, on ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productPropertyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productPropertyDo) RightJoin(table schema.Tabler, on ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productPropertyDo) Group(cols ...field.Expr) *productPropertyDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productPropertyDo) Having(conds ...gen.Condition) *productPropertyDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productPropertyDo) Limit(limit int) *productPropertyDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productPropertyDo) Offset(offset int) *productPropertyDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productPropertyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *productPropertyDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productPropertyDo) Unscoped() *productPropertyDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productPropertyDo) Create(values ...*do.ProductProperty) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productPropertyDo) CreateInBatches(values []*do.ProductProperty, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productPropertyDo) Save(values ...*do.ProductProperty) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productPropertyDo) First() (*do.ProductProperty, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductProperty), nil
	}
}

func (p productPropertyDo) Take() (*do.ProductProperty, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductProperty), nil
	}
}

func (p productPropertyDo) Last() (*do.ProductProperty, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductProperty), nil
	}
}

func (p productPropertyDo) Find() ([]*do.ProductProperty, error) {
	result, err := p.DO.Find()
	return result.([]*do.ProductProperty), err
}

func (p productPropertyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*do.ProductProperty, err error) {
	buf := make([]*do.ProductProperty, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productPropertyDo) FindInBatches(result *[]*do.ProductProperty, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productPropertyDo) Attrs(attrs ...field.AssignExpr) *productPropertyDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productPropertyDo) Assign(attrs ...field.AssignExpr) *productPropertyDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productPropertyDo) Joins(fields ...field.RelationField) *productPropertyDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productPropertyDo) Preload(fields ...field.RelationField) *productPropertyDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productPropertyDo) FirstOrInit() (*do.ProductProperty, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductProperty), nil
	}
}

func (p productPropertyDo) FirstOrCreate() (*do.ProductProperty, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*do.ProductProperty), nil
	}
}

func (p productPropertyDo) FindByPage(offset int, limit int) (result []*do.ProductProperty, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productPropertyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productPropertyDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productPropertyDo) Delete(models ...*do.ProductProperty) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productPropertyDo) withDO(do gen.Dao) *productPropertyDo {
	p.DO = *do.(*gen.DO)
	return p
}
