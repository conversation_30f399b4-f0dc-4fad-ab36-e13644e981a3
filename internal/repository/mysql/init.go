package mysql

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gorm/bytedgorm"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

var (
	fweEcomDB *gorm.DB
	dbName    = "motor_fwe_ecom"
	psm       = "toutiao.mysql.motor_fwe_ecom"
)

func init() {
	// init
	DB, err := gorm.Open(
		bytedgorm.MySQL(psm /*数据库PSM*/, dbName /*数据库名*/).WithReadReplicas(),
		bytedgorm.WithDefaults(),
		bytedgorm.WithSingularTable(),
	)

	// check err
	if err != nil {
		panic(err)
	}

	fweEcomDB = DB
	if env.IsBoe() {
		fweEcomDB = fweEcomDB.Debug()
	}

	logs.Info("init mysql for %s success", psm)

	SetDefault(fweEcomDB)
}

// WriteFweEcomDB  ...
func WriteFweEcomDB() *gorm.DB {
	return fweEcomDB.Clauses(dbresolver.Write)
}

// ReadFweEcomDB ...
func ReadFweEcomDB() *gorm.DB {
	return fweEcomDB.Clauses(dbresolver.Read)
}

// FweEcomDB Read write separation
func FweEcomDB() *gorm.DB {
	return fweEcomDB
}
