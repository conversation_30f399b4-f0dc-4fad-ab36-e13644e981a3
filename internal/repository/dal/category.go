package dal

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/model/biz"
	model "code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"strings"
)

type motorCategory struct{}

var Category = new(motorCategory)

type CategoryQueryParam struct {
	SelectColumns        []field.Expr
	CategoryIDList       []int64
	BizLineList          []int32
	ParentCategoryIdList []int64
	NameKeyword          *string
	Level                *int64
	Limit                int
	Offset               int
	NeedTotal            bool
}

func (s *motorCategory) Query(ctx context.Context, db *gorm.DB, param *CategoryQueryParam) (dataList []*model.Category, total int64, bizErr *errdef.BizErr) {
	var (
		q     = GetQuery(db).Category
		condS []gen.Condition
		err   error
	)
	condS = append(condS, q.IsDelete.Eq(0))
	if param.NameKeyword != nil {
		condS = append(condS, q.Name.Like("%"+*param.NameKeyword+"%"))
	}
	if param.Level != nil {
		condS = append(condS, q.Level.Eq(int32(*param.Level)))
	}
	if len(param.BizLineList) > 0 {
		condS = append(condS, q.BizLine.In(param.BizLineList...))
	}
	if len(param.ParentCategoryIdList) > 0 {
		condS = append(condS, q.ParentID.In(param.ParentCategoryIdList...))
	}
	if len(param.CategoryIDList) > 0 {
		condS = append(condS, q.ID.In(param.CategoryIDList...))
	}

	if param.NeedTotal {
		total, err = q.WithContext(ctx).Where(condS...).Count()
		if err != nil {
			bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
			logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
			return
		}
	}
	dataList, err = q.WithContext(ctx).
		Select(param.SelectColumns...).
		Where(condS...).
		Order(q.ID.Desc()).Limit(param.Limit).Offset(param.Offset).Find()
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
		return
	}
	return
}

func MGetCategory(ctx context.Context, categoryIds []int64, categoryKeys []string) (categories []*model.Category, err error) {
	catRead := FweEcomReadQuery.Category
	query := catRead.WithContext(ctx).Where(catRead.IsDelete.Eq(0))
	idQuery := query.Where(catRead.ID.In(categoryIds...))
	if len(categoryKeys) > 0 {
		idQuery.Or(catRead.CategoryKey.In(categoryKeys...))
	}

	categories, err = query.Where(idQuery).Find()
	if err != nil {
		return nil, err
	}

	return categories, err
}

func MGetCategoryWithIsTest(ctx context.Context, categoryIds []int64, categoryKeys []string, isTest *int32) (categories []*model.Category, err error) {
	catRead := FweEcomReadQuery.Category
	query := catRead.WithContext(ctx).Where(catRead.IsDelete.Eq(0))
	if isTest != nil {
		query = query.Where(catRead.IsTest.Eq(*isTest))
	}
	idQuery := query.Where(catRead.ID.In(categoryIds...))
	if len(categoryKeys) > 0 {
		idQuery.Or(catRead.CategoryKey.In(categoryKeys...))
	}

	categories, err = query.Where(idQuery).Find()
	if err != nil {
		return nil, err
	}

	return categories, err
}

func GetCategoryListByBizLine(ctx context.Context, bizLine int32, needAll bool, categoryType *int32, isTest int32) (categoryList []*model.Category, err error) {
	read := FweEcomReadQuery.Category

	cond := read.WithContext(ctx).Where(read.BizLine.Eq(bizLine)).Where(read.IsDelete.Eq(0)).Where(read.IsTest.Eq(isTest))

	if !needAll {
		cond = cond.Where(read.IsLeaf.Eq(1))
	}
	if categoryType != nil {
		cond = cond.Where(read.CategoryType.Eq(*categoryType))
	}

	cond = cond.Order(read.Level).Order(read.Sequence)

	categoryList, err = cond.Find()

	if err != nil {
		return nil, err
	}

	return categoryList, nil
}

func searchCategoryListByParam(ctx context.Context, req *category.GetBizLineCategoriesReq, isTest int32) (categoryList []*model.Category, err error) {
	read := FweEcomReadQuery.Category
	cond := read.WithContext(ctx).Where(read.BizLine.Eq(int32(req.GetBizLine()))).Where(read.IsDelete.Eq(0)).Where(read.IsTest.Eq(isTest))

	if req.CategorySearchReq != nil {
		if req.CategorySearchReq.Status != nil {
			cond = cond.Where(read.Status.Eq(*req.CategorySearchReq.Status))
		}
		if req.CategorySearchReq.Name != nil {
			cond = cond.Where(read.Name.Like(fmt.Sprintf("%%%s%%", *req.CategorySearchReq.Name)))
		}
		if req.CategorySearchReq.CategoryKey != nil {
			cond = cond.Where(read.CategoryKey.Eq(*req.CategorySearchReq.CategoryKey))
		}
		if len(req.CategorySearchReq.CategoryIds) > 0 {
			cond = cond.Where(read.ID.In(req.CategorySearchReq.CategoryIds...))
		}
	}
	if req.CategoryType != nil {
		cond = cond.Where(read.CategoryType.Eq(*req.CategoryType))
	}
	cond = cond.Order(read.Level).Order(read.Sequence)

	categoryList, err = cond.Find()

	if err != nil {
		return nil, err
	}

	return categoryList, nil
}

func SearchCategoryListByBizLine(ctx context.Context, req *category.GetBizLineCategoriesReq, isTest int32) (categoryList []*model.Category, err error) {
	// 先查满足条件的类目
	categoryList, err = searchCategoryListByParam(ctx, req, isTest)
	if err != nil {
		return nil, err
	}

	extraInfoList := gslice.FilterMap(categoryList, func(f *model.Category) (*biz.CategoryExtra, bool) {
		if strings.Trim(f.Extra, " ") == "" {
			return nil, false
		}

		var extraInfo = &biz.CategoryExtra{}
		err := sonic.UnmarshalString(f.Extra, &extraInfo)
		if err != nil {
			logs.CtxWarn(ctx, "Unmarshal Category Extra error, err=%+v", err)
			return nil, false
		}
		return extraInfo, true
	})

	var allCategoryIDs []int64

	// 补齐父子节点，构成树状结构
	// 需要查询父类目
	if req.CategorySearchReq != nil && req.CategorySearchReq.GetNeedParentCategory() {
		var parentsIDList []int64
		parentsIDList = gslice.FlatMap(extraInfoList, func(f *biz.CategoryExtra) []int64 {
			return f.Parents
		})
		allCategoryIDs = append(allCategoryIDs, parentsIDList...)
	}

	// 需要查询子类目
	if req.CategorySearchReq != nil && req.CategorySearchReq.GetNeedChildCategory() {
		var childrenIDList []int64
		childrenIDList = gslice.FlatMap(extraInfoList, func(f *biz.CategoryExtra) []int64 {
			return f.Children
		})
		allCategoryIDs = append(allCategoryIDs, childrenIDList...)
	}

	otherCategories, err := searchCategoryListByParam(ctx, &category.GetBizLineCategoriesReq{
		BizLine:      req.BizLine,
		NeedAll:      req.NeedAll,
		CategoryType: req.CategoryType,
		CategorySearchReq: &category.CategorySearchReq{
			CategoryIds: allCategoryIDs,
			Status:      req.CategorySearchReq.Status,
		},
		IsTest: req.IsTest,
	}, isTest)
	if err != nil {
		return nil, err
	}

	return gslice.UniqBy(append(categoryList, otherCategories...), func(t *model.Category) int64 {
		return t.ID
	}), nil
}

func CreateCategory(ctx context.Context, tx *mysql.Query, category *model.Category) (int64, error) {
	db := tx.Category
	err := db.WithContext(ctx).Create(category)
	if err != nil {
		return 0, err
	}

	return category.ID, nil
}

func UpdatesCategory(ctx context.Context, tx *mysql.Query, ids []int64, columns map[string]interface{}) (int64, error) {
	if columns == nil {
		return 0, nil
	}

	db := tx.Category
	info, err := db.WithContext(ctx).Where(db.ID.In(ids...)).Updates(columns)
	if err != nil {
		return 0, err
	}

	return info.RowsAffected, nil
}

func CountChildrenCategoryByParentID(ctx context.Context, tx *mysql.Query, parentID int64) (int64, error) {
	db := tx.Category

	return db.WithContext(ctx).Where(db.ParentID.Eq(parentID)).Count()
}

func QueryAllChildrenCategoryIDsByParentID(ctx context.Context, tx *mysql.Query, parentID int64) ([]int64, error) {
	db := tx.Category
	sql := `select id from (select * from motor_category order by parent_id, id) products_sorted, (select @pv := ?) 
	initialisation where find_in_set(parent_id, @pv) and length(@pv := concat(@pv, ',', id)) and is_delete=0`
	var ids []int64
	err := db.WithContext(ctx).UnderlyingDB().Raw(sql, parentID).Find(&ids).Error
	return ids, err
}
