package dal

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

type catPropRel struct{}

var CategoryPropertyRel = new(catPropRel)

func (s *catPropRel) MCreate(ctx context.Context, db *gorm.DB, input []*do.ProductCategoryPropertyRel) (bizErr *errdef.BizErr) {
	var (
		q   = GetQuery(db).ProductCategoryPropertyRel
		err error
	)
	err = q.WithContext(ctx).CreateInBatches(input, 50)
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[CategoryPropertyRel] err=%s", bizErr.Error())
		return
	}
	return
}

type CatRelQueryParam struct {
	PropertyKeyS []string
	CategoryIDs  []int64
	Limit        int
}

func (s *catPropRel) Query(ctx context.Context, db *gorm.DB, param *CatRelQueryParam) (dataList []*do.ProductCategoryPropertyRel, bizErr *errdef.BizErr) {
	var (
		q     = GetQuery(db).ProductCategoryPropertyRel
		condS []gen.Condition
		err   error
	)

	if param.Limit <= 0 {
		param.Limit = 1
	}

	condS = append(condS, q.DeleteTime.Eq(0))
	if len(param.PropertyKeyS) > 0 {
		condS = append(condS, q.PropertyKey.In(param.PropertyKeyS...))
	}
	if len(param.CategoryIDs) > 0 {
		condS = append(condS, q.CategoryID.In(param.CategoryIDs...))
	}

	dataList, err = q.WithContext(ctx).Where(condS...).Limit(param.Limit).Find()
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[CategoryPropertyRel] err=%s", bizErr.Error())
		return
	}
	return
}

type RelUpdateParam struct {
	WherePropertyKeyS []string
	WhereCategoryIDs  []int64
	UpdateDeleteTime  *int64
}

func (s *catPropRel) Update(ctx context.Context, db *gorm.DB, param *RelUpdateParam) (bizErr *errdef.BizErr) {
	var (
		q       = GetQuery(db).ProductCategoryPropertyRel
		condS   []gen.Condition
		updateS []field.AssignExpr
		err     error
	)
	// param
	if len(param.WhereCategoryIDs) > 1 && len(param.WherePropertyKeyS) > 1 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("Relation更新时不能同时指定多个类目和多个属性key")
		return
	}

	// where
	if len(param.WhereCategoryIDs) > 0 {
		condS = append(condS, q.CategoryID.In(param.WhereCategoryIDs...))
	}
	if len(param.WherePropertyKeyS) > 0 {
		condS = append(condS, q.PropertyKey.In(param.WherePropertyKeyS...))
	}
	if len(condS) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("Relation没有筛选条件")
		return
	}
	condS = append(condS, q.DeleteTime.Eq(0))

	// update
	if param.UpdateDeleteTime != nil {
		updateS = append(updateS, q.DeleteTime.Value(*param.UpdateDeleteTime))
	}

	// do
	_, err = q.WithContext(ctx).Where(condS...).UpdateColumnSimple(updateS...)
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[CategoryPropertyRel] err=%s", bizErr.Error())
		return
	}
	return
}
