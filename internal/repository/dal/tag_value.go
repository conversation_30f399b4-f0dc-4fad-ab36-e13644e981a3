package dal

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/pkg/errors"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
)

func BatchCreateTagValue(ctx context.Context, tx *mysql.Query, tagValueList []*do.TagValue) (err error) {
	db := tx.TagValue

	err = db.WithContext(ctx).CreateInBatches(tagValueList, 20)
	if err != nil {
		logs.CtxError(ctx, "[BatchCreateTagValue] CreateInBatches err:%v", err)
		return
	}

	return nil
}

func BatchGetTagValueByCodeType(ctx context.Context, tx *mysql.Query, tagCodeTypeList []string) (
	tagValueList []*do.TagValue, err error) {

	db := tx.TagValue
	tagValueList, err = db.WithContext(ctx).
		Where(db.TagCodeType.In(tagCodeTypeList...)).
		Where(db.IsDeleted.Eq(0)).
		Find()
	if err != nil {
		logs.CtxError(ctx, "[BatchGetTagValueByCodeType] BatchGetTagValueByCodeType err:%v", err)
		return
	}

	return
}

func UpdateTagValue(ctx context.Context, tx *mysql.Query, id int64, updates map[string]interface{}) (err error) {
	db := tx.TagValue
	result, err := db.WithContext(ctx).Where(db.ID.Eq(id)).Updates(updates)
	if err != nil {
		logs.CtxError(ctx, "[UpdateTagValue] UpdateTagValue err:%v", err)
		return
	}

	if result.RowsAffected != 1 {
		logs.CtxError(ctx, "[UpdateTagValue] rows affected not eq 1")
		err = errors.New("rows affected not eq 1")
		return
	}

	return
}

func DelTagValueByTagCodeType(ctx context.Context, tx *mysql.Query, tagCodeTypeList []string, operatorID string,
	operatorName string) (err error) {

	updates := map[string]interface{}{
		"is_deleted":    1,
		"operator_id":   operatorID,
		"operator_name": operatorName,
	}

	db := tx.TagValue
	_, err = db.WithContext(ctx).Where(db.TagCodeType.In(tagCodeTypeList...)).Updates(updates)
	if err != nil {
		logs.CtxError(ctx, "[DelTagValueByTagCodeType] db err:%v", err)
		return
	}

	return
}

func DelTagValueByIDs(ctx context.Context, tx *mysql.Query, ids []int64) (err error) {
	db := tx.TagValue
	_, err = db.WithContext(ctx).Where(db.ID.In(ids...)).UpdateColumn(db.IsDeleted, 1)
	if err != nil {
		logs.CtxError(ctx, "[DelTagValueByIDs] db err:%v", err)
		return
	}

	return
}
