package dal

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

type tenantRropRel struct{}

var TenantPropRel = new(tenantRropRel)

func (s *tenantRropRel) MCreate(ctx context.Context, db *gorm.DB, input []*do.ProductTenantPropertyRel) (bizErr *errdef.BizErr) {
	var (
		q   = GetQuery(db).ProductTenantPropertyRel
		err error
	)
	err = q.WithContext(ctx).CreateInBatches(input, 50)
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[TenantPropRel] err=%s", bizErr.Error())
		return
	}
	return
}

type TenantRelQueryParam struct {
	PropertyKeyS []string
	TenantTypeS  []int64
	Limit        int
}

func (s *tenantRropRel) Query(ctx context.Context, db *gorm.DB, param *TenantRelQueryParam) (dataList []*do.ProductTenantPropertyRel, bizErr *errdef.BizErr) {
	var (
		q     = GetQuery(db).ProductTenantPropertyRel
		condS []gen.Condition
		err   error
	)

	condS = append(condS, q.DeleteTime.Eq(0))
	if len(param.PropertyKeyS) > 0 {
		condS = append(condS, q.PropertyKey.In(param.PropertyKeyS...))
	}
	if len(param.TenantTypeS) > 0 {
		condS = append(condS, q.TenantType.In(param.TenantTypeS...))
	}

	dataList, err = q.WithContext(ctx).Where(condS...).Limit(param.Limit).Find()
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[TenantPropRel] err=%s", bizErr.Error())
		return
	}
	return
}

type TenantRelUpdateParam struct {
	WherePropertyKeyS []string
	WhereTenantTypeS  []int64
	UpdateDeleteTime  *int64
}

func (s *tenantRropRel) Update(ctx context.Context, db *gorm.DB, param *TenantRelUpdateParam) (bizErr *errdef.BizErr) {
	var (
		q       = GetQuery(db).ProductTenantPropertyRel
		condS   []gen.Condition
		updateS []field.AssignExpr
		err     error
	)
	// param
	if len(param.WhereTenantTypeS) > 1 && len(param.WherePropertyKeyS) > 1 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("Relation更新时不能同时指定多个类目和多个属性key")
		return
	}

	// where
	if len(param.WhereTenantTypeS) > 0 {
		condS = append(condS, q.TenantType.In(param.WhereTenantTypeS...))
	}
	if len(param.WherePropertyKeyS) > 0 {
		condS = append(condS, q.PropertyKey.In(param.WherePropertyKeyS...))
	}
	if len(condS) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("Relation没有筛选条件")
		return
	}
	condS = append(condS, q.DeleteTime.Eq(0))

	// update
	if param.UpdateDeleteTime != nil {
		updateS = append(updateS, q.DeleteTime.Value(*param.UpdateDeleteTime))
	}

	// do
	_, err = q.WithContext(ctx).Where(condS...).UpdateColumnSimple(updateS...)
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[TenantPropRel] err=%s", bizErr.Error())
		return
	}
	return
}
