package dal

import (
	"context"
	"errors"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
)

func CreateTagMeta(ctx context.Context, tx *mysql.Query, meta *do.TagMeta) (err error) {
	db := tx.TagMeta

	err = db.WithContext(ctx).Create(meta)
	if err != nil {
		logs.CtxError(ctx, "[CreateTagMeta] db create err:%v", err)
		return
	}

	return nil
}

func GetTagMetaByTagCodeTypeList(ctx context.Context, tx *mysql.Query, tagCodeType []string) (
	tagMetaList []*do.TagMeta, err error) {

	db := tx.TagMeta

	tagMetaList, err = db.WithContext(ctx).
		Where(db.TagCodeType.In(tagCodeType...)).
		Where(db.IsDeleted.Eq(0)).Find()

	if err != nil {
		logs.CtxError(ctx, "[GetTagMetaByTagCodeTypeList] db get err:%v", err)
		return
	}

	return
}

func GetTagMetaByIDs(ctx context.Context, tx *mysql.Query, tagIDs []int64) (tagMetaList []*do.TagMeta, err error) {
	db := tx.TagMeta

	tagMetaList, err = db.WithContext(ctx).
		Where(db.ID.In(tagIDs...)).
		Where(db.IsDeleted.Eq(0)).Find()

	if err != nil {
		logs.CtxError(ctx, "[GetTagMetaByIDs] db get err:%v", err)
		return
	}

	return
}

func DelTagMetaByCodeType(ctx context.Context, tx *mysql.Query, tagCodeType []string, operatorID string,
	operatorName string) (err error) {

	updates := map[string]interface{}{
		"is_deleted":    1,
		"operator_id":   operatorID,
		"operator_name": operatorName,
	}

	db := tx.TagMeta
	_, err = db.WithContext(ctx).Where(db.TagCodeType.In(tagCodeType...)).
		Updates(updates)
	if err != nil {
		logs.CtxError(ctx, "[DelTagMetaByCodeType] db err:%v", err)
		return
	}

	return
}

func UpdateTagMeta(ctx context.Context, tx *mysql.Query, tagCode string, tagType int32, updates map[string]interface{}) (err error) {
	db := tx.TagMeta

	result, err := db.WithContext(ctx).
		Where(db.TagCode.Eq(tagCode)).
		Where(db.Type.Eq(tagType)).
		Updates(updates)
	if err != nil {
		logs.CtxError(ctx, "[UpdateTagMeta] update err:%v", err)
		return
	}

	if result.RowsAffected != 1 {
		logs.CtxError(ctx, "[UpdateTagMeta] rows affected not eq 1")
		err = errors.New("rows affected not eq 1")
		return
	}

	return
}
