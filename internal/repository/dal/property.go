package dal

import (
	"context"

	model "code.byted.org/motor/fwe_category/internal/model/do"
)

func MGetProperty(ctx context.Context, catIds []int64, propIds []int64) (props []*model.Property, err error) {
	propRead := FweEcomReadQuery.Property
	db := propRead.WithContext(ctx)

	if len(catIds) > 0 {
		db = db.Where(propRead.CategoryID.In(catIds...))
	}

	if len(propIds) > 0 {
		db = db.Where(propRead.ID.In(propIds...))
	}

	props, err = db.
		Where(propRead.IsDelete.Eq(0)).
		Where(propRead.IsTest.Eq(0)).
		Find()

	if err != nil {
		return nil, err
	}

	return props, err
}

func GetProperty(ctx context.Context, categoryID int64, propertyID int64) (property *model.Property, err error) {
	propRead := FweEcomReadQuery.Property
	db := propRead.WithContext(ctx)

	property, err = db.Where(propRead.ID.Eq(propertyID)).
		Where(propRead.CategoryID.Eq(categoryID)).
		Where(propRead.IsDelete.Eq(0)).
		Where(propRead.IsTest.Eq(0)).
		First()

	if err != nil {
		return nil, err
	}

	return property, err
}

func MGetPropertyItemByKey(ctx context.Context, categoryID int64, propertyKeyList []string) (property []*model.Property, err error) {
	propRead := FweEcomReadQuery.Property
	db := propRead.WithContext(ctx)

	property, err = db.Where(propRead.UniqueKey.In(propertyKeyList...)).
		Where(propRead.CategoryID.Eq(categoryID)).
		Where(propRead.IsDelete.Eq(0)).
		Find()

	if err != nil {
		return nil, err
	}

	return property, err
}

func UpdatePropertyFeature(ctx context.Context, categoryID, propertyID int64, feature string) (err error) {
	opWrite := FweEcomWriteQuery.Property
	db := opWrite.WithContext(ctx)

	_, err = db.Where(opWrite.ID.Eq(propertyID)).
		Where(opWrite.CategoryID.Eq(categoryID)).
		Where(opWrite.IsDelete.Eq(0)).UpdateColumn(opWrite.Feature, feature)
	if err != nil {
		return err
	}

	return nil
}
