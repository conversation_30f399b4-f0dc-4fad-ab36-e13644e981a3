package dal

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

type productProperty struct{}

var ProductProperty = productProperty{}

func (s *productProperty) Save(ctx context.Context, db *gorm.DB, input *do.ProductProperty) (bizErr *errdef.BizErr) {
	var (
		q   = GetQuery(db).ProductProperty
		err error
	)
	err = q.WithContext(ctx).Omit(q.UpdateTime, q.CreateTime).Where(q.ID.Eq(input.ID)).Save(input)
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
		return
	}
	return
}

func (s *productProperty) MGet(ctx context.Context, db *gorm.DB, keyS []string) (mp map[string]*do.ProductProperty, bizErr *errdef.BizErr) {
	var (
		q        = GetQuery(db).ProductProperty
		dataList []*do.ProductProperty
		err      error
	)
	mp = make(map[string]*do.ProductProperty)
	dataList, err = q.WithContext(ctx).Where(q.PropertyKey.In(keyS...), q.DeleteTime.Eq(0)).Find()
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
		return
	}
	for _, v := range dataList {
		mp[v.PropertyKey] = v
	}
	return
}

func (s *productProperty) GetOne(ctx context.Context, db *gorm.DB, propertyKey string, updateLock bool) (output *do.ProductProperty, bizErr *errdef.BizErr) {
	var (
		q        = GetQuery(db).ProductProperty
		dataList []*do.ProductProperty
		err      error
	)

	tq := q.WithContext(ctx)
	if updateLock {
		tq = tq.Clauses(clause.Locking{Strength: "UPDATE"})
	}
	dataList, err = tq.Where(q.PropertyKey.Eq(propertyKey), q.DeleteTime.Eq(0)).Limit(1).Find()
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
		return
	}
	if len(dataList) == 0 {
		return
	}
	output = dataList[0]
	return
}

func (s *productProperty) MDel(ctx context.Context, db *gorm.DB, propertyKeyS []string, operator *category.Operator) (bizErr *errdef.BizErr) {
	var (
		q       = GetQuery(db).ProductProperty
		condS   []gen.Condition
		updateS []field.AssignExpr
		err     error
	)

	// condition
	condS = append(condS, q.DeleteTime.Eq(0))
	if len(propertyKeyS) > 0 {
		condS = append(condS, q.PropertyKey.In(propertyKeyS...))
	} else {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("propertyKeyS is nil")
		return
	}

	// update
	updateS = append(updateS, q.DeleteTime.Value(time.Now().Unix()), q.Status.Value(int32(category.CommonStatus_NotUse)))
	if operator != nil {
		updateS = append(updateS, q.UpdateName.Value(operator.OperatorName), q.UpdateUID.Value(operator.OperatorId))
	}

	// do
	_, err = q.WithContext(ctx).Where(condS...).UpdateColumnSimple(updateS...)
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
		return
	}
	return
}

type PropQueryParam struct {
	PropertyKeyList    []string
	NameKeyword        *string
	DescriptionKeyword *string
	Limit              int
	Offset             int
	NeedTotal          bool
}

func (s *productProperty) Query(ctx context.Context, db *gorm.DB, param *PropQueryParam) (dataList []*do.ProductProperty, total int64, bizErr *errdef.BizErr) {
	var (
		q     = GetQuery(db).ProductProperty
		condS []gen.Condition
		err   error
	)
	condS = append(condS, q.DeleteTime.Eq(0))
	if param.NameKeyword != nil {
		condS = append(condS, q.Name.Like("%"+*param.NameKeyword+"%"))
	}
	if param.DescriptionKeyword != nil {
		condS = append(condS, q.Description.Like("%"+*param.DescriptionKeyword+"%"))
	}
	if len(param.PropertyKeyList) > 0 {
		condS = append(condS, q.PropertyKey.In(param.PropertyKeyList...))
	}
	if param.NeedTotal {
		total, err = q.WithContext(ctx).Where(condS...).Count()
		if err != nil {
			bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
			logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
			return
		}
	}
	dataList, err = q.WithContext(ctx).Where(condS...).Order(q.ID.Desc()).Limit(param.Limit).Offset(param.Offset).Find()
	if err != nil {
		bizErr = motor_fwe_ecom_category.DBErr.WithErr(err)
		logs.CtxError(ctx, "[ProductProperty] err=%s", bizErr.Error())
		return
	}
	return
}

func (s *productProperty) DeepCopy(input *do.ProductProperty) *do.ProductProperty {
	newOne := &do.ProductProperty{
		ID:             input.ID,
		PropertyKey:    input.PropertyKey,
		PropertyType:   input.PropertyType,
		FieldType:      input.FieldType,
		ClassType:      input.ClassType,
		Name:           input.Name,
		Description:    input.Description,
		Status:         input.Status,
		CreateUID:      input.CreateUID,
		CreateName:     input.CreateName,
		UpdateUID:      input.UpdateUID,
		UpdateName:     input.UpdateName,
		CreateTime:     input.CreateTime,
		UpdateTime:     input.UpdateTime,
		DeleteTime:     input.DeleteTime,
		ValueDesc:      input.ValueDesc,
		PropertyConfig: input.PropertyConfig,
	}
	return newOne
}
