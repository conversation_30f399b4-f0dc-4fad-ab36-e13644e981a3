package dal

import (
	"code.byted.org/gopkg/logs"
	model "code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"context"
)

func MGetPropertyValue(ctx context.Context, catId int64, propId int64, parPropValId int64) (propVals []*model.PropertyValue, err error) {
	propValRead := FweEcomReadQuery.PropertyValue

	db := propValRead.WithContext(ctx).
		Where(propValRead.CategoryID.Eq(catId)).
		Where(propValRead.PropertyID.Eq(propId))

	if parPropValId != 0 {
		db = db.Where(propValRead.ParentValueID.Eq(parPropValId))
	}

	propVals, err = db.
		Where(propValRead.IsDelete.Eq(0)).
		Where(propValRead.IsTest.Eq(0)).Find()

	if err != nil {
		return nil, err
	}

	return propVals, err
}

func CreateOrUpdatePropertyValue(ctx context.Context, tx *mysql.Query, propertyValueDB *model.PropertyValue) error {
	var (
		db  = tx.PropertyValue
		err error
	)
	if propertyValueDB.ID == 0 {
		// 创建
		err = db.WithContext(ctx).Create(propertyValueDB)
	} else {
		// 更新
		_, err = db.WithContext(ctx).
			//Omit(db.CreateTime, db.CreateUID, db.IsDelete, db.Status, db.SpuID, db.PropertyID, db.PropertyKey).
			Select(db.ParentValueID, db.Name, db.Alias_, db.OutID, db.Sequence, db.Price, db.MarketPrice, db.UpdateTime, db.UpdateUID, db.Feature).
			Where(db.ID.Eq(propertyValueDB.ID)).
			Where(db.ShopID.Eq(propertyValueDB.ShopID)).
			Where(db.CategoryID.Eq(propertyValueDB.CategoryID)).
			Updates(propertyValueDB)
	}

	return err
}

func UpdatePropertyValueStatus(ctx context.Context, tx *mysql.Query, categoryId int64, shopId string, propertyValueId int64, status int32) (err error) {
	db := tx.PropertyValue
	_, err = db.WithContext(ctx).
		Where(db.ID.Eq(propertyValueId)).
		Where(db.ShopID.Eq(shopId)).
		Where(db.CategoryID.Eq(categoryId)).
		UpdateColumn(db.Status, status)
	if err != nil {
		logs.CtxError(ctx, "[UpdatePropertyValueStatus] UpdatePropertyValueStatus err:%v", err)
		return
	}
	return
}
