package property_handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/consts"
	"code.byted.org/motor/fwe_category/internal/domain"
	"code.byted.org/motor/fwe_category/internal/domain/property_value/common_property"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type QueryPropertyValueHandler struct {
}

func NewQueryPropertyValueHandler() *QueryPropertyValueHandler {
	return &QueryPropertyValueHandler{}
}

func (s *QueryPropertyValueHandler) QueryPropertyValue(ctx context.Context, req *category.QueryPropertyValueReq) (resp *category.QueryPropertyValueResp) {
	resp = category.NewQueryPropertyValueResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	property, err := dal.GetProperty(ctx, req.GetCategoryId(), req.GetPropertyId())
	if err != nil {
		logs.CtxError(ctx, "[QueryPropertyValue] GetProperty DB err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err).WithMessage("获取属性值失败")
		return
	}

	propertyValues := make([]*category.PropertyValue, 0)

	if property.Source != int32(consts.PropertySource_Common) {
		// 其他数据来源
		commonPropertyFetcher := common_property.NewCommonPropertyFetcher(property)
		if commonPropertyFetcher != nil {
			propertyValues = commonPropertyFetcher.FetchPropertyValueList(ctx, req.GetParentValueId())
		}
	} else {
		// 自定义属性-来源DB
		propertyValues, err = domain.GetPropertyValue(ctx, req.GetCategoryId(), req.GetPropertyId(), req.GetParentValueId())
		if err != nil {
			logs.CtxError(ctx, "[QueryPropertyValue] GetPropertyValue fail err is %v", err)
			bizErr = biz_err.ServerException.WithErr(err).WithMessage("获取属性值失败")
			return
		}
	}

	resp.PropertyValues = propertyValues

	return
}
