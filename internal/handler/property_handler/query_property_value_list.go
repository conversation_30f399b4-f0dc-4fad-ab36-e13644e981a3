package property_handler

import (
	"context"

	"code.byted.org/motor/fwe_category/internal/common/converter"
	model "code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type QueryPropertyValueListHandler struct {
	req *category.QueryPropertyValueListReq
}

func NewQueryPropertyValueListHandler() *QueryPropertyValueListHandler {
	return &QueryPropertyValueListHandler{}
}

func (s *QueryPropertyValueListHandler) QueryPropertyValue(ctx context.Context, req *category.QueryPropertyValueListReq) (
	resp *category.QueryPropertyValueListResp) {
	resp = category.NewQueryPropertyValueListResp()
	resp.BaseResp = base.NewBaseResp()
	var (
		bizErr *errdef.BizErr
		result []*model.PropertyValue
		total  int64
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	s.req = req
	result, total, bizErr = s.DoQuery(ctx)
	if bizErr != nil {
		return
	}

	resp.SetPropertyValues(converter.NewPO2DO().MTransPropertyValue(result))
	resp.SetTotal(total)
	resp.SetHasMore(total > req.GetOffset()+req.GetLimit())
	return
}

func (s *QueryPropertyValueListHandler) DoQuery(ctx context.Context) (result []*model.PropertyValue, total int64, bizErr *errdef.BizErr) {
	var (
		tableModel = dal.FweEcomWriteQuery.PropertyValue
		db         = tableModel.WithContext(ctx)
		req        = s.req
	)

	db = db.Where(tableModel.CategoryID.Eq(req.CategoryId)).Order(tableModel.ID.Desc())
	if len(req.ShopIdList) > 0 {
		db = db.Where(tableModel.ShopID.In(req.GetShopIdList()...))
	}

	if len(req.SpuIdList) > 0 {
		db = db.Where(tableModel.SpuID.In(req.GetSpuIdList()...))
	}

	if len(req.PropertyKeyList) > 0 {
		db = db.Where(tableModel.PropertyKey.In(req.GetPropertyKeyList()...))
	}

	if len(req.PropertyIdList) > 0 {
		db = db.Where(tableModel.PropertyID.In(req.GetPropertyIdList()...))
	}

	if req.IsSetPropertyValueStatus() {
		db = db.Where(tableModel.Status.Eq(int32(req.GetPropertyValueStatus())))
	}

	if req.GetPropertyValueName() != "" {
		db = db.Where(tableModel.Name.Like("%" + req.GetPropertyValueName() + "%"))
	}

	if len(req.PropertyValueIdList) > 0 {
		db = db.Where(tableModel.ID.In(req.GetPropertyValueIdList()...))
	}

	result, total, err := db.FindByPage(int(req.GetOffset()), int(req.GetLimit()))
	if err != nil {
		return nil, 0, biz_err.ServerException.WithErr(err)
	}

	return result, total, nil
}
