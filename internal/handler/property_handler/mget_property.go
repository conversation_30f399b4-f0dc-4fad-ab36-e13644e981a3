package property_handler

import (
	"context"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/domain/property_item"
	"code.byted.org/motor/fwe_category/internal/domain/property_value"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type MGetPropertyItemHandler struct {
	propertyKeyList     []string
	propertyKeyParamMap map[string]*category.PropertyGetParam
}

func NewMGetPropertyItemHandler() *MGetPropertyItemHandler {
	return &MGetPropertyItemHandler{}
}

func (s *MGetPropertyItemHandler) MGetPropertyItem(ctx context.Context, req *category.MGetPropertyItemReq) (resp *category.MGetPropertyItemResp) {
	resp = category.NewMGetPropertyItemResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		propertyValueMap map[string][]*category.PropertyValue
		propertyItemList []*category.PropertyItem
		bizErr           *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	if bizErr = s.checkParam(ctx, req); bizErr != nil {
		return
	}

	propertyItemList, bizErr = property_item.NewPropertyItemService().MGetPropertyItem(ctx, req.GetCategoryId(), s.propertyKeyList)
	if bizErr != nil {
		return
	}

	resp.SetPropertyItems(propertyItemList)

	if req.WithPropertyValue {
		propertyValueMap, bizErr = property_value.NewPropertyValueService(req.GetCategoryId(), req.GetShopIdList(), req.GetSpuIdList(), conv.Int64Ptr(1)).
			MGetPropertyValueByProperty(ctx, propertyItemList, s.propertyKeyParamMap)
		if bizErr != nil {
			return
		}

		gslice.ForEach(propertyItemList, func(property *category.PropertyItem) {
			property.Values = propertyValueMap[property.UniqueKey]
		})

		return
	}

	resp.SetPropertyItems(propertyItemList)
	return
}

func (s *MGetPropertyItemHandler) checkParam(ctx context.Context, req *category.MGetPropertyItemReq) *errdef.BizErr {
	if req.GetCategoryId() == 0 {
		return biz_err.ParamsErr.WithMessage("缺少类目ID")
	}

	if len(req.GetPropertyParamList()) == 0 {
		return biz_err.ParamsErr.WithMessage("property key 过多")
	}

	if len(req.GetPropertyParamList()) > 50 {
		return biz_err.ParamsErr.WithMessage("获取过多属性")
	}

	s.propertyKeyList = gslice.Map(req.GetPropertyParamList(), func(param *category.PropertyGetParam) string {
		return param.PropertyKey
	})
	s.propertyKeyParamMap = gslice.ToMap(req.GetPropertyParamList(), func(param *category.PropertyGetParam) (string, *category.PropertyGetParam) {
		return param.PropertyKey, param
	})

	return nil
}
