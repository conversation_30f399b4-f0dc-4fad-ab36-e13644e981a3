package property_handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/common/utils"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type UpsertPropertyValueHandler struct {
}

func NewUpsertPropertyValueHandler() *UpsertPropertyValueHandler {
	return &UpsertPropertyValueHandler{}
}

func (s *UpsertPropertyValueHandler) UpsertPropertyValue(ctx context.Context, req *category.UpsertPropertyValueReq) (resp *category.UpsertPropertyValueResp) {
	resp = category.NewUpsertPropertyValueResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	if req.PropertyValue == nil {
		bizErr = biz_err.ServerException.WithMessage("value 参数不对")
		return
	}

	propertyValueData := converter.NewDO2PO().PropertyVal(req.PropertyValue)
	err := dal.CreateOrUpdatePropertyValue(ctx, dal.FweEcomWriteQuery, propertyValueData)
	if err != nil {
		logs.CtxError(ctx, "[QueryPropertyValue] GetProperty DB err:%v", err)
		if utils.IsDuplicate(err) {
			bizErr = biz_err.PropertyValueDuplicateErr.WithErr(err)
			return
		}

		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	resp.SetPropertyValueId(propertyValueData.ID)
	return
}
