package handler

import (
	"context"

	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"

	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"

	"code.byted.org/motor/fwe_category/internal/domain"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

type MGetCategoryItemService struct {
}

func NewMGetSkuItemService() *MGetCategoryItemService {
	return &MGetCategoryItemService{}
}

func (s *MGetCategoryItemService) MGetCategoryItem(ctx context.Context, req *category.MGetCategoryItemReq) (resp *category.MGetCategoryItemResp) {
	resp = category.NewMGetCategoryItemResp()
	resp.BaseResp = base.NewBaseResp()
	var (
		isTest             = int32(0)
		bizErr             *errdef.BizErr
		categoryItemMap    = make(map[int64]*category.CategoryItem, len(req.GetCategoryIds()))
		categoryKeyItemMap = make(map[string]*category.CategoryItem, len(req.GetCategoryKeys()))
		categoryBases      []*category.CategoryBase
		err                error
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	resp.CategoryItemMap = categoryItemMap
	resp.CategoryKeyItemMap = categoryKeyItemMap

	if req.IsTest != nil && *req.IsTest {
		isTest = 1
	}

	categoryBases, err = domain.GetCategoryBaseByIDAndKey(ctx, req.GetCategoryIds(), req.GetCategoryKeys(), isTest)
	if err != nil {
		logs.CtxError(ctx, "[MGetCategoryItem] GetCategoryBaseByIDAndKey err is %v", err)
		bizErr = biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
		return resp
	}

	for _, categoryBase := range categoryBases {
		categoryItem := category.NewCategoryItem()
		categoryItem.SetCategoryBase(categoryBase)
		categoryItemMap[categoryBase.Id] = categoryItem
		categoryKeyItemMap[categoryBase.CategoryKey] = categoryItem
		if req.GetNeedProperty() {
			categoryItem.PropertyItems, err = domain.GetPropertyItem(ctx, []int64{categoryBase.Id})
			if err != nil {
				logs.CtxError(ctx, "[MGetCategoryItem] GetPropertyItem err is %v", err)
				bizErr = biz_err.DBErr.WithErr(err).WithMessage("获取属性项失败")
				return resp
			}
		}
	}

	return resp
}
