package tag

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type BatchDelTagMetaService struct {
}

func NewBatchDelTagMetaService() *BatchDelTagMetaService {
	return &BatchDelTagMetaService{}
}

func (s *BatchDelTagMetaService) BatchDelTagMeta(ctx context.Context, req *category.BatchDelTagMetaReq) (resp *category.BatchDelTagMetaResp) {
	resp = category.NewBatchDelTagMetaResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		tagCodeTypeList = make([]string, 0)
		bizErr          *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	for _, v := range req.GetCodeList() {
		tagCodeType := fmt.Sprintf("%d_%s", req.GetTagType(), v)
		if !slices.ContainsString(tagCodeTypeList, tagCodeType) {
			tagCodeTypeList = append(tagCodeTypeList, tagCodeType)
		}
	}

	if err := dal.FweEcomWriteQuery.Transaction(func(tx *mysql.Query) (err error) {
		if err = dal.DelTagMetaByCodeType(ctx, dal.FweEcomWriteQuery, tagCodeTypeList, req.GetOperator().GetOperatorId(),
			req.GetOperator().GetOperatorName()); err != nil {
			logs.CtxError(ctx, "[BatchDelTagMetaService] BatchDelTagMeta db err:%v", err)
			return
		}

		if err = dal.DelTagValueByTagCodeType(ctx, dal.FweEcomWriteQuery, tagCodeTypeList, req.GetOperator().GetOperatorId(),
			req.GetOperator().GetOperatorName()); err != nil {
			logs.CtxError(ctx, "[BatchDelTagMetaService] DelTagValueByTagCodeType db err:%v", err)
			return
		}

		return
	}); err != nil {
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	return
}
