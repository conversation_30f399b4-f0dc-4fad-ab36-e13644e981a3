package tag

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type MGetTagByIDService struct {
}

func NewMGetTagByIDService() *MGetTagByIDService {
	return &MGetTagByIDService{}
}

func (s *MGetTagByIDService) MGetTagByID(ctx context.Context, req *category.MGetTagByIDReq) (resp *category.MGetTagByIDResp) {
	resp = category.NewMGetTagByIDResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	tagMetaList, err := dal.GetTagMetaByIDs(ctx, dal.FweEcomReadQuery, req.GetTagIds())
	if err != nil {
		logs.CtxError(ctx, "[MGetTagByIDService] GetTagMetaByIDs err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	if len(tagMetaList) == 0 {
		return
	}

	tagCodeTypeList := make([]string, 0)
	for _, v := range tagMetaList {
		tagCodeTypeList = append(tagCodeTypeList, fmt.Sprintf("%d_%s", v.Type, v.TagCode))
	}

	tagValueList, err := dal.BatchGetTagValueByCodeType(ctx, dal.FweEcomReadQuery, tagCodeTypeList)
	if err != nil {
		logs.CtxError(ctx, "[MGetTagByCodeService] BatchGetTagValueByCodeType err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	codeType2TagValueList := make(map[string][]*do.TagValue)
	for _, v := range tagValueList {
		codeType2TagValueList[v.TagCodeType] = append(codeType2TagValueList[v.TagCodeType], v)
	}

	tagItemList := make([]*category.TagItem, 0, len(tagMetaList))

	for _, v := range tagMetaList {
		tagItemList = append(tagItemList, packTagItem(v, codeType2TagValueList[v.TagCodeType]))
	}

	resp.SetTagList(tagItemList)

	return
}
