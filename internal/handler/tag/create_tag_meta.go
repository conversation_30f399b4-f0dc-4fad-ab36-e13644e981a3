package tag

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type CreateTagMetaService struct {
}

func NewCreateTagMetaService() *CreateTagMetaService {
	return &CreateTagMetaService{}
}

func (s *CreateTagMetaService) CreateTagMeta(ctx context.Context, req *category.CreateTagMetaReq) (resp *category.CreateTagMetaResp) {
	resp = category.NewCreateTagMetaResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	tagMetaList, err := dal.GetTagMetaByTagCodeTypeList(ctx, dal.FweEcomReadQuery, []string{fmt.Sprintf("%d_%s", req.GetTagType(), req.GetCode())})
	if err != nil {
		logs.CtxError(ctx, "[CreateTagMeta] GetTagMetaByTagCodeTypeList err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	if len(tagMetaList) > 0 {
		logs.CtxWarn(ctx, "[CreateTagMeta] tag meta exist")
		bizErr = biz_err.ParamsErr.WithMessage("标签元数据已存在")
		return
	}

	if len(req.GetTagValueList()) > 20 {
		logs.CtxWarn(ctx, "[CreateTagMeta] tag value limit")
		bizErr = biz_err.ParamsErr.WithMessage("标签值最多支持20个")
		return
	}

	valueList := make([]string, 0)
	for _, value := range req.GetTagValueList() {
		if value.GetCode() != req.GetCode() {
			logs.CtxWarn(ctx, "[CreateTagMeta] tag value connect code not same")
			bizErr = biz_err.ParamsErr.WithMessage("标签值关联code不统一")
			return
		}

		if slices.ContainsString(valueList, value.GetValue()) {
			logs.CtxWarn(ctx, "[CreateTagMeta] tag value repeat")
			bizErr = biz_err.ParamsErr.WithMessage("存在重复标签值")
			return
		}

		valueList = append(valueList, value.GetValue())
	}

	tagMeta := s.transTagMeta(req)
	tagValueList := s.transTagValue(req)

	if err = dal.FweEcomWriteQuery.Transaction(func(tx *mysql.Query) (err error) {
		if err = dal.CreateTagMeta(ctx, tx, tagMeta); err != nil {
			logs.CtxError(ctx, "[CreateTagMeta] CreateTagMeta db err:%v", err)
			return
		}

		if err = dal.BatchCreateTagValue(ctx, tx, tagValueList); err != nil {
			logs.CtxError(ctx, "[CreateTagMeta] BatchCreateTagValue db err:%v", err)
			return
		}

		return
	}); err != nil {
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	resp.SetTagMetaId(tagMeta.ID)
	return resp
}

func (s *CreateTagMetaService) transTagValue(input *category.CreateTagMetaReq) (tagValueList []*do.TagValue) {
	var (
		operatorID   string
		operatorName string
	)

	if input.Creator != nil {
		operatorID = input.GetCreator().GetOperatorId()
		operatorName = input.GetCreator().GetOperatorName()
	}

	for _, v := range input.GetTagValueList() {
		tagValueItem := &do.TagValue{
			TagCode:      v.GetCode(),
			TagValue:     v.GetValue(),
			TagDesc:      v.GetDesc(),
			TagCodeType:  fmt.Sprintf("%d_%s", input.GetTagType(), input.GetCode()),
			OperatorID:   operatorID,
			OperatorName: operatorName,
			Extra:        v.GetExtra(),
			CreatedTime:  time.Now(),
			UpdatedTime:  time.Now(),
		}

		tagValueList = append(tagValueList, tagValueItem)
	}

	return
}

func (s *CreateTagMetaService) transTagMeta(input *category.CreateTagMetaReq) *do.TagMeta {
	tagMeta := &do.TagMeta{
		TagCode:     input.Code,
		Name:        input.Name,
		Type:        int32(input.TagType),
		Source:      int32(input.Source),
		Status:      int32(input.TagStatus),
		TagDesc:     input.Desc,
		Extra:       input.Extra,
		CreatedTime: time.Now(),
		UpdatedTime: time.Now(),
		TagCodeType: fmt.Sprintf("%d_%s", input.GetTagType(), input.GetCode()),
	}

	if input.Creator != nil {
		tagMeta.OperatorID = input.GetCreator().GetOperatorId()
		tagMeta.OperatorName = input.GetCreator().GetOperatorName()
	}

	return tagMeta
}
