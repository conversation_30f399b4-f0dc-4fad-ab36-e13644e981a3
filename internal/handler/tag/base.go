package tag

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

func packTagItem(tagMeta *do.TagMeta, tagValueList []*do.TagValue) *category.TagItem {
	tagValueItems := make([]*category.TagValue, 0, len(tagValueList))
	for _, v := range tagValueList {
		tagValueItems = append(tagValueItems, packTagValueItem(v))
	}

	item := &category.TagItem{
		TagType:      category.TagType(tagMeta.Type),
		Source:       int64(tagMeta.Source),
		Code:         tagMeta.TagCode,
		Name:         tagMeta.Name,
		TagStatus:    category.TagStatus(tagMeta.Status),
		Desc:         tagMeta.TagDesc,
		TagValueList: tagValueItems,
		CreatedTime:  tagMeta.CreatedTime.Unix(),
		UpdatedTime:  tagMeta.UpdatedTime.Unix(),
		Operator: &category.Operator{
			OperatorId:   tagMeta.OperatorID,
			OperatorName: tagMeta.OperatorName,
		},
		TagId: tagMeta.ID,
	}

	return item
}

func packTagValueItem(tagValue *do.TagValue) *category.TagValue {
	tagVItem := &category.TagValue{
		Code:    tagValue.TagCode,
		ValueId: tagValue.ID,
		Value:   tagValue.TagValue,
		Desc:    tagValue.TagDesc,
		Extra:   tagValue.Extra,
	}

	return tagVItem
}
