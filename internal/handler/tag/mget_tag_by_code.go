package tag

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type MGetTagByCodeService struct {
}

func NewMGetTagByCodeService() *MGetTagByCodeService {
	return &MGetTagByCodeService{}
}

func (s *MGetTagByCodeService) MGetTagByCode(ctx context.Context, req *category.MGetTagByCodeReq) (resp *category.MGetTagByCodeResp) {
	resp = category.NewMGetTagByCodeResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	tagCodeTypeList := make([]string, 0)
	for _, v := range req.GetTagMetaUniqItemList() {
		tagCodeTypeList = append(tagCodeTypeList, fmt.Sprintf("%d_%s", v.GetTagType(), v.GetTagCode()))
	}

	tagMetaList, err := dal.GetTagMetaByTagCodeTypeList(ctx, dal.FweEcomReadQuery, tagCodeTypeList)
	if err != nil {
		logs.CtxError(ctx, "[MGetTagByCodeService] GetTagMetaByTagCodeTypeList err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	tagValueList, err := dal.BatchGetTagValueByCodeType(ctx, dal.FweEcomReadQuery, tagCodeTypeList)
	if err != nil {
		logs.CtxError(ctx, "[MGetTagByCodeService] BatchGetTagValueByCodeType err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	codeType2TagValueList := make(map[string][]*do.TagValue)
	for _, v := range tagValueList {
		codeType2TagValueList[v.TagCodeType] = append(codeType2TagValueList[v.TagCodeType], v)
	}

	tagItemList := make([]*category.TagItem, 0, len(tagMetaList))

	for _, v := range tagMetaList {
		tagItemList = append(tagItemList, packTagItem(v, codeType2TagValueList[v.TagCodeType]))
	}

	resp.SetTagList(tagItemList)

	return
}
