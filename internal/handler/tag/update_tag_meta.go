package tag

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type UpdateTagMetaService struct {
}

func NewUpdateTagMetaService() *UpdateTagMetaService {
	return &UpdateTagMetaService{}
}

func (s *UpdateTagMetaService) UpdateTagMeta(ctx context.Context, req *category.UpdateTagMetaReq) (resp *category.UpdateTagMetaResp) {
	resp = category.NewUpdateTagMetaResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	tagMetaList, err := dal.GetTagMetaByTagCodeTypeList(ctx, dal.FweEcomReadQuery, []string{fmt.Sprintf("%d_%s", req.GetTagType(), req.GetCode())})
	if err != nil {
		logs.CtxError(ctx, "[UpdateTagMeta] GetTagMetaByTagCodeTypeList err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	if len(tagMetaList) == 0 {
		logs.CtxWarn(ctx, "[UpdateTagMeta] tag meta exist")
		bizErr = biz_err.ParamsErr.WithMessage("标签元数据不存在")
		return
	}

	if len(req.GetTagValueList()) > 20 {
		logs.CtxWarn(ctx, "[UpdateTagMeta] tag value limit")
		bizErr = biz_err.ParamsErr.WithMessage("标签值最多支持20个")
		return
	}

	valueList := make([]string, 0)
	for _, value := range req.GetTagValueList() {
		if value.GetCode() != req.GetCode() {
			logs.CtxWarn(ctx, "[UpdateTagMeta] tag value connect code not same")
			bizErr = biz_err.ParamsErr.WithMessage("标签值关联code不统一")
			return
		}

		if slices.ContainsString(valueList, value.GetValue()) {
			logs.CtxWarn(ctx, "[UpdateTagMeta] tag value repeat")
			bizErr = biz_err.ParamsErr.WithMessage("存在重复标签值")
			return
		}

		valueList = append(valueList, value.GetValue())
	}

	tagValueList, err := dal.BatchGetTagValueByCodeType(ctx, dal.FweEcomReadQuery, []string{tagMetaList[0].TagCodeType})
	if err != nil {
		logs.CtxError(ctx, "[UpdateTagMeta] BatchGetTagValueByCodeType err:%v", err)
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	tagMetaUpdates := s.transTagMetaUpdates(req, tagMetaList[0])
	tagValueUpdates, newTagValueList, needDelTagValueIDs := s.transTagValueUpdates(req, tagValueList)

	if err = dal.FweEcomWriteQuery.Transaction(func(tx *mysql.Query) (err error) {
		if len(tagMetaUpdates) > 0 {
			if err = dal.UpdateTagMeta(ctx, tx, req.GetCode(), int32(req.GetTagType()), tagMetaUpdates); err != nil {
				logs.CtxError(ctx, "[UpdateTagMeta] UpdateTagMeta db err:%v", err)
				return
			}
		}

		if len(tagValueUpdates) > 0 {
			for id, updates := range tagValueUpdates {
				err = dal.UpdateTagValue(ctx, tx, id, updates)
				if err != nil {
					logs.CtxError(ctx, "[UpdateTagMeta] UpdateTagValue db err:%v", err)
					return
				}
			}
		}

		if len(newTagValueList) > 0 {
			if err = dal.BatchCreateTagValue(ctx, tx, newTagValueList); err != nil {
				logs.CtxError(ctx, "[UpdateTagMeta] BatchCreateTagValue db err:%v", err)
				return
			}
		}

		if len(needDelTagValueIDs) > 0 {
			if err = dal.DelTagValueByIDs(ctx, tx, needDelTagValueIDs); err != nil {
				logs.CtxError(ctx, "[UpdateTagMeta] DelTagValueByIDs db err:%v", err)
				return
			}
		}

		return
	}); err != nil {
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	resp.SetTagMetaId(tagMetaList[0].ID)
	return
}

func (s *UpdateTagMetaService) transTagMetaUpdates(input *category.UpdateTagMetaReq, oldTagMeta *do.TagMeta) map[string]interface{} {
	tagMetaUpdates := make(map[string]interface{})

	if input.GetName() != oldTagMeta.Name {
		tagMetaUpdates["name"] = input.GetName()
	}

	if input.GetDesc() != oldTagMeta.TagDesc {
		tagMetaUpdates["desc"] = input.GetDesc()
	}

	if input.GetExtra() != oldTagMeta.Extra {
		tagMetaUpdates["extra"] = input.GetExtra()
	}

	if len(tagMetaUpdates) > 0 && input.Operator != nil {
		tagMetaUpdates["operator_id"] = input.GetOperator().GetOperatorId()
		tagMetaUpdates["operator_name"] = input.GetOperator().GetOperatorName()
	}

	return tagMetaUpdates
}

func (s *UpdateTagMetaService) transTagValueUpdates(input *category.UpdateTagMetaReq, oldTagValueList []*do.TagValue) (
	map[int64]map[string]interface{}, []*do.TagValue, []int64) {

	valueID2TagValueUpdates := make(map[int64]map[string]interface{})
	newTagValueList := make([]*do.TagValue, 0)
	tagValue2TagValueItem := make(map[string]*do.TagValue)
	needDelTagValueIDs := make([]int64, 0)

	for _, v := range oldTagValueList {
		tagValue2TagValueItem[v.TagValue] = v
	}

	newTagValues := make([]string, 0, len(input.GetTagValueList()))

	for _, v := range input.GetTagValueList() {
		newTagValues = append(newTagValues, v.GetValue())
		if tagValueItem, ok := tagValue2TagValueItem[v.GetValue()]; ok {
			tagValueUpdates := make(map[string]interface{})
			if v.GetDesc() != tagValueItem.TagDesc {
				tagValueUpdates["desc"] = v.GetDesc()
			}

			if v.GetExtra() != tagValueItem.Extra {
				tagValueUpdates["extra"] = v.GetExtra()
			}

			if len(tagValueUpdates) > 0 && input.Operator != nil {
				tagValueUpdates["operator_id"] = input.GetOperator().GetOperatorId()
				tagValueUpdates["operator_name"] = input.GetOperator().GetOperatorName()
			}

			if len(tagValueUpdates) > 0 {
				valueID2TagValueUpdates[tagValueItem.ID] = tagValueUpdates
			}
		} else {
			newTagValueList = append(newTagValueList, &do.TagValue{
				TagCode:      input.GetCode(),
				TagValue:     v.GetValue(),
				TagDesc:      v.GetDesc(),
				OperatorID:   input.GetOperator().GetOperatorId(),
				OperatorName: input.GetOperator().GetOperatorName(),
				Extra:        v.GetExtra(),
				CreatedTime:  time.Now(),
				UpdatedTime:  time.Now(),
				TagCodeType:  fmt.Sprintf("%d_%s", input.GetTagType(), input.GetCode()),
			})
		}
	}

	for _, v := range oldTagValueList {
		if !slices.ContainsString(newTagValues, v.TagValue) {
			needDelTagValueIDs = append(needDelTagValueIDs, v.ID)
		}
	}

	return valueID2TagValueUpdates, newTagValueList, needDelTagValueIDs
}
