package handler

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/motor/fwe_category/internal/common/utils"
	"code.byted.org/motor/fwe_category/internal/service"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	"context"
	"golang.org/x/exp/slices"
)

type CommonRsp interface {
	SetBaseResp(*base.BaseResp)
}

func GenResp(ctx context.Context, rsp CommonRsp, bizErr *errdef.BizErr) {
	baseResp := base.NewBaseResp()
	if bizErr != nil {
		baseResp.StatusMessage = bizErr.Message()
		baseResp.StatusCode = bizErr.StatusCode()
		logs.CtxError(ctx, "[%s] errCode=%d, errMsg=%s", utils.GetMethod(ctx), bizErr.StatusCode(), bizErr.Message())
	}
	rsp.SetBaseResp(baseResp)
	return
}

func CheckPermission(ctx context.Context, op *category.Operator) bool {
	if env.IsBoe() {
		return true
	}
	if op == nil {
		return false
	}
	opNameList, _ := service.GetOperatorWhiteList(ctx)
	if slices.Contains(opNameList, op.OperatorName) {
		return true
	}
	return false
}
