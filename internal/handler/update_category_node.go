package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type UpdateCategoryNodeService struct {
}

func NewUpdateCategoryNodeService() *UpdateCategoryNodeService {
	return &UpdateCategoryNodeService{}
}

func (s *UpdateCategoryNodeService) Update(ctx context.Context, req *category.UpdateCategoryNodeReq) (resp *category.UpdateCategoryNodeResp) {
	resp = category.NewUpdateCategoryNodeResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	if req.CategoryId == 0 {
		bizErr = biz_err.ParamsErr.WithMessage("invalid category id")
		return
	}

	categories, err := dal.MGetCategory(ctx, []int64{req.CategoryId}, nil)
	if err != nil {
		logs.CtxError(ctx, "[UpdateCategoryNodeService] Update category, get category from db err:%v", err)
		bizErr = biz_err.DBErr.WithErr(err).WithMessage("update category to db error")
		return
	}

	if len(categories) == 0 || categories[0] == nil {
		bizErr = biz_err.DBErr.WithMessage("update category to db error, no record found by id")
		return
	}

	category := categories[0]

	if req.ParentId != nil && *req.ParentId == category.ID { // 不允许将自己设置为parent
		bizErr = biz_err.ParamsErr.WithMessage("update category denied, can't use self as parent")
		return
	}

	var oldParent, newParent *do.Category
	if req.ParentId != nil && *req.ParentId != category.ParentID { // 只有变更了parent的情况下需要把新旧parent捞出来

		ids := make([]int64, 0)
		if *req.ParentId != 0 {
			ids = append(ids, *req.ParentId)
		}
		if category.ParentID != 0 {
			ids = append(ids, category.ParentID)
		}

		categories, err := dal.MGetCategory(ctx, ids, nil)
		if err != nil {
			logs.CtxError(ctx, "[UpdateCategoryNodeService] Update category, get parent from db err:%v", err)
			bizErr = biz_err.DBErr.WithMessage("update category to db error").WithErr(err)
			return
		}
		for _, c := range categories {
			if c.ID == category.ParentID {
				oldParent = c
			}
			if c.ID == *req.ParentId {
				newParent = c
			}
		}

		if *req.ParentId != 0 && newParent == nil {
			bizErr = biz_err.DBErr.WithMessage("update category to db error, can't find parent record")
			return
		}
	}

	// 处理category
	columns := make(map[string]interface{})
	if req.AuditStatus != nil {
		columns["audit_status"] = *req.AuditStatus
	}
	if req.Name != nil && len(*req.Name) > 0 {
		columns["name"] = *req.Name
	}
	if req.Description != nil {
		columns["description"] = *req.Description
	}
	if req.Status != nil {
		columns["status"] = *req.Status
	}
	if req.Extra != nil {
		columns["extra"] = *req.Extra
	}
	if req.Sequence != nil {
		columns["sequence"] = *req.Sequence
	}

	if newParent != nil {
		columns["parent_id"] = *req.ParentId
		columns["level"] = newParent.Level + 1
	} else if req.ParentId != nil && *req.ParentId == 0 {
		columns["parent_id"] = *req.ParentId
		columns["level"] = 1
	}

	tOuterId := category.TOuterID
	if req.TOuterId != nil {
		tOuterId = *req.TOuterId
	} else if newParent != nil && len(newParent.TOuterID) > 0 {
		tOuterId = newParent.TOuterID
	}
	columns["t_outer_id"] = tOuterId
	if err := dal.FweEcomWriteQuery.Transaction(func(tx *mysql.Query) error {
		_, err := dal.UpdatesCategory(ctx, tx, []int64{category.ID}, columns)
		if err != nil {
			logs.CtxError(ctx, "[UpdateCategoryNodeService] Update category err:%v", err)
			return err
		}
		resp.CategoryId = category.ID

		if req.SyncSubTOuterId != nil && *req.SyncSubTOuterId {
			childrenIds, err := dal.QueryAllChildrenCategoryIDsByParentID(ctx, tx, category.ID)
			if err != nil {
				logs.CtxError(ctx, "[UpdateCategoryNodeService] Query children err:%v", err)
				return err
			}
			if len(childrenIds) > 0 {
				_, err = dal.UpdatesCategory(ctx, tx, childrenIds, map[string]interface{}{
					"t_outer_id": tOuterId,
				})
				if err != nil {
					logs.CtxError(ctx, "[UpdateCategoryNodeService] Update children err:%v", err)
					return err
				}
			}
		}

		if oldParent != nil && newParent != nil && oldParent.ID == newParent.ID { // 表示parent无变更
			return nil
		}

		if newParent != nil && newParent.IsLeaf == 1 { // 如果新的父类目是叶子节点, 需要把它变更为非叶子
			_, err = dal.UpdatesCategory(ctx, tx, []int64{newParent.ID}, map[string]interface{}{
				"is_leaf": 0,
			})
			if err != nil {
				logs.CtxError(ctx, "[UpdateCategoryNodeService] Update new parent category err:%v", err)
				return err
			}
		}
		if oldParent != nil { // 需要判断原来的父节点下面还有没有子节点,如果没有, 需要更新为叶子节点
			count, err := dal.CountChildrenCategoryByParentID(ctx, tx, oldParent.ID)
			if err != nil {
				logs.CtxError(ctx, "[UpdateCategoryNodeService] Update old parent category err:%v", err)
				return err
			}
			if count == 0 {
				oldParent.IsLeaf = 1
				_, err = dal.UpdatesCategory(ctx, tx, []int64{oldParent.ID}, map[string]interface{}{
					"is_leaf": 1,
				})
				if err != nil {
					logs.CtxError(ctx, "[UpdateCategoryNodeService] Update old parent category err:%v", err)
					return err
				}
			}
		}

		return nil
	}); err != nil {
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	return
}
