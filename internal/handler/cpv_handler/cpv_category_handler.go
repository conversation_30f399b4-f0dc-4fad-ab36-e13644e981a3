package cpv_handler

import (
	"code.byted.org/motor/fwe_category/internal/handler"
	"code.byted.org/motor/fwe_category/internal/service/cpv_service"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	"context"
)

type categoryHandler struct{}

var CategoryHandler = new(categoryHandler)

// QueryCategory implements the FweEcomCategoryServiceImpl interface.
func (s *categoryHandler) QueryCategory(ctx context.Context, req *category.QueryCategoryReq) (resp *category.QueryCategoryRsp) {
	resp = category.NewQueryCategoryRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	resp, bizErr = cpv_service.CategoryService.Query(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

func (s *categoryHandler) MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq) (resp *category.MGetAllLeafCategoryResp) {
	resp = category.NewMGetAllLeafCategoryResp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	resp, bizErr = cpv_service.CategoryService.MGetAllLeafCategory(ctx, req)
	if bizErr != nil {
		return
	}
	return
}
