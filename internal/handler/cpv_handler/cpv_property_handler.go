package cpv_handler

import (
	"code.byted.org/motor/fwe_category/internal/handler"
	"code.byted.org/motor/fwe_category/internal/service/cpv_service"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
)

type propertyHandler struct{}

var PropertyHandler = new(propertyHandler)

// CreateProperty implements the FweEcomCategoryServiceImpl interface.
func (s *propertyHandler) CreateProperty(ctx context.Context, req *category.CreatePropertyReq) (resp *category.CreatePropertyRsp) {
	resp = category.NewCreatePropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	if !handler.CheckPermission(ctx, req.Operator) {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("当前登录人无权限修改")
		return
	}

	bizErr = cpv_service.PropertyService.CreateProperty(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

// UpdateProperty implements the FweEcomCategoryServiceImpl interface.
func (s *propertyHandler) UpdateProperty(ctx context.Context, req *category.UpdatePropertyReq) (resp *category.UpdatePropertyRsp) {
	resp = category.NewUpdatePropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	if !handler.CheckPermission(ctx, req.Operator) {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("当前登录人无权限修改")
		return
	}

	bizErr = cpv_service.PropertyService.UpdateProperty(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

// UpsertProperty implements the FweEcomCategoryServiceImpl interface.
func (s *propertyHandler) UpsertProperty(ctx context.Context, req *category.UpsertPropertyReq) (resp *category.UpsertPropertyRsp) {
	resp = category.NewUpsertPropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	if !handler.CheckPermission(ctx, req.Operator) {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("当前登录人无权限修改")
		return
	}

	resp.Property, bizErr = cpv_service.PropertyService.UpsertProperty(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

func (s *propertyHandler) MGetProperty(ctx context.Context, req *category.MGetPropertyReq) (resp *category.MGetPropertyRsp) {
	resp = category.NewMGetPropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	resp.PropertyMap, bizErr = cpv_service.PropertyService.MGetProperty(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

// MDelProperty implements the FweEcomCategoryServiceImpl interface.
func (s *propertyHandler) MDelProperty(ctx context.Context, req *category.MDelPropertyReq) (resp *category.MDelPropertyRsp) {
	resp = category.NewMDelPropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	if !handler.CheckPermission(ctx, req.Operator) {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("当前登录人无权限修改")
		return
	}

	bizErr = cpv_service.PropertyService.MDelProperty(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

// QueryProperty implements the FweEcomCategoryServiceImpl interface.
func (s *propertyHandler) QueryProperty(ctx context.Context, req *category.QueryPropertyReq) (resp *category.QueryPropertyRsp) {
	resp = category.NewQueryPropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	resp, bizErr = cpv_service.PropertyService.Query(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

// ExportProperty implements the FweEcomCategoryServiceImpl interface.
func (s *propertyHandler) ExportProperty(ctx context.Context, req *category.ExportPropertyReq) (resp *category.ExportPropertyRsp) {
	resp = category.NewExportPropertyRsp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	resp, bizErr = cpv_service.PropertyService.Export(ctx, req)
	if bizErr != nil {
		return
	}
	return
}

func (s *propertyHandler) GetPropertyMeta(ctx context.Context, req *category.GetPropertyMetaReq) (resp *category.GetPropertyMetaResp) {
	resp = category.NewGetPropertyMetaResp()
	var bizErr *errdef.BizErr
	defer func() {
		handler.GenResp(ctx, resp, bizErr)
	}()

	resp, bizErr = cpv_service.PropertyService.GetPropertyMeta(ctx, req)
	if bizErr != nil {
		return
	}
	return
}
