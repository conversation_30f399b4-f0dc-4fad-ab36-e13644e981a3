package handler

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"github.com/google/uuid"
)

type CreateCategoryNodeService struct {
}

func NewCreateCategoryNodeService() *CreateCategoryNodeService {
	return &CreateCategoryNodeService{}
}

func (s *CreateCategoryNodeService) Create(ctx context.Context, req *category.CreateCategoryNodeReq) (resp *category.CreateCategoryNodeResp) {
	resp = category.NewCreateCategoryNodeResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr  *errdef.BizErr
		bizLine = req.GetBizLine()
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	if bizLine <= 0 {
		bizErr = biz_err.ParamsErr.WithMessage("invalid biz line")
		return
	}

	if len(req.Name) == 0 {
		bizErr = biz_err.ParamsErr.WithMessage("invalid name")
		return
	}

	// 如果有parent, 则获取数据
	var parent *do.Category
	if req.ParentId != nil && *req.ParentId != 0 {
		categories, err := dal.MGetCategoryWithIsTest(ctx, []int64{*req.ParentId}, nil, nil)
		if err != nil {
			logs.CtxError(ctx, "[CreateCategoryNodeService] Create category, get parent from db err:%v", err)
			bizErr = biz_err.DBErr.WithMessage("create category to db error").WithErr(err)
			return
		}
		if len(categories) > 0 {
			parent = categories[0]
		}
	}

	if parent != nil && parent.BizLine != int32(req.BizLine) {
		bizErr = biz_err.ParamsErr.WithMessage("invalid biz line, inconsistent with parent")
		return
	}

	category, parent := s.transCategory(req, parent)
	if err := dal.FweEcomWriteQuery.Transaction(func(tx *mysql.Query) error {
		id, err := dal.CreateCategory(ctx, tx, category)
		if err != nil {
			logs.CtxError(ctx, "[CreateCategoryNodeService] Create category err:%v", err)
			return err
		}

		if req.CategoryKey == nil || len(*req.CategoryKey) == 0 {
			_, err = dal.UpdatesCategory(ctx, tx, []int64{category.ID}, map[string]interface{}{
				"category_key": fmt.Sprintf("%d", id),
			})
			if err != nil {
				logs.CtxError(ctx, "[CreateCategoryNodeService] Update category key err:%v", err)
				return err
			}
		}
		if parent != nil {
			_, err = dal.UpdatesCategory(ctx, tx, []int64{parent.ID}, map[string]interface{}{
				"is_leaf": parent.IsLeaf,
			})
			if err != nil {
				logs.CtxError(ctx, "[CreateCategoryNodeService] Update parent category err:%v", err)
				return err
			}
		}

		resp.CategoryId = id
		return nil
	}); err != nil {
		bizErr = biz_err.ServerException.WithErr(err)
		return
	}

	return
}

func (s *CreateCategoryNodeService) transCategory(req *category.CreateCategoryNodeReq, parent *do.Category) (category *do.Category, updateParent *do.Category) {
	category = &do.Category{
		Name:         req.Name,
		Description:  req.Description,
		Status:       req.Status,
		TOuterID:     req.TOuterId,
		AuditStatus:  req.AuditStatus,
		IsDelete:     0,
		Extra:        req.Extra,
		BizLine:      int32(req.BizLine),
		Sequence:     1, // 默认顺序 1
		CategoryType: req.CategoryType,
	}
	if req.IsTest != nil && *req.IsTest {
		category.IsTest = 1
	}
	if req.CategoryKey != nil && len(*req.CategoryKey) > 0 {
		category.CategoryKey = *req.CategoryKey
	} else {
		category.CategoryKey = uuid.NewString() // 暂定ID, 后面会用category id覆盖更新
	}

	if parent != nil {
		category.ParentID = *req.ParentId
		category.Level = parent.Level + 1
		category.IsLeaf = 1

		if parent.IsLeaf == 1 {
			parent.IsLeaf = 0
			updateParent = parent
			return
		}
	} else {
		category.ParentID = 0
		category.Level = 1
		category.IsLeaf = 0
	}
	return
}
