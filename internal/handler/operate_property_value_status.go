package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type OperatePropertyValueStatusService struct {
}

func NewOperatePropertyValueStatusService() *OperatePropertyValueStatusService {
	return &OperatePropertyValueStatusService{}
}

func (s *OperatePropertyValueStatusService) OperatePropertyValueStatus(ctx context.Context, req *category.OperatePropertyValueStatusReq) (resp *category.OperatePropertyValueStatusResp) {
	resp = category.NewOperatePropertyValueStatusResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		bizErr *errdef.BizErr
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	if err := dal.UpdatePropertyValueStatus(ctx, dal.FweEcomWriteQuery, req.CategoryId, req.ShopId, req.PropertyValueId, req.Status); err != nil {
		logs.CtxError(ctx, "[OperatePropertyValueStatus] UpdatePropertyValueStatus err: %+v", err)
		bizErr = biz_err.DBErr.WithErr(err).WithMessage("更新属性值状态失败")
		return
	}

	return
}
