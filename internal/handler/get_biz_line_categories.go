package handler

import (
	"context"

	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"

	"code.byted.org/gopkg/logs"
	model "code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/base"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

type GetBizLineCategoriesService struct {
}

func NewGetBizLineCategoriesService() *GetBizLineCategoriesService {
	return &GetBizLineCategoriesService{}
}

func (s *GetBizLineCategoriesService) GetBizLineCategories(ctx context.Context, req *category.GetBizLineCategoriesReq) (resp *category.GetBizLineCategoriesResp) {
	resp = category.NewGetBizLineCategoriesResp()
	resp.BaseResp = base.NewBaseResp()

	var (
		categoryList = make([]*category.CategoryBase, 0)
		bizErr       *errdef.BizErr
		bizLine      = req.GetBizLine()
	)

	defer func() {
		bizErr.KitexResponse(ctx, resp)
	}()

	resp.CategoryList = categoryList

	if bizLine <= 0 {
		bizErr = biz_err.ParamsErr.WithMessage("invalid biz line")
		return
	}
	var (
		categories []*model.Category
		err        error
	)
	isTest := int32(0)
	if req.IsTest != nil && *req.IsTest {
		isTest = 1
	}
	if req.CategorySearchReq != nil {
		categories, err = dal.SearchCategoryListByBizLine(ctx, req, isTest)
		if err != nil {
			logs.CtxError(ctx, "[CheHouGetCategoryListByBizLine] get category list by biz line err:%v", err)
			bizErr = biz_err.DBErr.WithMessage("get category from db by biz line err").WithErr(err)
			return
		}
	} else {
		categories, err = dal.GetCategoryListByBizLine(ctx, int32(req.GetBizLine()), req.GetNeedAll(), req.CategoryType, isTest)
		if err != nil {
			logs.CtxError(ctx, "[GetBizLineCategoriesService] get category list by biz line err:%v", err)
			bizErr = biz_err.DBErr.WithMessage("get category from db by biz line err").WithErr(err)
			return
		}
	}

	resp.CategoryList = s.packCategoryList(categories)

	return
}

func (s *GetBizLineCategoriesService) packCategoryList(categories []*model.Category) (categoryList []*category.CategoryBase) {
	for _, cate := range categories {
		categoryList = append(categoryList, &category.CategoryBase{
			Id:           cate.ID,
			Name:         cate.Name,
			ParentId:     cate.ParentID,
			Level:        int64(cate.Level),
			IsLeaf:       cate.IsLeaf,
			Description:  cate.Description,
			Status:       cate.Status,
			TOuterId:     cate.TOuterID,
			CreateTime:   cate.CreateTime.Unix(),
			UpdateTime:   cate.UpdateTime.Unix(),
			AuditStatus:  cate.AuditStatus,
			Extra:        cate.Extra,
			CategoryKey:  cate.CategoryKey,
			CategoryType: cate.CategoryType,
		})
	}

	return
}
