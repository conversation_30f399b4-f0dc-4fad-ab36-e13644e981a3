package converter

import (
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/common/utils"
	model "code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

type PO2DO struct {
}

func NewPO2DO() *PO2DO {
	return &PO2DO{}
}

// TranCategory 将Category打包成CategoryItem
func (p *PO2DO) TranCategory(categoryInfo *model.Category) *category.CategoryBase {
	if categoryInfo == nil {
		return nil
	}
	categoryBase := category.NewCategoryBase()

	categoryBase.Id = categoryInfo.ID
	categoryBase.Name = categoryInfo.Name
	categoryBase.ParentId = categoryInfo.ParentID
	categoryBase.Level = int64(categoryInfo.Level)
	categoryBase.Description = categoryInfo.Description
	categoryBase.Status = categoryInfo.Status
	categoryBase.TOuterId = categoryInfo.TOuterID
	categoryBase.CreateTime = categoryInfo.CreateTime.Unix()
	categoryBase.UpdateTime = categoryInfo.UpdateTime.Unix()
	categoryBase.AuditStatus = categoryInfo.AuditStatus
	categoryBase.Extra = categoryInfo.Extra
	categoryBase.CategoryKey = categoryInfo.CategoryKey
	categoryBase.IsTest = categoryInfo.IsTest == 1
	categoryBase.BizLine = categoryInfo.BizLine
	categoryBase.IsLeaf = categoryInfo.IsLeaf
	categoryBase.Sequence = categoryInfo.Sequence

	return categoryBase
}

func (p *PO2DO) TranProperty(propertyInfo *model.Property) *category.PropertyItem {
	return &category.PropertyItem{
		Id:                    propertyInfo.ID,
		Name:                  propertyInfo.Name,
		UniqueKey:             propertyInfo.UniqueKey,
		Alias:                 propertyInfo.Alias_,
		Status:                propertyInfo.Status,
		Source:                propertyInfo.Source,
		GroupKey:              propertyInfo.GroupKey,
		GroupName:             propertyInfo.GroupName,
		FieldType:             propertyInfo.FieldType,
		InputType:             propertyInfo.InputType,
		IsRequired:            propertyInfo.IsRequired,
		Type:                  propertyInfo.Type,
		Sequence:              propertyInfo.Sequence,
		ContainPropertyValues: propertyInfo.ContainPropertyValues == 1,
		UniqueConstraint:      propertyInfo.UniqueConstraint,
		CategoryId:            propertyInfo.CategoryID,
		CreateTime:            propertyInfo.CreateTime.Unix(),
		UpdateTime:            propertyInfo.UpdateTime.Unix(),
		CreateUid:             propertyInfo.CreateUID,
		UpdateUid:             propertyInfo.UpdateUID,
		Feature:               utils.UnmarshalStrToValue(propertyInfo.Feature, map[string]string{}),
		IsTest:                int64(propertyInfo.IsTest),
	}
}

func (p *PO2DO) MTranPropertyItem(propertyItemList []*model.Property) []*category.PropertyItem {
	return gslice.Map(propertyItemList, func(propertyItem *model.Property) *category.PropertyItem {
		return p.TranProperty(propertyItem)
	})
}

func (p *PO2DO) TranPropertyValue(propertyValInfo *model.PropertyValue) *category.PropertyValue {
	if propertyValInfo == nil {
		return nil
	}

	return &category.PropertyValue{
		Id:          propertyValInfo.ID,
		Name:        propertyValInfo.Name,
		Alias:       propertyValInfo.Alias_,
		Status:      propertyValInfo.Status,
		OutId:       propertyValInfo.OutID,
		ParentId:    propertyValInfo.ParentValueID,
		Sequence:    propertyValInfo.Sequence,
		PropertyKey: propertyValInfo.PropertyKey,
		PropertyId:  propertyValInfo.PropertyID,
		MarketPrice: propertyValInfo.MarketPrice,
		Price:       propertyValInfo.Price,
		CategoryId:  propertyValInfo.CategoryID,
		ShopId:      propertyValInfo.ShopID,
		IsTest:      propertyValInfo.IsTest,
		SpuId:       propertyValInfo.SpuID,
		CreateTime:  propertyValInfo.CreateTime.Unix(),
		UpdateTime:  propertyValInfo.UpdateTime.Unix(),
		CreateUid:   propertyValInfo.CreateUID,
		UpdateUid:   propertyValInfo.UpdateUID,
		Feature:     utils.UnmarshalStrToValue(propertyValInfo.Feature, map[string]string{}),
	}
}

func (p *PO2DO) MTransPropertyValue(propertyValList []*model.PropertyValue) []*category.PropertyValue {
	return gslice.Map(propertyValList, func(propertyValDB *model.PropertyValue) *category.PropertyValue {
		return p.TranPropertyValue(propertyValDB)
	})
}
