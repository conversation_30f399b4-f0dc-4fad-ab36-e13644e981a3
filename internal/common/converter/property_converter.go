package converter

import (
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/common/utils"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

type propertyConverter struct{}

var Property = new(propertyConverter)

func (s *propertyConverter) Do2Dto(input *do.ProductProperty, catS []*do.ProductCategoryPropertyRel, tenantS []*do.ProductTenantPropertyRel) (output *category.Property) {
	if input == nil {
		return
	}
	output = &category.Property{
		PropertyKey:  input.PropertyKey,
		PropertyType: category.PropertyType(input.PropertyType),
		FieldType:    utils.IgnoreErr(category.FieldTypeFromString(input.FieldType)),
		CreateTime:   input.CreateTime.Unix(),
		UpdateTime:   input.UpdateTime.Unix(),
		Description:  input.Description,
		Status:       category.CommonStatus(input.Status),
		Name:         input.Name,
		ClassType:    utils.IgnoreErr(category.ClassTypeFromString(input.ClassType)),
		ValueDesc:    utils.UnmarshalStrToValue(input.ValueDesc, &category.PropertyValueDesc{}),
		PropertyRelation: &category.PropRelation{
			CategoryIdList: gslice.Map(catS, func(x *do.ProductCategoryPropertyRel) int64 { return x.CategoryID }),
			TenantTypeList: gslice.Map(tenantS, func(x *do.ProductTenantPropertyRel) int64 { return x.TenantType }),
		},
		PropertyConfig: utils.UnmarshalStrToValue(input.PropertyConfig, &category.PropertyConfig{}),
		Creator: &category.Operator{
			OperatorId:   input.CreateUID,
			OperatorName: input.CreateName,
		},
		Updater: &category.Operator{
			OperatorId:   input.UpdateUID,
			OperatorName: input.UpdateName,
		},
	}
	return
}

func (s *propertyConverter) BatchProductDo2Dto(doList []*do.ProductProperty) []*category.Property {
	result := make([]*category.Property, 0, len(doList))

	for _, propDo := range doList {
		dto := s.Do2Dto(propDo, nil, nil)
		if dto != nil {
			result = append(result, dto)
		}
	}

	return result
}
