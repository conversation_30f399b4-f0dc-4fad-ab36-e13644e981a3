package converter

import (
	"code.byted.org/motor/fwe_category/internal/common/utils"
	model "code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"time"
)

type DO2PO struct {
}

func NewDO2PO() *DO2PO {
	return &DO2PO{}
}
func (p *DO2PO) PropertyVal(propertyVal *category.PropertyValue) *model.PropertyValue {
	return &model.PropertyValue{
		ID:            propertyVal.Id,
		CategoryID:    propertyVal.CategoryId,
		PropertyID:    propertyVal.PropertyId,
		PropertyKey:   propertyVal.PropertyKey,
		ShopID:        propertyVal.ShopId,
		SpuID:         propertyVal.SpuId,
		ParentValueID: propertyVal.ParentId,
		Name:          propertyVal.Name,
		Alias_:        propertyVal.Alias,
		Status:        propertyVal.Status,
		OutID:         propertyVal.OutId,
		Sequence:      propertyVal.Sequence,
		Price:         propertyVal.Price,
		MarketPrice:   propertyVal.MarketPrice,
		CreateTime:    time.Unix(propertyVal.CreateTime, 0),
		UpdateTime:    time.Unix(propertyVal.UpdateTime, 0),
		CreateUID:     propertyVal.CreateUid,
		UpdateUID:     propertyVal.UpdateUid,
		Feature:       utils.MarshalValueToStr(propertyVal.Feature),
		IsTest:        propertyVal.IsTest,
	}
}

func (p *DO2PO) PropertyItem(propertyItem *category.PropertyItem) *model.Property {
	return &model.Property{
		ID:               propertyItem.Id,
		CategoryID:       propertyItem.CategoryId,
		Name:             propertyItem.Name,
		Alias_:           propertyItem.Alias,
		Status:           propertyItem.Status,
		Source:           propertyItem.Source,
		GroupKey:         propertyItem.GroupKey,
		GroupName:        propertyItem.GroupName,
		UniqueKey:        propertyItem.UniqueKey,
		UniqueConstraint: propertyItem.UniqueConstraint,
		InputType:        propertyItem.InputType,
		FieldType:        propertyItem.FieldType,
		IsRequired:       propertyItem.IsRequired,
		Type:             propertyItem.Type,
		Sequence:         propertyItem.Sequence,
		CreateTime:       time.Unix(propertyItem.CreateTime, 0),
		UpdateTime:       time.Unix(propertyItem.CreateTime, 0),
		CreateUID:        propertyItem.CreateUid,
		UpdateUID:        propertyItem.UpdateUid,
		Feature:          utils.MarshalValueToStr(propertyItem.Feature),
		IsTest:           int32(propertyItem.IsTest),
	}
}
