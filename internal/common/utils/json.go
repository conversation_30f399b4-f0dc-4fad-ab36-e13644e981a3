package utils

import (
	"bytes"
	"strings"

	"github.com/bytedance/sonic"
)

func MarshalValueToStr[T any](value T, api ...sonic.API) string {
	if len(api) == 0 || api[0] == nil {
		str, _ := sonic.MarshalString(value)
		return str
	}
	str, _ := api[0].MarshalToString(value)
	return str
}

func MarshalValue[T any](value T) []byte {
	bytes, _ := sonic.Marshal(value)
	return bytes
}

var api = sonic.Config{
	UseNumber: true,
}.Froze()

func UnmarshalStrToValue[T any](str string, value T) T {
	var zero T
	var reader = strings.NewReader(str)
	if err := api.NewDecoder(reader).Decode(&value); err != nil {
		return zero
	}
	return value
}

func UnmarshalToValuePtr[T any](byteList []byte, valuePtr T) error {
	return api.NewDecoder(bytes.NewReader(byteList)).Decode(valuePtr)
}

func DeepCopy[T any](origin T) (target T) {
	var res T
	data, _ := sonic.Marshal(origin)
	_ = sonic.Unmarshal(data, &res)
	return res
}
