package utils

import (
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"github.com/go-sql-driver/mysql"
	"github.com/pkg/errors"
)

func IsDuplicate(err error) bool {
	var mysqlErr *mysql.MySQLError
	if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
		return true
	}

	return false
}

func WrapErr(err error) *errdef.BizErr {
	if bizErr, ok := err.(*errdef.BizErr); ok {
		return bizErr
	} else {
		return motor_fwe_ecom_category.ServerException.WithErr(err)
	}
}
