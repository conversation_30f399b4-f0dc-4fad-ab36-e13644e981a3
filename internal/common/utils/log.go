package utils

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/pkg/rpcinfo"
	"context"
	"github.com/bytedance/sonic"
)

func PrintInfoLog(ctx context.Context, format string, value interface{}) {
	data, _ := sonic.Marshal(value)
	logs.CtxInfo(ctx, format, string(data))
}

// GetMethod 获取调用方法
func GetMethod(ctx context.Context) string {
	info := rpcinfo.GetRPCInfo(ctx)
	if info != nil && info.To() != nil {
		return info.To().Method()
	}
	return ""
}

func IgnoreErr[T any](input T, err error) T {
	return input
}
