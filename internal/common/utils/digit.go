package utils

import (
	"strconv"
	"strings"

	"github.com/pkg/errors"
)

// Float642Int64 万 转 分
// 例如 输入 22.64 返回 22640000
func Float642Int64(amount string) (int64, error) {
	var res int64

	// 1. 校验输入为有效的float64
	_, err := strconv.ParseFloat(amount, 64)
	if err != nil {
		return 0, errors.WithMessage(err, "invalid float64")
	}

	// 2. 根据小数点分割, list[0]为整数位，list[1]为小数位
	digitList := strings.Split(amount, ".")
	if len(digitList) == 0 {
		return 0, errors.New("invalid float64")
	}

	// 3. 整数位转换 万
	digitWan, err := strconv.ParseInt(digitList[0], 10, 64)
	if err != nil {
		return 0, errors.WithMessage(err, "invalid int64")
	}
	res = res + digitWan*10000

	// 4. 小数位转换 元
	if len(digitList) == 2 {
		str := digitList[1]
		if len(str) > 4 {
			// 截断只支持 4位小数，到元级别
			str = str[:4]
		}
		// 补齐4位小数
		for i := len(str); i < 4; i++ {
			str = str + "0"
		}
		// 转换为int64
		digitYuan, err := strconv.ParseInt(str, 10, 64)
		if err != nil {
			return 0, errors.WithMessage(err, "invalid int64")
		}
		// 加和
		res = res + digitYuan
	}

	// 元转分
	res = res * 100
	return res, nil
}

func Float642Int64IgnoreErr(amount string) int64 {
	fen, _ := Float642Int64(amount)
	return fen
}
