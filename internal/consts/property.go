package consts

// PropertySource 类目属性来源
type PropertySource = int32

const (
	PropertySource_Common       PropertySource = 0 // 通用来源-DB
	PropertySource_Car_Brand    PropertySource = 1 // 车型库品牌
	PropertySource_Car_SubBrand PropertySource = 2 // 车型库子品牌
	PropertySource_Car_Series   PropertySource = 3 // 车型库车系
	PropertySource_Car_Car      PropertySource = 4 // 车型库车款
	PropertySource_Province     PropertySource = 5 // 省份
	PropertySource_City         PropertySource = 6 // 城市
)

// CarGroupKeyMap 车型库通用属性聚合key
var CarGroupKeyMap = map[PropertySource]string{
	PropertySource_Car_Brand:    "brand_id",
	PropertySource_Car_SubBrand: "sub_brand_id",
	PropertySource_Car_Series:   "series_id",
	PropertySource_Car_Car:      "car_id",
}

// CarFilterKeyMap 车型库通用属性筛选key
var CarFilterKeyMap = map[PropertySource]string{
	PropertySource_Car_Series: "brand_id",
	PropertySource_Car_Car:    "series_id",
}
