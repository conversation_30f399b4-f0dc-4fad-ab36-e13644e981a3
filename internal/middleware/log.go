package middleware

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/pkg/endpoint"
	"code.byted.org/kite/kitex/pkg/rpcinfo"
	"github.com/bytedance/sonic"
)

// LogMiddleware 日志中间件
func LogMiddleware(next endpoint.Endpoint) endpoint.Endpoint {
	return func(ctx context.Context, request interface{}, response interface{}) error {
		rpcInfo := rpcinfo.GetRPCInfo(ctx)
		ctx = logs.NewNoticeCtx(ctx)
		reqStr, _ := sonic.Marshal(request)
		logs.CtxPushNotice(ctx, "from", rpcInfo.From().ServiceName())
		logs.CtxPushNotice(ctx, "to", rpcInfo.To().ServiceName())
		logs.CtxPushNotice(ctx, "method", rpcInfo.To().Method())
		logs.CtxPushNotice(ctx, "req", string(reqStr))
		err := next(ctx, request, response)
		respStr, _ := sonic.Marshal(response)
		logs.CtxPushNotice(ctx, "resp", string(respStr))
		logs.CtxFlushNotice(ctx)
		return err
	}
}
