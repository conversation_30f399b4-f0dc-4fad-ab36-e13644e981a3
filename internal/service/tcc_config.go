package service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/biz"
	"code.byted.org/motor/fwe_category/pkg/tcc"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"encoding/json"
)

func GetAllCitiesMap(ctx context.Context) (map[string]*biz.City, error) {
	allCitiesMap := make(map[string]*biz.City)
	result, err := tcc.CategoryTCCConfig.Get(ctx, "all_cities")
	if err != nil {
		logs.CtxError(ctx, "[GetAllCitiesMap] get tcc err:%v", err)
		return allCitiesMap, err
	}

	err = json.Unmarshal([]byte(result), &allCitiesMap)
	if err != nil {
		logs.CtxError(ctx, "[GetAllCitiesMap] unmarshal allCitiesMap err:%v", err)
		return allCitiesMap, err
	}

	return allCitiesMap, nil
}

type CategoryWhiteList struct {
	CategoryIDList   []int64  `json:"category_id_list"`
	OperatorNameList []string `json:"operator_name_list"`
}

func GetOperatorWhiteList(ctx context.Context) (operatorNameList []string, bizErr *errdef.BizErr) {
	str, err := tcc.CategoryTCCConfig.Get(ctx, "category_white_list")
	if err != nil {
		bizErr = motor_fwe_ecom_category.ServerException.WithErr(err)
		return
	}
	var cfg CategoryWhiteList
	_ = json.Unmarshal([]byte(str), &cfg)
	operatorNameList = cfg.OperatorNameList
	return
}
