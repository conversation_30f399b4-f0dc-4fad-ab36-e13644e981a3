package service

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/motor_car_agg_sort/kitex_gen/motor/agg_sort"
	aggSort "code.byted.org/overpass/motor_car_agg_sort/kitex_gen/motor/car/agg_sort"
	"code.byted.org/overpass/motor_car_agg_sort/rpc/motor_car_agg_sort"
)

func Search(ctx context.Context, group *agg_sort.Group, filters []*agg_sort.FilterClause) (total int32, hasMore bool, itemList []*agg_sort.ItemInfo, err error) {
	resp, err := motor_car_agg_sort.RawCall.Search(ctx, &aggSort.SearchRequest{
		Params: &agg_sort.Params{
			Group:   group,
			Filters: filters,
		},
	}, calloption.WithReqRespLogsInfo())

	if err != nil {
		logs.CtxError(ctx, "[Search] rpc err:%v", err)
		return
	}

	total = resp.GetResult_().GetTotal()
	hasMore = resp.GetResult_().GetHasMore()
	itemList = resp.GetResult_().GetItemList()

	return
}
