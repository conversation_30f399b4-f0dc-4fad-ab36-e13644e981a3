package service

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/common/option/calloption"
	"code.byted.org/overpass/motor_car_base/kitex_gen/motor/car_base"
	"code.byted.org/overpass/motor_car_base/rpc/motor_car_base"
)

func GetCarProperty(ctx context.Context, ids []int32, idType car_base.IdTypeEnum, propertyKeys []string) (
	map[int32]car_base.PropertyMap, error) {
	req := &car_base.GetCarPropertyRequest{
		Ids:          ids,
		IdType:       idType,
		PropertyKeys: propertyKeys,
	}

	resp, err := motor_car_base.RawCall.GetCarProperty(ctx, req, calloption.WithReqRespLogsInfo())
	if err != nil {
		logs.CtxError(ctx, "[GetCarProperty] rpc err:%v", err)
		return nil, err
	}

	return resp.GetPropertyData(), nil
}
