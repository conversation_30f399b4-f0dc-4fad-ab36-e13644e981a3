package cpv_service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/service/cpv_service/cache"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
)

type CacheDataAccessStrategy struct{}

func NewCacheDataAccessStrategy() DataAccessStrategy {
	return &CacheDataAccessStrategy{}
}

func (s *CacheDataAccessStrategy) GetCategoriesByIDs(ctx context.Context, categoryIDs []int64) ([]*do.Category, *errdef.BizErr) {
	var categoryList []*do.Category
	_, err := cache.MGetCategoryCache.MGet(ctx, categoryIDs, &categoryList)
	if err != nil {
		logs.CtxWarn(ctx, "[CacheDataAccessStrategy] GetCategoriesByIDs from cache failed, err: %v", err)
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
	}
	categoryList = gslice.Filter(categoryList, func(category *do.Category) bool { return category != nil })
	return categoryList, nil
}

func (s *CacheDataAccessStrategy) GetCategoriesByParentIDs(ctx context.Context, parentCategoryIDs []int64) ([]*do.Category, *errdef.BizErr) {
	result := new([][]*do.Category)
	_, err := cache.QueryCategoryCache.MGet(ctx, parentCategoryIDs, result)
	if err != nil {
		logs.CtxError(ctx, "[CacheDataAccessStrategy] GetCategoriesByParentIDs from cache failed, err: %+v", err)
		return nil, biz_err.DBErr.WithErr(err).WithMessage("根据父类目ID获取类目失败")
	}
	categoryList := gslice.Uniq(gslice.Flatten(*result))
	categoryList = gslice.Filter(categoryList, func(category *do.Category) bool { return category != nil })
	return categoryList, nil
}

func (s *CacheDataAccessStrategy) GetPropertiesByKeys(ctx context.Context, propertyKeys []string) ([]*do.ProductProperty, *errdef.BizErr) {
	var doList []*do.ProductProperty
	_, err := cache.QueryPropertyCache.MGet(ctx, propertyKeys, &doList)
	if err != nil {
		logs.CtxError(ctx, "[CacheDataAccessStrategy] GetPropertiesByKeys from cache failed, err=%s", err.Error())
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取属性失败")
	}
	doList = gslice.Filter(doList, func(property *do.ProductProperty) bool { return property != nil })
	return doList, nil
}

func (s *CacheDataAccessStrategy) GetPropertyRelationsByTenantIDs(ctx context.Context, tenantTypeIDs []int64) ([]*do.ProductTenantPropertyRel, *errdef.BizErr) {
	result := new([][]*do.ProductTenantPropertyRel)
	_, err := cache.TenantPropertyRelCache.MGet(ctx, tenantTypeIDs, result)
	if err != nil {
		logs.CtxError(ctx, "[CacheDataAccessStrategy] GetPropertyRelationsByTenantIDs from cache failed, err=%s", err.Error())
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取租户属性关系失败")
	}
	relList := gslice.Uniq(gslice.Flatten(*result))
	return relList, nil
}

func (s *CacheDataAccessStrategy) GetPropertyRelationsByCategoryIDs(ctx context.Context, categoryIDs []int64) ([]*do.ProductCategoryPropertyRel, *errdef.BizErr) {
	result := new([][]*do.ProductCategoryPropertyRel)
	_, err := cache.CategoryPropertyRelCache.MGet(ctx, categoryIDs, result)
	if err != nil {
		logs.CtxError(ctx, "[CacheDataAccessStrategy] GetPropertyRelationsByCategoryIDs from cache failed, err=%s", err.Error())
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取类目属性关系失败")
	}
	relList := gslice.Uniq(gslice.Flatten(*result))
	return relList, nil
}

func (s *CacheDataAccessStrategy) GetAllParentIDs(ctx context.Context, categoryIDs []int64) ([]int64, *errdef.BizErr) {
	parentIDs := new([][]int64)
	_, err := cache.GetAllParentIDsCache.MGet(ctx, categoryIDs, parentIDs)
	if err != nil {
		logs.CtxError(ctx, "[CacheDataAccessStrategy] GetAllParentIDs from cache failed, err=%s", err.Error())
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取类目所有父类ID失败")
	}
	allIDs := gslice.Uniq(gslice.Flatten(*parentIDs))
	return allIDs, nil
}
