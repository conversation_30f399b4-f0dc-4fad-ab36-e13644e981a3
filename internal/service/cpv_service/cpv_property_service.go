package cpv_service

import (
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/lang/v2/mathx"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/common/utils"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/model/dto"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"fmt"
	"golang.org/x/exp/slices"
	"gorm.io/gorm"
	"time"
)

type propertyService struct{}

var PropertyService = new(propertyService)

func (s *propertyService) CreateProperty(ctx context.Context, req *category.CreatePropertyReq) (bizErr *errdef.BizErr) {
	var (
		needValueDescList = []category.FieldType{
			category.FieldType_EnumV, category.FieldType_StructV, category.FieldType_ArrayV,
		}
		oldDo *do.ProductProperty
	)
	// 参数
	if req == nil || req.Operator == nil || len(req.Operator.OperatorId) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("operator_id is nil")
		return
	}
	if slices.Contains(needValueDescList, req.FieldType) {
		if req.ValueDesc == nil || len(req.ValueDesc.DescTxt) == 0 {
			bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("需要定义属性值描述")
			return
		}
		// todo 校验thrift格式

	}

	// 查找
	oldDo, bizErr = dal.ProductProperty.GetOne(ctx, nil, req.PropertyKey, false)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	if oldDo != nil {
		msg := fmt.Sprintf("property_key=%s 已存在，请去编辑", req.PropertyKey)
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage(msg)
		return
	}

	// 更新
	upsertReq := &category.UpsertPropertyReq{
		PropertyKey:    req.PropertyKey,
		PropertyType:   req.PropertyType,
		FieldType:      req.FieldType,
		Description:    &req.Description,
		Status:         &req.Status,
		Name:           &req.Name,
		ClassType:      &req.ClassType,
		ValueDesc:      req.ValueDesc,
		RelParam:       req.RelParam,
		PropertyConfig: req.PropertyConfig,
		Operator:       req.Operator,
	}
	_, bizErr = s.UpsertProperty(ctx, upsertReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	return
}

func (s *propertyService) UpdateProperty(ctx context.Context, req *category.UpdatePropertyReq) (bizErr *errdef.BizErr) {
	var (
		needValueDescList = []string{
			category.FieldType_EnumV.String(), category.FieldType_StructV.String(), category.FieldType_ArrayV.String(),
		}
		oldDo *do.ProductProperty
	)
	// 参数
	if req == nil || req.Operator == nil || len(req.Operator.OperatorId) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("operator_id is nil")
		return
	}

	// 查
	oldDo, bizErr = dal.ProductProperty.GetOne(ctx, nil, req.PropertyKey, false)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	if oldDo == nil {
		msg := fmt.Sprintf("property_key=%s 未找到，无法编辑", req.PropertyKey)
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage(msg)
		return
	}

	if slices.Contains(needValueDescList, oldDo.FieldType) {
		if req.ValueDesc == nil || len(req.ValueDesc.DescTxt) == 0 {
			bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("需要定义属性值描述")
			return
		}
		// todo 校验thrift格式

	}

	// 更新
	upsertReq := &category.UpsertPropertyReq{
		PropertyKey:    req.PropertyKey,
		Description:    req.Description,
		Status:         req.Status,
		Name:           req.Name,
		ClassType:      req.ClassType,
		ValueDesc:      req.ValueDesc,
		RelParam:       req.RelParam,
		PropertyConfig: req.PropertyConfig,
		Operator:       req.Operator,
	}
	_, bizErr = s.UpsertProperty(ctx, upsertReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	return
}

func (s *propertyService) UpsertProperty(ctx context.Context, req *category.UpsertPropertyReq) (rsp *category.Property, bizErr *errdef.BizErr) {
	var (
		newDo        *do.ProductProperty
		newCatRel    []*do.ProductCategoryPropertyRel
		newTenantRel []*do.ProductTenantPropertyRel
		err          error
	)
	// 参数
	if req == nil || req.Operator == nil || len(req.Operator.OperatorId) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("operator_id is nil")
		return
	}

	// 事务
	err = mysql.FweEcomDB().Transaction(func(tx *gorm.DB) error {
		var (
			txErr   *errdef.BizErr // 这里一定要返回 txErr 格式
			oldDto  = &dto.Property{}
			catS    []*do.ProductCategoryPropertyRel
			tenantS []*do.ProductTenantPropertyRel
		)

		/********************************************* read *************************************/
		// 获取历史属性，加锁
		oldDto.Do, txErr = dal.ProductProperty.GetOne(ctx, tx, req.PropertyKey, true)
		if txErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", txErr.Error())
			return txErr
		}
		// 获取历史类目关系
		catS, txErr = dal.CategoryPropertyRel.Query(ctx, tx, &dal.CatRelQueryParam{PropertyKeyS: []string{req.PropertyKey}, Limit: 10})
		if txErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", txErr.Error())
			return txErr
		}
		newCatRel = catS
		// 获取历史租户关系
		tenantS, txErr = dal.TenantPropRel.Query(ctx, tx, &dal.TenantRelQueryParam{PropertyKeyS: []string{req.PropertyKey}, Limit: 10})
		if txErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", txErr.Error())
			return txErr
		}
		newTenantRel = tenantS

		/********************************************* inner *************************************/
		// 模型转换
		oldDto.Dto = converter.Property.Do2Dto(oldDto.Do, catS, tenantS)
		newDo = s.buildPropertyDo(req, oldDto)

		/********************************************* write *************************************/
		// 保存属性
		txErr = dal.ProductProperty.Save(ctx, tx, newDo)
		if txErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", txErr.Error())
			return txErr
		}

		// 商品属性关系更新
		if rp := req.RelParam; rp != nil && len(rp.CategoryIdList) > 0 && newDo.PropertyType == int32(category.PropertyType_Property) {
			param := &CatPropRelParam{OldRelS: catS, PropertyKey: req.PropertyKey, CategoryIDs: rp.CategoryIdList, Operator: req.Operator}
			newCatRel, txErr = CatPropRel.UpsertWithTx(ctx, tx, param)
			if txErr != nil {
				logs.CtxError(ctx, "[PropertyService] err=%s", txErr.Error())
				return txErr
			}
		}
		// 商品标签关系更新
		if rp := req.RelParam; rp != nil && len(rp.TenantTypeList) > 0 && newDo.PropertyType == int32(category.PropertyType_Tag) {
			param := &TenantPropRelParam{OldRelS: tenantS, PropertyKey: req.PropertyKey, TenantTypeS: rp.TenantTypeList, Operator: req.Operator}
			newTenantRel, txErr = TagTenantRel.UpsertWithTx(ctx, tx, param)
			if txErr != nil {
				logs.CtxError(ctx, "[PropertyService] err=%s", txErr.Error())
				return txErr
			}
		}

		return nil
	})
	if err != nil {
		bizErr = utils.WrapErr(err)
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}

	rsp = converter.Property.Do2Dto(newDo, newCatRel, newTenantRel)
	return
}

func (s *propertyService) MGetProperty(ctx context.Context, req *category.MGetPropertyReq) (mp map[string]*category.Property, bizErr *errdef.BizErr) {
	if req == nil || len(req.PropertyKeyList) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("PropertyKeyList is nil")
		return
	}
	var (
		propS     = make(map[string]*do.ProductProperty)
		catMap    = make(map[string][]*do.ProductCategoryPropertyRel)
		tenantMap = make(map[string][]*do.ProductTenantPropertyRel)
	)
	mp = make(map[string]*category.Property)
	propS, bizErr = dal.ProductProperty.MGet(ctx, nil, req.PropertyKeyList)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	if req.NeedCategoryRel {
		var catS []*do.ProductCategoryPropertyRel
		catS, bizErr = dal.CategoryPropertyRel.Query(ctx, nil, &dal.CatRelQueryParam{PropertyKeyS: req.PropertyKeyList, Limit: 300})
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}
		for _, v := range catS {
			catMap[v.PropertyKey] = append(catMap[v.PropertyKey], v)
		}
	}
	if req.NeedTenantRel {
		var tenantS []*do.ProductTenantPropertyRel
		tenantS, bizErr = dal.TenantPropRel.Query(ctx, nil, &dal.TenantRelQueryParam{PropertyKeyS: req.PropertyKeyList, Limit: 300})
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}
		for _, v := range tenantS {
			tenantMap[v.PropertyKey] = append(tenantMap[v.PropertyKey], v)
		}
	}
	for propKey, propDo := range propS {
		x := catMap[propKey]
		y := tenantMap[propKey]
		w := converter.Property.Do2Dto(propDo, x, y)
		if w == nil {
			continue
		}
		mp[propKey] = w
	}
	return
}

func (s *propertyService) Export(ctx context.Context, req *category.ExportPropertyReq) (rsp *category.ExportPropertyRsp, bizErr *errdef.BizErr) {
	rsp = category.NewExportPropertyRsp()
	var queryRsp *category.QueryPropertyRsp
	queryReq := &category.QueryPropertyReq{
		PropertyKeyList:    req.PropertyKeyList,
		NameKeyword:        req.NameKeyword,
		CategoryIdList:     req.CategoryIdList,
		TenantTypeList:     req.TenantTypeList,
		DescriptionKeyword: req.DescriptionKeyword,
		Limit:              1,
	}
	// 第一次查，先确定数量
	queryRsp, bizErr = s.Query(ctx, queryReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	if queryRsp.Total > 500 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("导出数量不能超过500条")
		return
	}
	// 第二次查，返回内容
	queryReq.Limit = queryRsp.Total
	queryRsp, bizErr = s.Query(ctx, queryReq)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	// 返回数据格式处理
	var titleStr = []string{"属性编码", "属性名称", "属性类型", "字段类型", "属性值描述", "属性配置", "属性描述", "属性状态", "属性领域", "更新人", "更新时间", "创建人", "创建时间"}
	rsp.StrData = append(rsp.StrData, titleStr)
	for _, v := range queryRsp.PropertyList {
		// 导出字段处理
		if strList := s.buildStrList(v); len(strList) > 0 {
			rsp.StrData = append(rsp.StrData, strList)
		}
	}
	return
}

func (s *propertyService) Query(ctx context.Context, req *category.QueryPropertyReq) (rsp *category.QueryPropertyRsp, bizErr *errdef.BizErr) {
	rsp = category.NewQueryPropertyRsp()
	queryParam := &dal.PropQueryParam{
		PropertyKeyList:    req.PropertyKeyList,
		NameKeyword:        req.NameKeyword,
		DescriptionKeyword: req.DescriptionKeyword,
		Limit:              mathx.MaxInteger(int(req.Limit), 1),
		Offset:             int(conv.PtrToValueOrDefault(req.Offset, 0)),
		NeedTotal:          true,
	}

	// 子查询
	if len(req.CategoryIdList) > 0 {
		// 向上继承
		var allCategoryIDs []int64
		allCategoryIDs, bizErr = CategoryService.GetAllParentIDs(ctx, req.CategoryIdList)
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}

		// 类目属性关系
		relParam := &dal.CatRelQueryParam{
			CategoryIDs: allCategoryIDs,
			Limit:       500,
		}
		var relList []*do.ProductCategoryPropertyRel
		relList, bizErr = dal.CategoryPropertyRel.Query(ctx, nil, relParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}
		pkList := gslice.Map(relList, func(x *do.ProductCategoryPropertyRel) string { return x.PropertyKey })
		queryParam.PropertyKeyList = append(queryParam.PropertyKeyList, pkList...)
	}
	if len(req.TenantTypeList) > 0 {
		// 类目属性关系
		relParam := &dal.TenantRelQueryParam{
			TenantTypeS: req.TenantTypeList,
			Limit:       500,
		}
		var relList []*do.ProductTenantPropertyRel
		relList, bizErr = dal.TenantPropRel.Query(ctx, nil, relParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}
		pkList := gslice.Map(relList, func(x *do.ProductTenantPropertyRel) string { return x.PropertyKey })
		queryParam.PropertyKeyList = append(queryParam.PropertyKeyList, pkList...)
	}
	// 去重
	queryParam.PropertyKeyList = slicex.Distinct(queryParam.PropertyKeyList)

	var doList []*do.ProductProperty
	doList, rsp.Total, bizErr = dal.ProductProperty.Query(ctx, nil, queryParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	for _, v := range doList {
		w := converter.Property.Do2Dto(v, nil, nil)
		if w == nil {
			continue
		}
		rsp.PropertyList = append(rsp.PropertyList, w)
	}
	rsp.HasMore = rsp.Total > int64(queryParam.Limit+queryParam.Offset)
	return
}

func (s *propertyService) MDelProperty(ctx context.Context, req *category.MDelPropertyReq) (bizErr *errdef.BizErr) {
	// param
	if req == nil || len(req.PropertyKeyList) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("PropertyKeyList is nil")
		return
	}
	if req.Operator == nil || len(req.Operator.OperatorId) == 0 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("OperatorID is nil")
		return
	}
	if len(req.PropertyKeyList) > 20 {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("len(PropertyKeyList) 超过20个")
		return
	}

	// do
	bizErr = dal.ProductProperty.MDel(ctx, nil, req.PropertyKeyList, req.Operator)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}

	return
}

func (s *propertyService) buildStrList(input *category.Property) (strList []string) {
	if input == nil {
		return
	}
	var (
		descStr    string
		cfgStr     string
		updater    string
		creator    string
		updateTime time.Time
		createTime time.Time
	)
	if input.ValueDesc != nil {
		descStr = input.ValueDesc.DescTxt
	}
	if input.PropertyConfig != nil {
		cfgStr = utils.MarshalValueToStr(input.PropertyConfig)
	}
	if input.Updater != nil {
		updater = input.Updater.OperatorName
	}
	if input.Creator != nil {
		creator = input.Creator.OperatorName
	}
	updateTime = time.Unix(input.UpdateTime, 0)
	createTime = time.Unix(input.CreateTime, 0)

	// 返回
	strList = append(
		strList,
		input.PropertyKey,
		input.Name,
		input.PropertyType.String(),
		input.FieldType.String(),
		descStr,
		cfgStr,
		input.Description,
		input.Status.String(),
		input.ClassType.String(),
		updater,
		updateTime.Format("2006-01-02 15:04:05"),
		creator,
		createTime.Format("2006-01-02 15:04:05"),
	)
	return
}

func (s *propertyService) buildPropertyDo(req *category.UpsertPropertyReq, withOld *dto.Property) (newDo *do.ProductProperty) {
	if p := withOld; p != nil && p.Do != nil {
		newDo = dal.ProductProperty.DeepCopy(p.Do)
		if newDo.PropertyConfig == "" {
			newDo.PropertyConfig = "{}"
		}
	} else {
		newDo = &do.ProductProperty{
			PropertyKey:    req.PropertyKey,
			PropertyType:   int32(req.PropertyType),
			FieldType:      req.FieldType.String(),
			Status:         int32(conv.PtrToValueOrDefault(req.Status, category.CommonStatus_InUse)),
			ValueDesc:      "{}",
			PropertyConfig: "{}",
		}
		if op := req.Operator; op != nil {
			newDo.CreateUID = op.OperatorId
			newDo.CreateName = op.OperatorName
		}
	}
	if status := req.Status; status != nil {
		newDo.Status = int32(*status)
	}
	if name := req.Name; name != nil {
		newDo.Name = *name
	}
	if d := req.Description; d != nil {
		newDo.Description = *d
	}
	if ct := req.ClassType; ct != nil {
		newDo.ClassType = ct.String()
	}
	if vp := req.ValueDesc; vp != nil {
		newDo.ValueDesc = utils.MarshalValueToStr(vp)
	}
	if pc := req.PropertyConfig; pc != nil {
		newDo.PropertyConfig = utils.MarshalValueToStr(pc)
	}
	if op := req.Operator; op != nil {
		newDo.UpdateUID = op.OperatorId
		newDo.UpdateName = op.OperatorName
	}
	return
}

func (s *propertyService) GetPropertyMeta(ctx context.Context, req *category.GetPropertyMetaReq) (resp *category.GetPropertyMetaResp, bizErr *errdef.BizErr) {
	resp = category.NewGetPropertyMetaResp()
	dataAccessStrategy := GlobalStrategyFactory.GetStrategy(req.ReadStrategy)
	var propertyKeyList []string
	if len(req.GetCategoryIdList()) > 0 {
		// 获取父类类目 ID
		var allCategoryIDs []int64
		allCategoryIDs, bizErr = dataAccessStrategy.GetAllParentIDs(ctx, req.CategoryIdList)
		if bizErr != nil {
			logs.CtxError(ctx, "[GetPropertyMeta] err=%s", bizErr.Error())
			return
		}
		logs.CtxInfo(ctx, "[GetPropertyMeta] allCategoryIDs=%+v", allCategoryIDs)
		// 获取类目属性关系
		var catRelList []*do.ProductCategoryPropertyRel
		catRelList, bizErr = dataAccessStrategy.GetPropertyRelationsByCategoryIDs(ctx, allCategoryIDs)
		if bizErr != nil {
			logs.CtxError(ctx, "[GetPropertyMeta] err=%s", bizErr.Error())
			return
		}
		logs.CtxInfo(ctx, "[GetPropertyMeta] catRelList=%+v", catRelList)
		propertyKeyList = append(propertyKeyList,
			gslice.Map(catRelList, func(x *do.ProductCategoryPropertyRel) string { return x.PropertyKey })...,
		)
	}
	if len(req.GetTenantTypeList()) > 0 {
		// 获取租户属性关系
		var tenantRelList []*do.ProductTenantPropertyRel
		tenantRelList, bizErr = dataAccessStrategy.GetPropertyRelationsByTenantIDs(ctx, req.GetTenantTypeList())
		if bizErr != nil {
			logs.CtxError(ctx, "[GetPropertyMeta] err=%s", bizErr.Error())
			return
		}
		logs.CtxInfo(ctx, "[GetPropertyMeta] tenantRelList=%+v", tenantRelList)
		propertyKeyList = append(propertyKeyList,
			gslice.Map(tenantRelList, func(x *do.ProductTenantPropertyRel) string { return x.PropertyKey })...,
		)
	}

	// 去重
	propertyKeyList = gslice.Uniq(propertyKeyList)
	var propertyList []*category.Property
	if len(propertyKeyList) > 0 {
		// 根据属性key获取属性详情
		var doList []*do.ProductProperty
		doList, bizErr = dataAccessStrategy.GetPropertiesByKeys(ctx, propertyKeyList)
		if bizErr != nil {
			logs.CtxError(ctx, "[GetPropertyMeta] err=%s", bizErr.Error())
			return
		}
		// 转换属性DO为DTO
		propertyList = gslice.Map(doList, func(x *do.ProductProperty) *category.Property {
			return converter.Property.Do2Dto(x, nil, nil)
		})
	}
	// 设置响应
	resp.SetPropertyList(propertyList)
	return resp, nil
}
