package cpv_service

import (
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/lang/v2/mathx"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/gvalue"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	"context"
	"gorm.io/gen/field"
)

type categoryService struct{}

var CategoryService = new(categoryService)

func (s *categoryService) Query(ctx context.Context, req *category.QueryCategoryReq) (rsp *category.QueryCategoryRsp, bizErr *errdef.BizErr) {
	rsp = &category.QueryCategoryRsp{}
	queryParam := &dal.CategoryQueryParam{
		CategoryIDList:       req.CategoryIdList,
		BizLineList:          req.BizLineList,
		ParentCategoryIdList: req.ParentCategoryIdList,
		NameKeyword:          req.NameKeyword,
		Level:                req.Level,
		Limit:                mathx.MaxInteger(int(req.Limit), 1),
		Offset:               int(conv.PtrToValueOrDefault(req.Offset, 0)),
		NeedTotal:            true,
	}
	var doList []*do.Category
	doList, rsp.Total, bizErr = dal.Category.Query(ctx, nil, queryParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
		return
	}
	for _, v := range doList {
		w := converter.NewPO2DO().TranCategory(v)
		if w == nil {
			continue
		}
		rsp.CategoryList = append(rsp.CategoryList, w)
	}
	rsp.HasMore = rsp.Total > int64(queryParam.Limit+queryParam.Offset)
	return
}

func (s *categoryService) GetAllParentIDs(ctx context.Context, categoryIDs []int64) (allIDs []int64, bizErr *errdef.BizErr) {
	var (
		dataList []*do.Category
		selectS  = []field.Expr{mysql.Category.ID, mysql.Category.ParentID}
		thisIDs  = categoryIDs
	)
	// 最多三层查询，这里5是兜底之后会新增
	for i := 0; i < 5; i++ {
		thisIDs = slicex.Filter(slicex.Distinct(thisIDs), gvalue.IsNotZero[int64])
		if len(thisIDs) == 0 {
			break
		}
		allIDs = append(allIDs, thisIDs...)
		queryParam := &dal.CategoryQueryParam{
			SelectColumns:  selectS,
			CategoryIDList: thisIDs,
			Limit:          len(thisIDs),
		}
		dataList, _, bizErr = dal.Category.Query(ctx, nil, queryParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[PropertyService] err=%s", bizErr.Error())
			return
		}
		thisIDs = gslice.Map(dataList, func(x *do.Category) int64 { return x.ParentID })
	}
	return
}

func (s *categoryService) MGetAllLeafCategory(ctx context.Context, req *category.MGetAllLeafCategoryReq) (resp *category.MGetAllLeafCategoryResp, bizErr *errdef.BizErr) {
	resp = category.NewMGetAllLeafCategoryResp()
	var (
		maxLevel             = 3
		leafCategory         []*do.Category
		newParentCategoryIds = make([]int64, 0)
		dataAccessStrategy   = GlobalStrategyFactory.GetStrategy(req.ReadStrategy)
	)
	// 1. 查询当前Category
	var categoryList []*do.Category
	categoryList, bizErr = dataAccessStrategy.GetCategoriesByIDs(ctx, req.GetCategoryIdList())
	if bizErr != nil {
		logs.CtxError(ctx, "[MGetAllLeafCategory] GetCategoriesByIDs err: %+v", bizErr)
		return
	}
	for _, item := range categoryList {
		if item != nil && item.IsLeaf == 1 {
			leafCategory = append(leafCategory, item)
		} else {
			if item != nil && item.ID != 0 {
				newParentCategoryIds = append(newParentCategoryIds, item.ID)
			}
		}
	}

	for i := 0; i < maxLevel && len(newParentCategoryIds) > 0; i++ {
		var categoryList []*do.Category
		categoryList, bizErr = dataAccessStrategy.GetCategoriesByParentIDs(ctx, newParentCategoryIds)
		if bizErr != nil {
			logs.CtxError(ctx, "[MGetAllLeafCategory] GetCategoriesByParentIDs err: %+v", bizErr)
			return
		}
		logs.CtxInfo(ctx, "[MGetAllLeafCategory] categoryList: %+v", categoryList)
		newParentCategoryIds = make([]int64, 0)
		for _, cat := range categoryList {
			if cat.IsLeaf == 1 {
				leafCategory = append(leafCategory, cat)
			} else if cat.ID != 0 {
				newParentCategoryIds = append(newParentCategoryIds, cat.ID)
			}
		}

		if len(newParentCategoryIds) == 0 {
			break
		}
	}
	leafCategoryList := gslice.Map(leafCategory, func(x *do.Category) *category.CategoryBase { return converter.NewPO2DO().TranCategory(x) })
	resp.SetLeafCategoryList(leafCategoryList)
	return resp, nil
}
