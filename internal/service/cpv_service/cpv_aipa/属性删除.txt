在 “属性管理” 列表中，每行属性上的 “删除” 操作，请使用 API 接口 POST http://bpi-boe.dcarlife.net/motor/fwe_ecom/category/property/mdelete/
使用样例如下：
输入参数：
{
    "property_key_list": [
        "gxd_test"
    ],
    "operator": {
        "operator_id": "0",
        "operator_name": "AIPA 系统"
    }
}
其中，property_key_list 传入多个属性编码，operator 为操作人，operator_id 写死 0，operator_name 写死 AIPA 系统。
返回参数：
{
    "message": "",
    "status": 0
}
返回值处理方式和 property/upsert/ 一样