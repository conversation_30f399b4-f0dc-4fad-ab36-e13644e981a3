在 “类目管理 “的列表右上角，增加 “新增类目” 按钮，点击后弹出表单。
表单使用 API 接口 POST http://bpi-boe.dcarlife.net/motor/fwe_ecom/category/category/create/ 接口使用样例如下：
输入参数：
{
    "biz_line": 28,
    "name": "郭晓东测试",
    "parent_id": 9205,
    "description": "测试",
    "status": 1,
    "audit_status": 1,
    "operator": {
        "operator_id": "1",
        "operator_name": "郭晓东"
    },
    "is_test": true
}
其中 biz_line 为业务线参数，必填；
name 为类目名称，必填；
parend_id 为父类目 ID，若当前为一级类目，则填 0。若当前为二到四级类目，则使用当前类目节点的类目 ID；
description 为类目描述；
status 为类目状态，和列表上状态规则一致；
audit_status 为类目审核状态，和列表上 “审核状态” 规则一致；
is_test 为类目是否为测试，和列表上 “是否测试” 规则一致；
operator 为当前登录态人员信息，其中 operator_id 为当前登录人工号，operator_name 为当前登录人姓名；
输出参数：
{
    "message": "",
    "status": 0
}
其中，先检查 status 是否为 0，为 0 表示接口请求成功；
不为 0 则把 message 信息抛给用户；