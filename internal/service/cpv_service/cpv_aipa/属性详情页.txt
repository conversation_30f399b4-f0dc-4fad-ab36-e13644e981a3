重构“属性详情”页面，页面分为“基本信息”，“属性关系”，“属性值配置”，“属性相关配置”四个模块。
页面字段填充使用外部API接口，
POST ${apiDomain}/motor/fwe_ecom/category/property/mget/ apiDomain的取值逻辑和之前保持一致。
接口使用样例如下：
输入参数:
{
    "property_key_list": [
        "属性编码"
    ],
    "need_category_rel": true,
    "need_tenant_rel": true
}
其中，property_key_list为属性编码列表参数，使用当前属性详情页的属性编码填充；
need_category_rel为是否获取当前属性和类目的关系，写死传true即可。
need_tenant_rel为是否获取当前属性和租户的关系，写死传true即可。
返回参数：
{
    "data": {
        "property_map": {
            "属性编码": {
                "property_key": "属性编码",
                "property_type": 1,
                "field_type": 6,
                "status": 1,
                "name": "属性名称",
                "description": "属性描述",
                "class_type": 2,
                "update_time": 1747658540,
                "create_time": 1747636173,
                "creator": {
                    "operator_id": "创建人工号",
                    "operator_name": "创建人名称"
                },
                "updater": {
                    "operator_id": "更新人工号",
                    "operator_name": "更新人名称"
                },
                "property_relation": {
                    "category_id_list": [
                        1,
                        555
                    ],
                    "tenant_type_list": [
                        1,
                        28
                    ]
                },
                "value_desc":{
                    "desc_txt": "属性值thrift描述代码"
                },
                "property_config":{
                    "need_snapshot": true,
                    "need_log": true,
                    "conf_list": [
                        {
                            "index_field_name": "索引名称",
                            "index_type_list": [
                                "Product_Common_ES_To_C",
                                "Mpu_Common_ES_To_C",
                                "Sku_Common_ClickHouse",
                                "Sku_Common_Krypton"
                            ],
                            "search_type_list": [
                                "ProductProperty",
                                "SkuProperty"
                            ],
                            "json_path": "field_path.sub_field_path"
                        }
                    ]
                }
            }
        }
    },
    "message": "",
    "status": 0
}
其中参数描述如下：

其中，先判断status是否为0，若不为0，则表示接口请求错误，将message的内容抛给用户。
若为0，则表示接口请求成功，继续解析data数据；

1、属性“基本信息”模块：
property_map 为属性编码与属性详情数据的键值对，其中键就是输入参数中对应的属性编码；
property_key 为属性编码，与对应键相同；
property_type 为商品属性分类，映射规则为 1:"商品描述属性"，2:"商品附着属性"；
field_type 为属性值类型，映射规则为 1:"字符串"，2:"整型"，3:"浮点数"，4:"时间戳"，5:"布尔"，6:"枚举"，7:"结构体"；
name 为属性名称；
description 为属性描述；
class_type 为属性领域分类，映射规则为 1:"交易"，2:"履约"，99:"其他"；
update_time 为属性更新时间，格式为时间戳，请正确转换为日期24小时制；
create_time 为属性创建时间，格式为时间戳，请正确转换为日期24小时制；
status 为属性状态，映射规则为 1:"使用中"，2:"已停用"；
creator 为创建人，里面operator_id为工号，operator_name 为姓名；
updater 为更新人，里面operator_id为工号，operator_name 为姓名；

2、“属性关系”模块：
property_relation 为属性与其他实体（类目，租户）之间的关系，里面category_id_list为类目ID列表。tenant_type_list 为租户类型列表。
当商品属性分类为“商品描述属性”时，即property_type=1时，只展示类目ID列表，
当商品属性分类为“商品附着属性”时，即property_type=2时，只展示租户类型列表；

3、“属性值配置”模块：
value_desc 为属性值描述，里面只有一个desc_txt字符串，其内容是一段thrift格式代码。
当属性值类型为“枚举”或“结构体”或“数组”时，即field_type in (6、7、8)时，展示该thrift代码输入框，请正确展示高亮thrift代码；

4、“属性相关配置模块”：
property_config 为属性相关配置，其中，
need_snapshot 表示当前属性是否启用快照，当商品属性分类为“商品附着属性”时，即property_type=2时，展示该字段;
need_log 表示当前属性是否启用日志，当商品属性分类为“商品附着属性”时，即property_type=2时，展示该字段;
conf_list 表示当前属性的索引配置列表，其中，
index_field_name 表示当前属性的索引名称；
index_type_list 为属性对应的索引种类，多选，可选集合为“Product_Common_ES_To_C”，“Mpu_Common_ES_To_C”，“Sku_Common_ClickHouse”，“Sku_Common_Krypton”，反显可编辑；
search_type_list 为属性的搜索类型，多选，可选集合为“ProductProperty”，“SkuProperty”，反显可编辑；
json_path 为属性路径；
