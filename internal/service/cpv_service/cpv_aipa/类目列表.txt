在左侧Tab新增 “类目管理” Tab，点击类目管理 Tab 后，展示类目列表，使用 API 接口 POST http://bpi-boe.dcarlife.net/motor/fwe_ecom/category/category/query/
接口使用样例如下：
输入参数：
{
    "biz_line_list": [
        28
    ],
    "parent_category_id_list": [
        9858
    ],
    "name_keyword": "酒店",
    "level": 1,
    "category_id_list": [
        9855
    ],
    "limit": 10,
    "offset": 0
}
其中，biz_line_list 为业务线列表，支持在类目列表中输入整数进行筛选；
parent_category_id_list 为上级类目 ID，当点击列表中的一行类目时，用这行的类目 ID 去请求其对应的下级类目数据；
name_keyword 为类目名称，支持在类目列表上输入框进行筛选；
level 为类目层级，默认使用 1，表示一级类目，当点击一级类目请求二级类目时，请使用 level=2 请求接口，以此类推，最多支持四级类目。
category_id_list 为类目ID列表，支持在类目列表输入整数进行筛选；
limit 为当前页的类目数量；
offset 为当前页的偏移量；
输出参数：
{
    "data": {
        "category_list": [
            {
                "is_test": false,
                "level": 3,
                "is_leaf": 0,
                "description": "经济型酒店",
                "name": "经济型酒店",
                "id": 9858,
                "biz_line": 28,
                "audit_status": 1,
                "status": 1,
                "update_time": 1725889282,
                "create_time": 1725889282,
                "category_type": 0
            }
        ],
        "total": 3,
        "has_more": false
    },
    "message": "",
    "status": 0
}
其中，优先查看 status 不为 0 时，则认为接口请求错误，将 message 内容抛给用户。
当 status 为 0 时，即接口请求成功；
其中，total 表示当前筛选条件下的类目总数。
has_more 表示是否还有下一页。
category_list 为类目列表数据，里面每一个元素都是一个类目；
is_test 表示当前类目是否为测试类目；
level 表示当前类目的层级，1 表示一级类目，2 表示二级类目，一次类推，最多支持四级类目；
is_leaf 表示当前类目是否为叶子类目，叶子类目不可点击；
description 为类目描述，纯文案，限制 400 个字符；
name 为类目名称，限制 50 个字符；
id 为类目 ID，是当前类目的主键；
biz_line 是当前类目的的业务线；
audit_status 表示当前类目的审核状态，1：“审核通过”，0：“待审核”；
status 为当前类目的状态，1:“启用中”，0:“已停用”；
category_type 为当前类目的类目分类；
update_time 为当前类目的更新时间，格式为时间戳；
create_time 为当前类目的创建时间，格式为时间戳；