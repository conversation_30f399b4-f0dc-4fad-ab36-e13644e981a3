重构“属性列表”模块，
页面字段填充逻辑使用外部API接口，
POST ${apiDomain}/motor/fwe_ecom/category/property/query/ apiDomain的取值逻辑和之前保持一致。
使用样例：
输入：
{
    "property_key_list": [
        "属性编码"
    ],
    "category_id_list":[
        555
    ],
    "tenant_type_list":[
        1
    ],
    "name_keyword": "属性名称关键字",
    "description_keyword": "属性描述关键字",
    "limit": 10,
    "offset": 0,
}
其中，property_key_list 为属性编码列表， 支持输入英文和下划线来筛选属性；
category_id_list 为类目ID列表，支持输入一个类目ID来筛选属性；
tenant_type_list 为租户ID列表，支持输入一个租户ID来筛选属性；
name_keyword 为属性名称关键字，支持输入框筛选；
description_keyword 为属性描述关键字，支持输入框筛选；
limit 为当前页的属性数量;
offset 为偏移量;
输出：
{
    "data": {
        "property_list": [
            {
                "property_key": "test_integer_2",
                "property_type": 1,
                "field_type": 2,
                "status": 1,
                "name": "测试-整型-2",
                "description": "属性描述",
                "class_type": 2,
                "create_time": 1747713362,
                "update_time": 1747906539,
                "creator": {
                    "operator_id": "5591569",
                    "operator_name": "郭晓东"
                },
                "updater": {
                    "operator_id": "5591569",
                    "operator_name": "郭晓东"
                }
            }
        ],
        "total": 11,
        "has_more": true
    },
    "message": "",
    "status": 0
}
其中，优先查看status, 不为0时，则认为接口请求错误，将message内容抛给用户。
当status为0时，即接口请求成功，继续解析data数据；
total 表示当前筛选条件下的属性总数；
has_more表示是否还有下一页；

property_list是返回的属性列表数据，每个元素里面，
property_key为属性编码；
property_type 为商品属性分类，映射规则为 1:"商品描述属性"，2:"商品附着属性"；
field_type 为属性值类型，映射规则为 1:"字符串"，2:"整型"，3:"浮点数"，4:"时间戳"，5:"布尔"，6:"枚举"，7:"结构体"，8:"数组"；
status 为属性状态，映射规则为 1:"使用中"，2:"已停用"；
name为属性名称；
description 为属性描述，反显可编辑；
class_type 为属性领域分类，映射规则为 1:"交易"，2:"履约"，99:"其他"；
update_time 为属性更新时间，格式为时间戳，请正确转换为日期24小时制；
create_time 为属性创建时间，格式为时间戳，请正确转换为日期24小时制；
creator 为创建人，里面operator_id为工号，operator_name 为姓名；
updater 为更新人，里面operator_id为工号，operator_name 为姓名；

