在 “类目管理 “列表，每行类目数据最右侧，增加操作 - 编辑按钮。点击类目编辑按钮，弹出编辑表单，样式和类目新增表单一致，表单数据先用列表上类目数据填充，其中业务线（biz_line），类目 ID（category_id）不可编辑，其余字段都可编辑
表单使用新增的 API 接口 POST http://bpi-boe.dcarlife.net/motor/fwe_ecom/category/category/update/ 接口使用样例如下：
输入参数：
{
    "category_id": 9864,
    "name": "郭晓东测试一级类目变更",
    "description": "测试",
    "parent_id": 9855,
    "status": 1,
    "audit_status": 1,
    "operator": {
        "operator_id": "1",
        "operator_name": "郭晓东"
    }
}
其中，name 为类目名称；
parend_id 为父类目 ID，输入只能为整数；
description 为类目描述；
status 为类目状态，和列表上状态规则一致；
audit_status 为类目审核状态，和列表上 “审核状态” 规则一致；
is_test 为类目是否为测试，和列表上 “是否测试” 规则一致；
operator 为当前登录态人员信息，其中 operator_id 为当前登录人工号，operator_name 为当前登录人姓名；
输出参数：
{
    "message": "",
    "status": 0
}
其中，先检查 status 是否为 0，为 0 表示接口请求成功；
不为 0 则把 message 信息抛给用户；
注意，改动不要影响已有的 API 接口逻辑。