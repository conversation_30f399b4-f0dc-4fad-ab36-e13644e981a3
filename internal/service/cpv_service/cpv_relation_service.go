package cpv_service

import (
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	motor_fwe_ecom_category "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
	"gorm.io/gorm"
	"time"
)

type catPropRel struct{}
type tagTenantRel struct{}

var CatPropRel = new(catPropRel)
var TagTenantRel = new(tagTenantRel)

type CatPropRelParam struct {
	PropertyKey string
	CategoryIDs []int64
	Operator    *category.Operator
	OldRelS     []*do.ProductCategoryPropertyRel
}

func (s *catPropRel) diffInOnePropKey(param *CatPropRelParam) (delRelS []int64, addRelS, newRelS []*do.ProductCategoryPropertyRel) {
	var (
		catMap      = make(map[int64]bool)
		matchCatMap = make(map[int64]bool)
	)
	for _, v := range param.CategoryIDs {
		catMap[v] = true
	}
	for _, v := range param.OldRelS {
		if catMap[v.CategoryID] {
			matchCatMap[v.CategoryID] = true
			newRelS = append(newRelS, v)
			continue
		}
		delRelS = append(delRelS, v.CategoryID)
	}
	for k := range catMap {
		if matchCatMap[k] {
			continue
		}
		w := &do.ProductCategoryPropertyRel{
			CategoryID:  k,
			PropertyKey: param.PropertyKey,
			Status:      int32(category.CommonStatus_InUse),
			CreatedTime: time.Now(),
			UpdatedTime: time.Now(),
		}
		if op := param.Operator; op != nil {
			w.CreateUID = op.OperatorId
			w.CreateName = op.OperatorName
			w.UpdateUID = op.OperatorId
			w.UpdateName = op.OperatorName
		}
		addRelS = append(addRelS, w)
	}
	newRelS = append(newRelS, addRelS...)
	return
}

func (s *catPropRel) UpsertWithTx(ctx context.Context, db *gorm.DB, param *CatPropRelParam) (newS []*do.ProductCategoryPropertyRel, bizErr *errdef.BizErr) {
	if db == nil {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("need db")
		return
	}
	if param == nil || param.Operator == nil {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("operator is nil")
		return
	}
	var (
		delS []int64
		addS []*do.ProductCategoryPropertyRel
	)

	// 计算差异
	delS, addS, newS = s.diffInOnePropKey(param)

	// write
	// 新增
	if len(addS) > 0 {
		bizErr = dal.CategoryPropertyRel.MCreate(ctx, db, addS)
		if bizErr != nil {
			logs.CtxError(ctx, "[RelationService] err=%s", bizErr.Error())
			return
		}
	}

	// 删除
	if len(delS) > 0 {
		bizErr = dal.CategoryPropertyRel.Update(ctx, db, &dal.RelUpdateParam{
			WherePropertyKeyS: []string{param.PropertyKey},
			WhereCategoryIDs:  delS,
			UpdateDeleteTime:  conv.Ptr(time.Now().Unix()),
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[RelationService] err=%s", bizErr.Error())
			return
		}
	}
	return
}

type TenantPropRelParam struct {
	PropertyKey string
	TenantTypeS []int64
	Operator    *category.Operator
	OldRelS     []*do.ProductTenantPropertyRel
}

func (s *tagTenantRel) UpsertWithTx(ctx context.Context, db *gorm.DB, param *TenantPropRelParam) (newS []*do.ProductTenantPropertyRel, bizErr *errdef.BizErr) {
	if db == nil {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("need db")
		return
	}
	if param == nil || param.Operator == nil {
		bizErr = motor_fwe_ecom_category.ParamsErr.WithMessage("operator is nil")
		return
	}
	var (
		delS []int64
		addS []*do.ProductTenantPropertyRel
	)

	// 计算差异
	delS, addS, newS = s.diffInOnePropKey(param)

	// write
	// 新增
	if len(addS) > 0 {
		bizErr = dal.TenantPropRel.MCreate(ctx, db, addS)
		if bizErr != nil {
			logs.CtxError(ctx, "[RelationService] err=%s", bizErr.Error())
			return
		}
	}

	// 删除
	if len(delS) > 0 {
		bizErr = dal.TenantPropRel.Update(ctx, db, &dal.TenantRelUpdateParam{
			WherePropertyKeyS: []string{param.PropertyKey},
			WhereTenantTypeS:  delS,
			UpdateDeleteTime:  conv.Ptr(time.Now().Unix()),
		})
		if bizErr != nil {
			logs.CtxError(ctx, "[RelationService] err=%s", bizErr.Error())
			return
		}
	}
	return
}

func (s *tagTenantRel) diffInOnePropKey(param *TenantPropRelParam) (delRelS []int64, addRelS, newRelS []*do.ProductTenantPropertyRel) {
	var (
		catMap      = make(map[int64]bool)
		matchCatMap = make(map[int64]bool)
	)
	for _, v := range param.TenantTypeS {
		catMap[v] = true
	}
	for _, v := range param.OldRelS {
		if catMap[v.TenantType] {
			matchCatMap[v.TenantType] = true
			newRelS = append(newRelS, v)
			continue
		}
		delRelS = append(delRelS, v.TenantType)
	}
	for k := range catMap {
		if matchCatMap[k] {
			continue
		}
		w := &do.ProductTenantPropertyRel{
			TenantType:  k,
			PropertyKey: param.PropertyKey,
			Status:      int32(category.CommonStatus_InUse),
			CreatedTime: time.Now(),
			UpdatedTime: time.Now(),
		}
		if op := param.Operator; op != nil {
			w.CreateUID = op.OperatorId
			w.CreateName = op.OperatorName
			w.UpdateUID = op.OperatorId
			w.UpdateName = op.OperatorName
		}
		addRelS = append(addRelS, w)
	}
	newRelS = append(newRelS, addRelS...)
	return
}
