package cpv_service

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"context"
)

type DatabaseDataAccessStrategy struct{}

func NewDatabaseDataAccessStrategy() DataAccessStrategy {
	return &DatabaseDataAccessStrategy{}
}

func (s *DatabaseDataAccessStrategy) GetCategoriesByIDs(ctx context.Context, categoryIDs []int64) ([]*do.Category, *errdef.BizErr) {
	categoryList, err := dal.MGetCategoryWithIsTest(ctx, categoryIDs, nil, nil)
	if err != nil {
		logs.CtxError(ctx, "[DatabaseDataAccessStrategy] GetCategoriesByIDs from db failed, err: %v", err)
		return nil, biz_err.DBErr.WithErr(err).WithMessage("获取类目失败")
	}
	return categoryList, nil
}

func (s *DatabaseDataAccessStrategy) GetCategoriesByParentIDs(ctx context.Context, parentCategoryIDs []int64) ([]*do.Category, *errdef.BizErr) {
	queryParam := &dal.CategoryQueryParam{
		ParentCategoryIdList: parentCategoryIDs,
		Limit:                500,
	}
	logs.CtxInfo(ctx, "[DatabaseDataAccessStrategy] GetCategoriesByParentIDs queryParam: %+v", queryParam)
	categoryList, _, bizErr := dal.Category.Query(ctx, nil, queryParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[DatabaseDataAccessStrategy] GetCategoriesByParentIDs from db failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	return categoryList, nil
}

func (s *DatabaseDataAccessStrategy) GetPropertiesByKeys(ctx context.Context, propertyKeys []string) ([]*do.ProductProperty, *errdef.BizErr) {
	queryParam := &dal.PropQueryParam{
		PropertyKeyList: propertyKeys,
		Limit:           len(propertyKeys),
	}
	doList, _, bizErr := dal.ProductProperty.Query(ctx, nil, queryParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[DatabaseDataAccessStrategy] GetPropertiesByKeys from db failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	return doList, nil
}

func (s *DatabaseDataAccessStrategy) GetPropertyRelationsByTenantIDs(ctx context.Context, tenantTypeIDs []int64) ([]*do.ProductTenantPropertyRel, *errdef.BizErr) {
	relParam := &dal.TenantRelQueryParam{
		TenantTypeS: tenantTypeIDs,
		Limit:       500,
	}
	relList, bizErr := dal.TenantPropRel.Query(ctx, nil, relParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[DatabaseDataAccessStrategy] GetPropertyRelationsByTenantIDs from db failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	return relList, nil
}

func (s *DatabaseDataAccessStrategy) GetPropertyRelationsByCategoryIDs(ctx context.Context, categoryIDs []int64) ([]*do.ProductCategoryPropertyRel, *errdef.BizErr) {
	relParam := &dal.CatRelQueryParam{
		CategoryIDs: categoryIDs,
		Limit:       500,
	}
	relList, bizErr := dal.CategoryPropertyRel.Query(ctx, nil, relParam)
	if bizErr != nil {
		logs.CtxError(ctx, "[DatabaseDataAccessStrategy] GetPropertyRelationsByCategoryIDs from db failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	return relList, nil
}

func (s *DatabaseDataAccessStrategy) GetAllParentIDs(ctx context.Context, categoryIDs []int64) ([]int64, *errdef.BizErr) {
	allIDs, bizErr := CategoryService.GetAllParentIDs(ctx, categoryIDs)
	if bizErr != nil {
		logs.CtxError(ctx, "[DatabaseDataAccessStrategy] GetAllParentIDs from db failed, err=%s", bizErr.Error())
		return nil, bizErr
	}
	return allIDs, nil
}
