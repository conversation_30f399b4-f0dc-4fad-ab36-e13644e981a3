package cpv_service

import (
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

type StrategyFactory struct {
	cacheStrategy    DataAccessStrategy
	databaseStrategy DataAccessStrategy
}

func NewStrategyFactory() *StrategyFactory {
	return &StrategyFactory{
		cacheStrategy:    NewCacheDataAccessStrategy(),
		databaseStrategy: NewDatabaseDataAccessStrategy(),
	}
}
func (f *StrategyFactory) GetStrategy(readStrategy *category.ReadStrategy) DataAccessStrategy {
	if readStrategy != nil && *readStrategy == category.ReadStrategy_CacheDefault {
		return f.cacheStrategy
	}
	return f.databaseStrategy
}

// 全局策略工厂实例
var GlobalStrategyFactory = NewStrategyFactory()
