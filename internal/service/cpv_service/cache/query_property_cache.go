package cache

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"context"
	"fmt"
	"time"
)

var QueryPropertyCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v", "motor.fwe_ecom.category.Property")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	QueryPropertyCache = anycache.New(
		cache.MustNewLocalBytesCacheLRU(256),
		codec.NewJson(codec.JsonImplIteratorDefault),
	).
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				propertyKey := item.(string)
				return genCacheKeyByPropertyKey(propertyKey)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				propertyKeys := missedItems.([]string)
				queryParam := &dal.PropQueryParam{
					PropertyKeyList: propertyKeys,
					Limit:           len(propertyKeys),
				}
				properties, _, bizErr := dal.ProductProperty.Query(ctx, nil, queryParam)
				if bizErr != nil {
					return nil, bizErr
				}
				resultMap := make(map[string]*do.ProductProperty)
				for _, property := range properties {
					key := genCacheKeyByPropertyKey(property.PropertyKey)
					resultMap[key] = property
				}
				return resultMap, nil
			},
		)
}

func genCacheKeyByPropertyKey(propertyKey string) string {
	return fmt.Sprintf("property:key:%s", propertyKey)
}
