package cache

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"context"
	"fmt"
	"time"
)

var CategoryPropertyRelCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v", "motor.fwe_ecom.category.CatPropRel")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	CategoryPropertyRelCache = anycache.New(
		cache.MustNewLocalBytesCacheLRU(256),
		codec.<PERSON>son(codec.JsonImplIteratorDefault),
	).
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				categoryID := item.(int64)
				return fmt.Sprintf("cat_prop_rel:cat_id:%d", categoryID)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				categoryIDs := missedItems.([]int64)
				queryParam := &dal.CatRelQueryParam{
					CategoryIDs: categoryIDs,
					Limit:       500,
				}
				relations, bizErr := dal.CategoryPropertyRel.Query(ctx, nil, queryParam)
				if bizErr != nil {
					return nil, bizErr
				}
				// 手动将返回的 slice 转换为 map
				resultMap := make(map[string][]*do.ProductCategoryPropertyRel)
				for _, rel := range relations {
					key := fmt.Sprintf("cat_prop_rel:cat_id:%d", rel.CategoryID)
					resultMap[key] = append(resultMap[key], rel)
				}
				return resultMap, nil
			},
		)
}
