package cache

import (
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"context"
	"fmt"
	"gorm.io/gen/field"
	"time"
)

var GetAllParentIDsCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v", "motor.fwe_ecom.category.GetAllParentIDs")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	GetAllParentIDsCache = anycache.New(
		cache.MustNewLocalBytesCacheLRU(256),
		codec.NewJson(codec.JsonImplIteratorDefault),
	).
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				categoryID := item.(int64)
				return genCacheKeyByCategoryId(categoryID)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				categoryIDs := missedItems.([]int64)
				// 批量从DB获取父链路
				parentMap, err := GetAllParentIDs(ctx, categoryIDs)
				if err != nil {
					return nil, err
				}
				return parentMap, nil
			},
		)
}

func GetAllParentIDs(ctx context.Context, categoryIDs []int64) (map[string][]int64, *errdef.BizErr) {
	var (
		maxLevel = 3
		result   = map[string][]int64{}
	)
	for _, id := range categoryIDs {
		key := genCacheKeyByCategoryId(id)
		result[key] = []int64{id} // 每个ID的父链路都包含自己
	}

	// childID -> parentID 的映射
	childToParent := make(map[int64]int64)
	currentIDs := gslice.Uniq(categoryIDs)

	// 通过循环批量获取所有层级的父ID，直到最顶层
	for i := 0; i < maxLevel && len(currentIDs) > 0; i++ {
		queryParam := &dal.CategoryQueryParam{
			SelectColumns:  []field.Expr{mysql.Category.ID, mysql.Category.ParentID},
			CategoryIDList: currentIDs,
		}
		dataList, _, bizErr := dal.Category.Query(ctx, nil, queryParam)
		if bizErr != nil {
			logs.CtxError(ctx, "[MGetAllParentIDs] query failed: %v", bizErr)
			return nil, bizErr
		}

		nextIDs := make([]int64, 0)
		for _, cat := range dataList {
			if cat.ID != 0 && cat.ParentID != 0 {
				childToParent[cat.ID] = cat.ParentID
				nextIDs = append(nextIDs, cat.ParentID)
			}
		}
		currentIDs = gslice.Uniq(nextIDs)
	}

	// 基于已构建的 childToParent 映射，为每个初始ID回溯出完整的父链路
	for _, catID := range categoryIDs {
		curr := catID
		for {
			parentID, ok := childToParent[curr]
			if !ok || parentID == 0 {
				break
			}
			key := genCacheKeyByCategoryId(catID)
			result[key] = append(result[key], parentID)
			curr = parentID
		}
	}

	return result, nil
}

func genCacheKeyByCategoryId(categoryID int64) string {
	return fmt.Sprintf("get_all_parentIDs:cat_id:%d", categoryID)
}
