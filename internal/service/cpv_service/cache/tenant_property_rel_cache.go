package cache

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"context"
	"fmt"
	"time"
)

var TenantPropertyRelCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v", "motor.fwe_ecom.category.TenantPropRel")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	TenantPropertyRelCache = anycache.New(
		cache.MustNewLocalBytesCacheLRU(256),
		codec.New<PERSON>son(codec.JsonImplIteratorDefault),
	).
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				tenantType := item.(int64)
				return genCacheKeyByTenantType(tenantType)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				tenantTypes := missedItems.([]int64)
				queryParam := &dal.TenantRelQueryParam{
					TenantTypeS: tenantTypes,
					Limit:       500,
				}
				relations, bizErr := dal.TenantPropRel.Query(ctx, nil, queryParam)
				if bizErr != nil {
					return nil, bizErr
				}
				// 手动将返回的 slice 转换为 map
				resultMap := make(map[string][]*do.ProductTenantPropertyRel)
				for _, relation := range relations {
					key := genCacheKeyByTenantType(relation.TenantType)
					resultMap[key] = append(resultMap[key], relation)
				}
				return resultMap, nil
			},
		)
}

func genCacheKeyByTenantType(tenantType int64) string {
	return fmt.Sprintf("tenant_prop_rel:tenant_type:%d", tenantType)
}
