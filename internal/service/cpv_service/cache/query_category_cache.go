package cache

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"context"
	"fmt"
	"time"
)

var QueryCategoryCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v", "motor.fwe_ecom.category.QueryCategory")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	QueryCategoryCache = anycache.New(
		cache.MustNewLocalBytesCacheLRU(256),
		codec.NewJson(codec.JsonImplIteratorDefault),
	).
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			// 定义如何从输入参数生成缓存键
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				parentCategoryID := item.(int64)
				return fmt.Sprintf("parentCategory:id:%d", parentCategoryID)
			},
			// 定义回源函数，用于在缓存未命中时从数据库批量获取数据
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				parentCategoryIDs := missedItems.([]int64)
				queryParam := &dal.CategoryQueryParam{
					ParentCategoryIdList: parentCategoryIDs,
					Limit:                500,
				}
				// 进行数据库查询
				categories, _, bizErr := dal.Category.Query(ctx, nil, queryParam)
				if bizErr != nil {
					return nil, bizErr
				}
				resultMap := make(map[string][]*do.Category)
				for _, category := range categories {
					key := fmt.Sprintf("parentCategory:id:%d", category.ParentID)
					resultMap[key] = append(resultMap[key], category)
				}
				return resultMap, nil
			},
		)
}
