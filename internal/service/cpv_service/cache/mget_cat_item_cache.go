package cache

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"context"
	"fmt"
	"time"
)

var MGetCategoryCache anycache.BatchFetcher

func init() {
	nameSpace := fmt.Sprintf("%v", "motor.fwe_ecom.category.MGetCategory")
	logicExpireTime := 5 * time.Minute
	physicalExpireTime := 10 * time.Minute

	MGetCategoryCache = anycache.New(
		cache.MustNewLocalBytesCacheLRU(256),
		codec.New<PERSON>son(codec.JsonImplIteratorDefault),
	).
		WithNameSpace(nameSpace).
		WithSourceStrategy(anycache.SsExpiredDataAndAsyncSource).
		WithTTL(logicExpireTime, physicalExpireTime).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				categoryID := item.(int64)
				return fmt.Sprintf("category:id:%d", categoryID)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				categoryIDs := missedItems.([]int64)
				categories, err := dal.MGetCategoryWithIsTest(ctx, categoryIDs, nil, nil)
				if err != nil {
					return nil, err
				}
				resultMap := make(map[string]*do.Category)
				for _, category := range categories {
					key := fmt.Sprintf("category:id:%d", category.ID)
					resultMap[key] = category
				}
				return resultMap, nil
			},
		)
}

func genCacheKeyByCategoryID(categoryID int64) string {
	return fmt.Sprintf("category:id:%d", categoryID)
}
