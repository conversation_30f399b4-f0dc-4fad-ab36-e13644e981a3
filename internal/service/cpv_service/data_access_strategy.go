package cpv_service

import (
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	"context"
)

// DataAccessStrategy 定义数据访问策略接口
type DataAccessStrategy interface {
	// GetCategoriesByIDs 根据类目ID列表获取类目信息
	GetCategoriesByIDs(ctx context.Context, categoryIDs []int64) ([]*do.Category, *errdef.BizErr)

	// GetCategoriesByParentIDs 根据父类目ID列表获取子类目信息
	GetCategoriesByParentIDs(ctx context.Context, parentCategoryIDs []int64) ([]*do.Category, *errdef.BizErr)

	// GetPropertiesByKeys 根据属性key列表获取属性信息
	GetPropertiesByKeys(ctx context.Context, propertyKeys []string) ([]*do.ProductProperty, *errdef.BizErr)

	// GetPropertyRelationsByTenantIDs 根据租户类型ID列表获取租户属性关系
	GetPropertyRelationsByTenantIDs(ctx context.Context, tenantTypeIDs []int64) ([]*do.ProductTenantPropertyRel, *errdef.BizErr)

	// GetPropertyRelationsByCategoryIDs 根据类目ID列表获取类目属性关系
	GetPropertyRelationsByCategoryIDs(ctx context.Context, categoryIDs []int64) ([]*do.ProductCategoryPropertyRel, *errdef.BizErr)

	// GetAllParentIDs 根据类目ID列表获取所有父类目ID
	GetAllParentIDs(ctx context.Context, categoryIDs []int64) ([]int64, *errdef.BizErr)
}
