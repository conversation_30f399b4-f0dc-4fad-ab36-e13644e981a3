package domain

import (
	"context"
	"sort"

	"github.com/pkg/errors"

	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

func GetCategoryBaseByIDAndKey(ctx context.Context, catIDs []int64, catKeys []string, isTest int32) ([]*category.CategoryBase, error) {
	categories, err := dal.MGetCategoryWithIsTest(ctx, catIDs, catKeys, &isTest)
	if err != nil {
		return nil, errors.Wrap(err, "[GetCategoryBaseByIDAndKey] MGetCategory from mysql fail")
	}

	categoryBases := make([]*category.CategoryBase, 0, len(categories))
	for _, categoryInfo := range categories {
		categoryBases = append(categoryBases, converter.NewPO2DO().TranCategory(categoryInfo))
	}

	return categoryBases, nil
}

func GetPropertyItem(ctx context.Context, catIDs []int64) ([]*category.PropertyItem, error) {
	properties, err := dal.MGetProperty(ctx, catIDs, nil)
	if err != nil {
		return nil, errors.Wrap(err, "[GetPropertyItem] MGetProperty from mysql fail")
	}

	propertyItems := make([]*category.PropertyItem, 0, len(properties))
	for _, propertyInfo := range properties {
		propertyItem := converter.NewPO2DO().TranProperty(propertyInfo)
		if propertyItem.ContainPropertyValues {
			propertyItem.Values, err = GetPropertyValue(ctx, propertyInfo.CategoryID, propertyItem.Id, 0)

			if err != nil {
				logs.CtxError(ctx, "[GetPropertyItem] GetPropertyValue failed, propertyItem id is %d", propertyItem.Id)
			}
		}

		propertyItems = append(propertyItems, propertyItem)
	}

	sort.Slice(propertyItems, func(i, j int) bool {
		return propertyItems[i].Sequence < propertyItems[i].Sequence
	})

	return propertyItems, nil
}

func GetPropertyValue(ctx context.Context, catID int64, proID int64, parProValIDs int64) ([]*category.PropertyValue, error) {
	propertyVals, err := dal.MGetPropertyValue(ctx, catID, proID, parProValIDs)
	if err != nil {
		return nil, errors.Wrap(err, "[GetPropertyValue] MGetPropertyValue from mysql fail")
	}

	propertyValues := make([]*category.PropertyValue, 0, len(propertyVals))
	for _, propertyValInfo := range propertyVals {
		propertyValues = append(propertyValues, converter.NewPO2DO().TranPropertyValue(propertyValInfo))
	}

	sort.Slice(propertyValues, func(i, j int) bool {
		return propertyValues[i].Sequence < propertyValues[i].Sequence
	})

	return propertyValues, nil
}
