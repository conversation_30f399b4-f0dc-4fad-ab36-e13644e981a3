package common_property

import (
	"context"

	"code.byted.org/motor/fwe_category/internal/consts"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

// CommonPropertyFetcher 通用属性值获取器
type CommonPropertyFetcher interface {
	FetchPropertyValueList(ctx context.Context, parentValueID int64) []*category.PropertyValue
}

func NewCommonPropertyFetcher(property *do.Property) CommonPropertyFetcher {
	switch property.Source {
	case int32(consts.PropertySource_Car_Car): // to 临时 boe环境车型库暂时不行
		return NewCarCarPropertyValueFetcher(property)
	case int32(consts.PropertySource_Car_Brand), int32(consts.PropertySource_Car_SubBrand),
		int32(consts.PropertySource_Car_Series): // int32(consts.PropertySource_Car_Car)
		// 适用于获取车型库品牌、子品牌、车系、车型属性值
		return NewCarPropertyValueFetcher(property)
	case int32(consts.PropertySource_Province), int32(consts.PropertySource_City):
		// 适用于获取省份、城市列表属性值
		return NewCityPropertyValueFetcher(property)
	default:
		return nil
	}
}
