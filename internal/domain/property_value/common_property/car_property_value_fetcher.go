package common_property

import (
	"code.byted.org/motor/gopkg/tools/tools_recover"
	"context"
	"sync"

	"github.com/bytedance/sonic"
	"github.com/tidwall/gjson"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/consts"
	"code.byted.org/motor/fwe_category/internal/model/biz"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/service"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/overpass/motor_car_agg_sort/kitex_gen/motor/agg_sort"
	"code.byted.org/overpass/motor_car_base/kitex_gen/motor/car_base"
)

type CarPropertyValueFetcher struct {
	Property *do.Property
}

func NewCarPropertyValueFetcher(property *do.Property) CommonPropertyFetcher {
	return &CarPropertyValueFetcher{
		Property: property,
	}
}

func (f CarPropertyValueFetcher) FetchPropertyValueList(ctx context.Context, parentValueID int64) (
	propertyValueList []*category.PropertyValue) {

	// 获取配置的静态筛选条件、属性值列表、聚合key
	exFilters, propertyKeys := f.getCarSearchParams(ctx, f.Property.Feature)
	groupKey := consts.CarGroupKeyMap[consts.PropertySource(f.Property.Source)]

	// 构建筛选请求
	filters, group := f.buildSearchReq(exFilters, parentValueID, groupKey)

	// 聚合筛选车型库数据
	_, _, itemList, err := service.Search(ctx, group, filters)
	if err != nil {
		logs.CtxError(ctx, "[CarPropertyValueFetcher] FetchPropertyValueList err:%v")
		return
	}

	// 根据聚合结果获取id列表
	ids := make([]int32, 0, len(itemList))
	for _, item := range itemList {
		if item.GetGroupRes() == nil || item.GetGroupRes().GetGroupKeyVal() == nil ||
			item.GetGroupRes().GetGroupKeyVal()[groupKey] == nil {
			continue
		}
		ids = append(ids, int32(item.GetGroupRes().GetGroupKeyVal()[groupKey].GetIntVal()))
	}

	// 根据id获取需要的车型库属性值
	id2PropertyMap := f.getCarPropertyByIDType(ctx, f.Property.Source, propertyKeys, ids)
	// 打包
	propertyValueList = f.packCarPropertyValue(ctx, ids, id2PropertyMap, f.Property.Source)

	return
}

func (f CarPropertyValueFetcher) getCarSearchParams(ctx context.Context, propertyFeature string) (filters []*agg_sort.FilterClause, propertyKeys []string) {
	if propertyFeature == "" {
		return
	}

	var carSearchParams *biz.CarSearchParams
	carSearchParamsStr := gjson.Get(propertyFeature, "car_search_params").String()
	logs.CtxInfo(ctx, "[CarPropertyValueFetcher] carSearchParamsStr:%s", carSearchParamsStr)
	err := sonic.UnmarshalString(carSearchParamsStr, &carSearchParams)
	if err != nil {
		logs.CtxError(ctx, "[CarPropertyValueFetcher] getCarSearchParams err:%v", err)
	}

	filters = carSearchParams.Filters
	propertyKeys = carSearchParams.PropertyKeys

	return
}

func (f CarPropertyValueFetcher) packCarPropertyValue(ctx context.Context, ids []int32, id2CarPropertyMap map[int32]map[string]string,
	source int32) (propertyValueList []*category.PropertyValue) {

	var nameKey string

	switch source {
	case int32(consts.PropertySource_Car_Brand):
		nameKey = "brand_name"
	case int32(consts.PropertySource_Car_SubBrand):
		nameKey = "sub_brand_name"
	case int32(consts.PropertySource_Car_Series):
		nameKey = "series_name"
	case int32(consts.PropertySource_Car_Car):
		nameKey = "car_name"
	}

	logs.CtxInfo(ctx, "[CarPropertyValueFetcher] ids:%+v id2CarPropertyMap:%+v", ids, id2CarPropertyMap)

	for _, id := range ids {
		var name string
		carPropertyMap := id2CarPropertyMap[id]
		if carPropertyMap != nil {
			name = carPropertyMap[nameKey]
		}
		propertyValueList = append(propertyValueList, &category.PropertyValue{
			Id:      int64(id),
			Name:    name,
			Status:  1,
			Feature: id2CarPropertyMap[id],
		})
	}

	return
}

func (f CarPropertyValueFetcher) getCarPropertyByIDType(ctx context.Context, source int32, propertyKeys []string, ids []int32) map[int32]map[string]string {
	id2PropertyMap := make(map[int32]map[string]string)
	if len(propertyKeys) == 0 || len(ids) == 0 {
		logs.CtxInfo(ctx, "[GetCarPropertyByIDType] propertyKeys:%+v or ids:%+v empty", propertyKeys, ids)
		return id2PropertyMap
	}

	var (
		wg     sync.WaitGroup
		lock   sync.Mutex
		idType car_base.IdTypeEnum
	)

	switch source {
	case int32(consts.PropertySource_Car_Brand):
		idType = car_base.IdTypeEnum_BRAND_ID
	case int32(consts.PropertySource_Car_SubBrand):
		idType = car_base.IdTypeEnum_SUB_BRAND_ID
	case int32(consts.PropertySource_Car_Series):
		idType = car_base.IdTypeEnum_SERIES_ID
	case int32(consts.PropertySource_Car_Car):
		idType = car_base.IdTypeEnum_CAR_ID
	}

	// 分批获取
	chunkIDs := slices.ChunkEveryInt32(ids, 30)
	for _, chunkID := range chunkIDs {
		wg.Add(1)

		go func(idList []int32) {
			defer func() {
				tools_recover.CheckRecover(ctx, recover())
				wg.Done()
			}()

			resultMap, err := service.GetCarProperty(ctx, idList, idType, propertyKeys)
			if err != nil {
				logs.CtxInfo(ctx, "[GetCarPropertyByIDType] get car property err:%v", err)
				return
			}

			lock.Lock()
			for id, propertyMap := range resultMap {
				id2PropertyMap[id] = propertyMap
			}
			lock.Unlock()

		}(chunkID)
	}

	wg.Wait()

	return id2PropertyMap
}

func (f CarPropertyValueFetcher) buildSearchReq(exFilters []*agg_sort.FilterClause, parentValueID int64, groupKey string) (
	filters []*agg_sort.FilterClause, group *agg_sort.Group) {

	group = &agg_sort.Group{
		GroupKeyList: []string{groupKey},
	}

	if filterKey := consts.CarFilterKeyMap[consts.PropertySource(f.Property.Source)]; filterKey != "" {
		filters = append(filters, &agg_sort.FilterClause{
			Key:    filterKey,
			Option: agg_sort.FilterOption_EQUAL,
			Val: &agg_sort.AllTypeVal{
				IntVals: []int64{
					parentValueID,
				},
			},
		})
	}

	filters = append(filters, exFilters...)

	return
}
