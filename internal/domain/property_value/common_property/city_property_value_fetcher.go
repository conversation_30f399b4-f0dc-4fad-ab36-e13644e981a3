package common_property

import (
	"context"
	"sort"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/consts"
	"code.byted.org/motor/fwe_category/internal/model/biz"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/service"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

type CityPropertyValueFetcher struct {
	Property *do.Property
}

func NewCityPropertyValueFetcher(property *do.Property) CommonPropertyFetcher {
	return &CityPropertyValueFetcher{
		Property: property,
	}
}

func (f CityPropertyValueFetcher) FetchPropertyValueList(ctx context.Context, parentValueID int64) (
	propertyValueList []*category.PropertyValue) {

	cityMap, err := service.GetAllCitiesMap(ctx)
	if err != nil {
		logs.CtxError(ctx, "[CityPropertyValueFetcher] get all cities map err:%v", err)
		return
	}

	switch f.Property.Source {
	case int32(consts.PropertySource_Province):
		// 获取聚合省份列表
		propertyValueList = f.getAggregationProvinceList(cityMap)
	case int32(consts.PropertySource_City):
		// 获取城市列表 若parentValueID（省份id）不为空 取该省份下的城市
		propertyValueList = f.getAggregationCityList(cityMap, parentValueID)
	}

	return
}

func (f CityPropertyValueFetcher) getAggregationProvinceList(cityMap map[string]*biz.City) (
	propertyValueList []*category.PropertyValue) {

	provinceIDList := make([]int, 0)
	provinceID2NameMap := make(map[int32]string)
	for _, city := range cityMap {
		if slices.ContainsInt(provinceIDList, int(city.ProvinceID)) {
			continue
		}
		provinceIDList = append(provinceIDList, int(city.ProvinceID))
		provinceID2NameMap[city.ProvinceID] = city.ProvinceName
	}

	sort.Ints(provinceIDList)

	for _, provinceID := range provinceIDList {
		propertyValueList = append(propertyValueList, &category.PropertyValue{
			Id:     int64(provinceID),
			Name:   provinceID2NameMap[int32(provinceID)],
			Status: 1,
		})
	}

	return
}

func (f CityPropertyValueFetcher) getAggregationCityList(cityMap map[string]*biz.City, parentValueID int64) (
	propertyValueList []*category.PropertyValue) {

	provinceIDList := make([]int, 0)
	provinceID2City := make(map[int32][]*biz.City)

	for _, city := range cityMap {
		if !slices.ContainsInt(provinceIDList, int(city.ProvinceID)) {
			provinceIDList = append(provinceIDList, int(city.ProvinceID))
		}
		provinceID2City[city.ProvinceID] = append(provinceID2City[city.ProvinceID], city)
	}

	if len(provinceID2City[int32(parentValueID)]) > 0 {
		cityList := provinceID2City[int32(parentValueID)]
		sort.Slice(cityList, func(i, j int) bool {
			return cityList[i].CityID < cityList[j].CityID
		})
		for _, city := range cityList {
			propertyValueList = append(propertyValueList, &category.PropertyValue{
				Id:     int64(city.CityID),
				Name:   city.CityName,
				Status: 1,
			})
		}

	} else {
		sort.Ints(provinceIDList)
		for _, provinceID := range provinceIDList {
			cityList := provinceID2City[int32(provinceID)]
			sort.Slice(cityList, func(i, j int) bool {
				return cityList[i].CityID < cityList[j].CityID
			})
			for _, city := range cityList {
				propertyValueList = append(propertyValueList, &category.PropertyValue{
					Id:     int64(city.CityID),
					Name:   city.CityName,
					Status: 1,
				})
			}
		}
	}

	return
}
