package common_property

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/motor/fwe_category/internal/common/utils"
	"code.byted.org/motor/fwe_category/internal/model/do"
	"code.byted.org/motor/fwe_category/internal/service"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/overpass/motor_car_base/kitex_gen/motor/car_base"
	"context"
)

type CarCarPropertyValueFetcher struct {
	Property *do.Property
}

func (f *CarCarPropertyValueFetcher) FetchPropertyValueList(ctx context.Context, parentValueID int64) []*category.PropertyValue {

	// 获取车系下 所有车型ID
	seriesPropertyMap, err := service.GetCarProperty(ctx, []int32{int32(parentValueID)}, car_base.IdTypeEnum_SERIES_ID, []string{
		"online_car_ids",
		// "offline_car_ids",
		"presale_car_ids",
		"unlisted_car_ids",
	})
	if err != nil {
		logs.CtxError(ctx, "[CarCarPropertyValueFetcher] GetCarProperty failed, err is %v", err)
		return nil
	}
	carIDs := make([]int32, 0, 10)

	for _, value := range seriesPropertyMap[int32(parentValueID)] {
		carIDs = append(carIDs, utils.UnmarshalStrToValue(value, []int32{})...)
	}

	carPropertyMap, err := service.GetCarProperty(ctx, carIDs, car_base.IdTypeEnum_CAR_ID, []string{
		"car_name",
		"official_price",
	})

	if err != nil {
		logs.CtxError(ctx, "[CarCarPropertyValueFetcher] carPropertyMap failed, err is %v", err)
		return nil
	}

	res := make([]*category.PropertyValue, 0, len(carIDs))
	for _, carID := range carIDs {
		carInfo := carPropertyMap[carID]
		if carInfo == nil {
			continue
		}
		res = append(res, &category.PropertyValue{
			Id:          int64(carID),
			Name:        carInfo["car_name"], // car_name
			Alias:       "",
			Status:      1,
			OutId:       conv.Int64ToStr(int64(carID)),
			ParentId:    parentValueID,
			PropertyKey: f.Property.UniqueKey,
			Price:       utils.Float642Int64IgnoreErr(carInfo["official_price"]),
			MarketPrice: utils.Float642Int64IgnoreErr(carInfo["official_price"]),
			CategoryId:  f.Property.CategoryID,
			SpuId:       parentValueID,
			PropertyId:  f.Property.ID,
			Feature:     carInfo,
		})
	}
	return res
}

func NewCarCarPropertyValueFetcher(property *do.Property) CommonPropertyFetcher {
	return &CarCarPropertyValueFetcher{
		Property: property,
	}
}
