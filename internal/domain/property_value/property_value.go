package property_value

import (
	"code.byted.org/motor/fwe_ecom_product_common/util/errgroup"
	"context"
	"sync"

	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/consts"
	"code.byted.org/motor/fwe_category/internal/domain/property_value/common_property"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
	"gorm.io/gen"
)

type PropertyValueService struct {
	CategoryID int64
	ShopIDList []string
	SpuIDList  []int64
	Status     *int64
}

func NewPropertyValueService(CategoryID int64, ShopIDList []string, SpuIDList []int64, Status *int64) *PropertyValueService {
	return &PropertyValueService{
		CategoryID: CategoryID,
		ShopIDList: ShopIDList,
		SpuIDList:  SpuIDList,
		Status:     Status,
	}
}

func (s *PropertyValueService) MGetPropertyValueByProperty(ctx context.Context, propertyList []*category.PropertyItem, params map[string]*category.PropertyGetParam) (map[string][]*category.PropertyValue, *errdef.BizErr) {
	var (
		commonPropertyList  []*category.PropertyItem
		specialPropertyList []*category.PropertyItem
		resPropertyMap      map[string][]*category.PropertyValue

		wLock    = sync.Mutex{}
		eg, ctx2 = errgroup.WithContext(ctx)
	)

	gslice.ForEach(propertyList, func(property *category.PropertyItem) {
		if property.Source == consts.PropertySource_Common {
			commonPropertyList = append(commonPropertyList, property)
		} else {
			specialPropertyList = append(specialPropertyList, property)
		}
	})

	if len(commonPropertyList) > 0 {
		eg.Go(ctx2, func(ctx context.Context) error {
			tempPropertyMap, bizErr := s.mGetPropertyValueWithDB(ctx, commonPropertyList)
			if bizErr != nil {
				return bizErr
			}

			wLock.Lock()
			resPropertyMap = gmap.Merge(resPropertyMap, tempPropertyMap)
			wLock.Unlock()
			return nil
		})
	}

	if len(specialPropertyList) > 0 {
		for _, specialProperty := range specialPropertyList {
			eg.Go(ctx, func(ctx context.Context) error {
				param := params[specialProperty.UniqueKey]
				if param == nil {
					return nil
				}
				if propertyFetcher := common_property.NewCommonPropertyFetcher(converter.NewDO2PO().PropertyItem(specialProperty)); propertyFetcher != nil {
					propertyValues := propertyFetcher.FetchPropertyValueList(ctx, param.GetParentValueId())
					wLock.Lock()
					resPropertyMap = gmap.Merge(resPropertyMap, map[string][]*category.PropertyValue{
						specialProperty.UniqueKey: propertyValues,
					})
					wLock.Unlock()
				}

				return nil
			})
		}
	}

	if err := eg.Wait(); err != nil {
		return resPropertyMap, biz_err.ServerException.WithErr(err)
	}

	return resPropertyMap, nil
}

func (s *PropertyValueService) mGetPropertyValueWithDB(ctx context.Context, propertyList []*category.PropertyItem) (map[string][]*category.PropertyValue, *errdef.BizErr) {
	propValRead := dal.FweEcomReadQuery.PropertyValue

	propertyKeyList := gslice.Map(propertyList, func(property *category.PropertyItem) string {
		return property.UniqueKey
	})

	db := propValRead.WithContext(ctx).
		Where(propValRead.CategoryID.Eq(s.CategoryID)).
		Where(propValRead.ShopID.In(s.ShopIDList...)).
		Where(propValRead.SpuID.In(s.SpuIDList...)).
		Where(propValRead.PropertyKey.In(propertyKeyList...))

	if s.Status != nil {
		db = db.Where(propValRead.Status.Eq(int32(*s.Status)))
	}

	propertyValueList, err := db.FindInBatch(500, func(tx gen.Dao, batch int) error {
		return nil
	})
	if err != nil {
		return nil, biz_err.ServerException.WithErr(err)
	}

	propertyValueDOList := converter.NewPO2DO().MTransPropertyValue(propertyValueList)
	propertyMap := gslice.GroupBy(propertyValueDOList, func(propertyValDO *category.PropertyValue) string {
		return propertyValDO.PropertyKey
	})

	propertyMap = gmap.MapValues(propertyMap, func(valueList []*category.PropertyValue) []*category.PropertyValue {
		gslice.SortBy(valueList, func(value1 *category.PropertyValue, value2 *category.PropertyValue) bool {
			return value1.Sequence < value2.Sequence
		})
		return valueList
	})

	return propertyMap, nil
}
