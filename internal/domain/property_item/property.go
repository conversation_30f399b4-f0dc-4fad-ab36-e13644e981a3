package property_item

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/motor/fwe_category/internal/common/converter"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
	"code.byted.org/motor/fwe_ecom_lib/ecom_err/errdef"
	biz_err "code.byted.org/motor/fwe_ecom_lib/ecom_err/psm_err_code/106_motor_fwe_ecom_category"
)

type PropertyItemService struct {
}

func NewPropertyItemService() *PropertyItemService {
	return &PropertyItemService{}
}

func (s PropertyItemService) MGetPropertyItem(ctx context.Context, categoryID int64, propertyKeyList []string) ([]*category.PropertyItem, *errdef.BizErr) {
	var (
		propertyItemList []*category.PropertyItem
	)

	propertyList, err := dal.MGetPropertyItemByKey(ctx, categoryID, propertyKeyList)
	if err != nil {
		logs.CtxError(ctx, "[MGetPropertyItem] MGetPropertyItemByKey DB err:%v", err)
		return nil, biz_err.ServerException.WithErr(err).WithMessage("MGetPropertyItemByKey failed")
	}

	propertyItemList = converter.NewPO2DO().MTranPropertyItem(propertyList)
	gslice.SortBy(propertyItemList, func(item1 *category.PropertyItem, item2 *category.PropertyItem) bool {
		return item1.Sequence < item2.Sequence
	})

	return propertyItemList, nil
}
