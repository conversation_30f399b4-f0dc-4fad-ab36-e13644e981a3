package main

import (
	"code.byted.org/kite/kitex/server"
	"code.byted.org/motor/fwe_category/internal/middleware"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category/fweecomcategoryservice"
	"code.byted.org/motor/fwe_category/pkg/tcc"
	ecom_err_middleware "code.byted.org/motor/fwe_ecom_lib/ecom_err/middleware"
)

func main() {
	Init()

	if err := fweecomcategoryservice.NewServer(
		new(FweEcomCategoryServiceImpl),
		server.WithMiddleware(middleware.LogMiddleware),
		server.WithMiddleware(ecom_err_middleware.KitexMiddleware),
	).Run(); err != nil {
		panic(err)
	}
}

func Init() {
	tcc.Init()
}
