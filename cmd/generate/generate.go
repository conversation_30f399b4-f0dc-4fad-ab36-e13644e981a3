package main

import (
	"code.byted.org/gorm/bytedgen"
	"code.byted.org/motor/fwe_category/internal/repository/mysql"
	"gorm.io/gen"
)

// GEN Guideline: https://bytedance.feishu.cn/wiki/wikcnbYLEL78aOYLBsT2ExUGiEd

// generate code
func main() {
	// init db
	db := mysql.FweEcomDB()

	// specify the output directory (default: "./query")
	// ### if you want to query without context constrain, set mode gen.WithoutContext ###
	g := bytedgen.NewGenerator(gen.Config{
		OutPath:      "../../internal/repository/mysql",
		ModelPkgPath: "../../internal/model/do",
		/* Mode: gen.WithoutContext,*/
		// if you want the nullable field generation property to be pointer type, set FieldNullable true
		/* FieldNullable: true,*/
		Mode:              gen.WithDefaultQuery,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
	})

	// reuse the database connection in Project or create a connection here
	// if you want to use GenerateModel/GenerateModelAs, UseDB is necessray or it will panic
	g.UseDB(db)

	// 为指定的结构体或表格生成基础CRUD查询方法
	g.ApplyBasic(
		g.GenerateModelAs("motor_category", "Category"),
		g.GenerateModelAs("motor_category_property", "Property"),
		g.GenerateModelAs("motor_category_property_value", "PropertyValue"),
		g.GenerateModelAs("tag_meta", "TagMeta"),
		g.GenerateModelAs("tag_value", "TagValue"),
		g.GenerateModel("product_property"),
		g.GenerateModel("product_category_property_rel"),
		g.GenerateModel("product_tenant_property_rel"),
	)

	// apply diy interfaces on structs or table models
	// g.ApplyInterface(func(method model.Method) {}, model.User{}, g.GenerateModel("company"))

	// execute the action of code generation
	g.Execute()
}
