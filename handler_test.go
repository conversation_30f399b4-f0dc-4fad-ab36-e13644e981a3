package main

import (
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/motor/gopkg/tools"
	"context"
	"testing"
	"time"

	"code.byted.org/motor/fwe_category/internal/common/utils"
	"code.byted.org/motor/fwe_category/internal/repository/dal"
	"code.byted.org/motor/fwe_category/kitex_gen/motor/fwe_ecom/category"
)

func init() {
	Init()
}

// doas --login-type sso -p motor.fwe_ecom.category go test -v -count=1 -run "TestMGetCategory" handler_test.go handler.go main.go
func TestMGetCategory(t *testing.T) {
	ctx := context.Background()

	category, err := dal.MGetCategory(ctx, []int64{501}, []string{"product_car_inner"})

	utils.PrintInfoLog(ctx, "category %s", category)
	utils.PrintInfoLog(ctx, "category %s", err)

	time.Sleep(time.Second * 2)
}

// doas -p motor.fwe.category go test -v -count=1 -run "TestMGetProperty" handler_test.go
func TestMGetProperty(t *testing.T) {
	ctx := context.Background()

	category, err := dal.MGetProperty(ctx, []int64{1}, nil)

	utils.PrintInfoLog(ctx, "category %s", category)
	utils.PrintInfoLog(ctx, "category %s", err)

	time.Sleep(time.Second * 2)
}

// doas --login-type sso -p motor.fwe_ecom.category go test -v -count=1 -run "TestFweEcomCategoryServiceImpl_MGetCategoryItem" handler_test.go handler.go main.go
func TestFweEcomCategoryServiceImpl_MGetCategoryItem(t *testing.T) {
	type args struct {
		ctx context.Context
		req *category.MGetCategoryItemReq
	}
	tests := []struct {
		name     string
		args     args
		wantResp *category.MGetCategoryItemResp
		wantErr  bool
	}{
		{
			name: "1",
			args: args{
				ctx: context.Background(),
				req: &category.MGetCategoryItemReq{
					CategoryIds:  []int64{501},
					NeedProperty: true,
					CategoryKeys: []string{"product_car_inner"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &FweEcomCategoryServiceImpl{}
			gotResp, _ := s.MGetCategoryItem(tt.args.ctx, tt.args.req)
			t.Log(tools.GetLogStr(gotResp))
		})
	}
}

// doas --login-type sso -p motor.fwe_ecom.category go test -v -count=1 -run "TestFweEcomCategoryServiceImpl_GetBizLineCategories" handler_test.go handler.go main.go
func TestFweEcomCategoryServiceImpl_GetBizLineCategories(t *testing.T) {
	type args struct {
		ctx context.Context
		req *category.GetBizLineCategoriesReq
	}
	tests := []struct {
		name     string
		args     args
		wantResp *category.GetBizLineCategoriesResp
		wantErr  bool
	}{
		{
			name: "1",
			args: args{
				ctx: context.Background(),
				req: &category.GetBizLineCategoriesReq{
					BizLine: 1,
					NeedAll: conv.BoolPtr(true),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &FweEcomCategoryServiceImpl{}
			gotResp, _ := s.GetBizLineCategories(tt.args.ctx, tt.args.req)
			t.Log(tools.GetLogStr(gotResp))
		})
	}
}
