#!/usr/bin/env bash
RUN_NAME="motor.fwe_ecom.category"

mkdir -p output/bin output/conf
cp script/* output/
cp conf/* output/conf/
chmod +x output/bootstrap.sh

if [ "$IS_SYSTEM_TEST_ENV" != "1" ]; then
    if [ "$BUILD_TYPE" = "offline" -o "$BUILD_TYPE" = "test" ]; then
        go get code.byted.org/bet/go_coverage@tiktok_sg
        go_coverage annotate -extra-info=include:internal # use skip-files to skip files that not need calculate coverage, e.g. -skip-files=test*,example.go
    fi
    go build -o output/bin/${RUN_NAME}
else
    go test -c -covermode=set -o output/bin/${RUN_NAME} -coverpkg=./...
fi