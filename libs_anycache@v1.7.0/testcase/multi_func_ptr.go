package testcase

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"
)

// 批量的情况
func GetDiamondPtrList(ctx context.Context, liveId int64, diamondIds []int64) ([]*Diamond, error) {
	if ctx.Value("bad_source") != nil {
		return nil, errors.New("bad_source")
	}

	if ctx.Value("latency") != nil {
		time.Sleep(ctx.Value("latency").(time.Duration))
	}

	var diamonds []*Diamond
	for _, id := range diamondIds {
		if id < 100 {
			diamonds = append(diamonds, &Diamond{
				id, liveId, 1, rand.Int63(),
			})
		} else {
			diamonds = append(diamonds, nil)
		}
	}
	return diamonds, nil
}

type GetDiamondListPtrContext struct {
	ctx        context.Context
	liveId     int64
	diamondIds []int64
}

func (c *GetDiamondListPtrContext) MultiFetch(ctx context.Context, missedItems interface{}) (interface{}, error) {
	diamondIds := missedItems.([]int64)
	diamondList, err := GetDiamondPtrList(c.ctx, c.liveId, diamondIds)
	if err != nil {
		return nil, err
	}

	return diamondList, nil
}

func (c *GetDiamondListPtrContext) genKeyFromParams(ctx context.Context, item interface{}) string {
	id := item.(int64)
	return fmt.Sprintf("%d:%d", c.liveId, id)
}

func (c *GetDiamondListPtrContext) genKeyFromResults(ctx context.Context, outItem interface{}) string {
	diamond := outItem.(*Diamond)
	return fmt.Sprintf("%d:%d", diamond.LiveId, diamond.Id)
}

// var _ anycache.BatchLoader = &GetDiamondListPtrContext{}

func NewGetDiamondListPtrContext(ctx context.Context, liveId int64, diamondIds []int64) *GetDiamondListPtrContext {
	return &GetDiamondListPtrContext{
		ctx:        ctx,
		liveId:     liveId,
		diamondIds: diamondIds,
	}
}

func (c *GetDiamondListPtrContext) GetInItems(ctx context.Context) interface{} {
	return c.diamondIds
}
