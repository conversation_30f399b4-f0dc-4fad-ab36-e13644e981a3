package testcase

import (
	"time"

	"code.byted.org/kv/goredis"
	"github.com/alicebob/miniredis/v2"
)

var (
	// cli       *goredis.Client
	// onceRedis sync.Once

	RedisFakePSM = "toutiao.redis.webcast_libs"
)

func GetRedisCli() *goredis.Client {
	return GetMockRedisClient()

	// onceRedis.Do(func() {
	// 	cli = NewRedisCli(RedisFakePSM)
	// })

	// return cli
}

func NewRedisCli(psm string) *goredis.Client {
	opt := goredis.NewOptionWithTimeout(time.Second, time.Second, time.Second, time.Second, time.Second, time.Second, 10)
	cli, err := goredis.NewClientWithOption(psm, opt)
	if err != nil {
		panic(err)
	}
	return cli
}

func GetMockRedisClient() *goredis.Client {
	s, err := miniredis.Run()
	if err != nil {
		panic(err)
	}

	go func() {
		// Decrease the TTL manually.
		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		for range ticker.C {
			s.FastForward(time.Second)
		}
	}()

	option := goredis.NewOption()
	option.DisableAutoLoadConf()
	option.ReadTimeout = time.Second
	option.WriteTimeout = time.Second
	option.DisableGDPRVerify = true

	cli, err := goredis.NewClientWithServers("", []string{s.Addr()}, option)
	if err != nil {
		panic(err)
	}

	return cli
}
