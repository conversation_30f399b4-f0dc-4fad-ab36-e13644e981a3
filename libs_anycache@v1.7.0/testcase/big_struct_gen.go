package testcase

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *AnchorLevelStats) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Level":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Level")
					return
				}
				z.Level = nil
			} else {
				if z.Level == nil {
					z.Level = new(int64)
				}
				*z.Level, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "Level")
					return
				}
			}
		case "Experience":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Experience")
					return
				}
				z.Experience = nil
			} else {
				if z.Experience == nil {
					z.Experience = new(int64)
				}
				*z.Experience, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "Experience")
					return
				}
			}
		case "LowestExperienceThisLevel":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "LowestExperienceThisLevel")
					return
				}
				z.LowestExperienceThisLevel = nil
			} else {
				if z.LowestExperienceThisLevel == nil {
					z.LowestExperienceThisLevel = new(int64)
				}
				*z.LowestExperienceThisLevel, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "LowestExperienceThisLevel")
					return
				}
			}
		case "HighestExperienceThisLevel":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "HighestExperienceThisLevel")
					return
				}
				z.HighestExperienceThisLevel = nil
			} else {
				if z.HighestExperienceThisLevel == nil {
					z.HighestExperienceThisLevel = new(int64)
				}
				*z.HighestExperienceThisLevel, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "HighestExperienceThisLevel")
					return
				}
			}
		case "TaskStartExperience":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "TaskStartExperience")
					return
				}
				z.TaskStartExperience = nil
			} else {
				if z.TaskStartExperience == nil {
					z.TaskStartExperience = new(int64)
				}
				*z.TaskStartExperience, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "TaskStartExperience")
					return
				}
			}
		case "TaskStartTime":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "TaskStartTime")
					return
				}
				z.TaskStartTime = nil
			} else {
				if z.TaskStartTime == nil {
					z.TaskStartTime = new(int64)
				}
				*z.TaskStartTime, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "TaskStartTime")
					return
				}
			}
		case "TaskDecreaseExperience":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "TaskDecreaseExperience")
					return
				}
				z.TaskDecreaseExperience = nil
			} else {
				if z.TaskDecreaseExperience == nil {
					z.TaskDecreaseExperience = new(int64)
				}
				*z.TaskDecreaseExperience, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "TaskDecreaseExperience")
					return
				}
			}
		case "TaskTargetExperience":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "TaskTargetExperience")
					return
				}
				z.TaskTargetExperience = nil
			} else {
				if z.TaskTargetExperience == nil {
					z.TaskTargetExperience = new(int64)
				}
				*z.TaskTargetExperience, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "TaskTargetExperience")
					return
				}
			}
		case "TaskEndTime":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "TaskEndTime")
					return
				}
				z.TaskEndTime = nil
			} else {
				if z.TaskEndTime == nil {
					z.TaskEndTime = new(int64)
				}
				*z.TaskEndTime, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "TaskEndTime")
					return
				}
			}
		case "ProfileDialogBg":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ProfileDialogBg")
					return
				}
				z.ProfileDialogBg = nil
			} else {
				if z.ProfileDialogBg == nil {
					z.ProfileDialogBg = new(UrlStruct)
				}
				err = z.ProfileDialogBg.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "ProfileDialogBg")
					return
				}
			}
		case "ProfileDialogBgBack":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ProfileDialogBgBack")
					return
				}
				z.ProfileDialogBgBack = nil
			} else {
				if z.ProfileDialogBgBack == nil {
					z.ProfileDialogBgBack = new(UrlStruct)
				}
				err = z.ProfileDialogBgBack.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "ProfileDialogBgBack")
					return
				}
			}
		case "StageLevel":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "StageLevel")
					return
				}
				z.StageLevel = nil
			} else {
				if z.StageLevel == nil {
					z.StageLevel = new(UrlStruct)
				}
				err = z.StageLevel.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "StageLevel")
					return
				}
			}
		case "SmallIcon":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "SmallIcon")
					return
				}
				z.SmallIcon = nil
			} else {
				if z.SmallIcon == nil {
					z.SmallIcon = new(UrlStruct)
				}
				err = z.SmallIcon.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "SmallIcon")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *AnchorLevelStats) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 13
	// write "Level"
	err = en.Append(0x8d, 0xa5, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	if z.Level == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.Level)
		if err != nil {
			err = msgp.WrapError(err, "Level")
			return
		}
	}
	// write "Experience"
	err = en.Append(0xaa, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if err != nil {
		return
	}
	if z.Experience == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.Experience)
		if err != nil {
			err = msgp.WrapError(err, "Experience")
			return
		}
	}
	// write "LowestExperienceThisLevel"
	err = en.Append(0xb9, 0x4c, 0x6f, 0x77, 0x65, 0x73, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x69, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	if z.LowestExperienceThisLevel == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.LowestExperienceThisLevel)
		if err != nil {
			err = msgp.WrapError(err, "LowestExperienceThisLevel")
			return
		}
	}
	// write "HighestExperienceThisLevel"
	err = en.Append(0xba, 0x48, 0x69, 0x67, 0x68, 0x65, 0x73, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x69, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	if z.HighestExperienceThisLevel == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.HighestExperienceThisLevel)
		if err != nil {
			err = msgp.WrapError(err, "HighestExperienceThisLevel")
			return
		}
	}
	// write "TaskStartExperience"
	err = en.Append(0xb3, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if err != nil {
		return
	}
	if z.TaskStartExperience == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.TaskStartExperience)
		if err != nil {
			err = msgp.WrapError(err, "TaskStartExperience")
			return
		}
	}
	// write "TaskStartTime"
	err = en.Append(0xad, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	if z.TaskStartTime == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.TaskStartTime)
		if err != nil {
			err = msgp.WrapError(err, "TaskStartTime")
			return
		}
	}
	// write "TaskDecreaseExperience"
	err = en.Append(0xb6, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if err != nil {
		return
	}
	if z.TaskDecreaseExperience == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.TaskDecreaseExperience)
		if err != nil {
			err = msgp.WrapError(err, "TaskDecreaseExperience")
			return
		}
	}
	// write "TaskTargetExperience"
	err = en.Append(0xb4, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if err != nil {
		return
	}
	if z.TaskTargetExperience == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.TaskTargetExperience)
		if err != nil {
			err = msgp.WrapError(err, "TaskTargetExperience")
			return
		}
	}
	// write "TaskEndTime"
	err = en.Append(0xab, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	if z.TaskEndTime == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.TaskEndTime)
		if err != nil {
			err = msgp.WrapError(err, "TaskEndTime")
			return
		}
	}
	// write "ProfileDialogBg"
	err = en.Append(0xaf, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x42, 0x67)
	if err != nil {
		return
	}
	if z.ProfileDialogBg == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.ProfileDialogBg.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "ProfileDialogBg")
			return
		}
	}
	// write "ProfileDialogBgBack"
	err = en.Append(0xb3, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x42, 0x67, 0x42, 0x61, 0x63, 0x6b)
	if err != nil {
		return
	}
	if z.ProfileDialogBgBack == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.ProfileDialogBgBack.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "ProfileDialogBgBack")
			return
		}
	}
	// write "StageLevel"
	err = en.Append(0xaa, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	if z.StageLevel == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.StageLevel.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "StageLevel")
			return
		}
	}
	// write "SmallIcon"
	err = en.Append(0xa9, 0x53, 0x6d, 0x61, 0x6c, 0x6c, 0x49, 0x63, 0x6f, 0x6e)
	if err != nil {
		return
	}
	if z.SmallIcon == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.SmallIcon.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "SmallIcon")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *AnchorLevelStats) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 13
	// string "Level"
	o = append(o, 0x8d, 0xa5, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if z.Level == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.Level)
	}
	// string "Experience"
	o = append(o, 0xaa, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if z.Experience == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.Experience)
	}
	// string "LowestExperienceThisLevel"
	o = append(o, 0xb9, 0x4c, 0x6f, 0x77, 0x65, 0x73, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x69, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if z.LowestExperienceThisLevel == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.LowestExperienceThisLevel)
	}
	// string "HighestExperienceThisLevel"
	o = append(o, 0xba, 0x48, 0x69, 0x67, 0x68, 0x65, 0x73, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x68, 0x69, 0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if z.HighestExperienceThisLevel == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.HighestExperienceThisLevel)
	}
	// string "TaskStartExperience"
	o = append(o, 0xb3, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if z.TaskStartExperience == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.TaskStartExperience)
	}
	// string "TaskStartTime"
	o = append(o, 0xad, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65)
	if z.TaskStartTime == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.TaskStartTime)
	}
	// string "TaskDecreaseExperience"
	o = append(o, 0xb6, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x65, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if z.TaskDecreaseExperience == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.TaskDecreaseExperience)
	}
	// string "TaskTargetExperience"
	o = append(o, 0xb4, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if z.TaskTargetExperience == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.TaskTargetExperience)
	}
	// string "TaskEndTime"
	o = append(o, 0xab, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65)
	if z.TaskEndTime == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.TaskEndTime)
	}
	// string "ProfileDialogBg"
	o = append(o, 0xaf, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x42, 0x67)
	if z.ProfileDialogBg == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.ProfileDialogBg.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "ProfileDialogBg")
			return
		}
	}
	// string "ProfileDialogBgBack"
	o = append(o, 0xb3, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x42, 0x67, 0x42, 0x61, 0x63, 0x6b)
	if z.ProfileDialogBgBack == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.ProfileDialogBgBack.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "ProfileDialogBgBack")
			return
		}
	}
	// string "StageLevel"
	o = append(o, 0xaa, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if z.StageLevel == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.StageLevel.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "StageLevel")
			return
		}
	}
	// string "SmallIcon"
	o = append(o, 0xa9, 0x53, 0x6d, 0x61, 0x6c, 0x6c, 0x49, 0x63, 0x6f, 0x6e)
	if z.SmallIcon == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.SmallIcon.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "SmallIcon")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *AnchorLevelStats) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Level":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Level = nil
			} else {
				if z.Level == nil {
					z.Level = new(int64)
				}
				*z.Level, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Level")
					return
				}
			}
		case "Experience":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Experience = nil
			} else {
				if z.Experience == nil {
					z.Experience = new(int64)
				}
				*z.Experience, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Experience")
					return
				}
			}
		case "LowestExperienceThisLevel":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.LowestExperienceThisLevel = nil
			} else {
				if z.LowestExperienceThisLevel == nil {
					z.LowestExperienceThisLevel = new(int64)
				}
				*z.LowestExperienceThisLevel, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LowestExperienceThisLevel")
					return
				}
			}
		case "HighestExperienceThisLevel":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.HighestExperienceThisLevel = nil
			} else {
				if z.HighestExperienceThisLevel == nil {
					z.HighestExperienceThisLevel = new(int64)
				}
				*z.HighestExperienceThisLevel, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "HighestExperienceThisLevel")
					return
				}
			}
		case "TaskStartExperience":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.TaskStartExperience = nil
			} else {
				if z.TaskStartExperience == nil {
					z.TaskStartExperience = new(int64)
				}
				*z.TaskStartExperience, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "TaskStartExperience")
					return
				}
			}
		case "TaskStartTime":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.TaskStartTime = nil
			} else {
				if z.TaskStartTime == nil {
					z.TaskStartTime = new(int64)
				}
				*z.TaskStartTime, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "TaskStartTime")
					return
				}
			}
		case "TaskDecreaseExperience":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.TaskDecreaseExperience = nil
			} else {
				if z.TaskDecreaseExperience == nil {
					z.TaskDecreaseExperience = new(int64)
				}
				*z.TaskDecreaseExperience, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "TaskDecreaseExperience")
					return
				}
			}
		case "TaskTargetExperience":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.TaskTargetExperience = nil
			} else {
				if z.TaskTargetExperience == nil {
					z.TaskTargetExperience = new(int64)
				}
				*z.TaskTargetExperience, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "TaskTargetExperience")
					return
				}
			}
		case "TaskEndTime":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.TaskEndTime = nil
			} else {
				if z.TaskEndTime == nil {
					z.TaskEndTime = new(int64)
				}
				*z.TaskEndTime, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "TaskEndTime")
					return
				}
			}
		case "ProfileDialogBg":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ProfileDialogBg = nil
			} else {
				if z.ProfileDialogBg == nil {
					z.ProfileDialogBg = new(UrlStruct)
				}
				bts, err = z.ProfileDialogBg.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "ProfileDialogBg")
					return
				}
			}
		case "ProfileDialogBgBack":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ProfileDialogBgBack = nil
			} else {
				if z.ProfileDialogBgBack == nil {
					z.ProfileDialogBgBack = new(UrlStruct)
				}
				bts, err = z.ProfileDialogBgBack.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "ProfileDialogBgBack")
					return
				}
			}
		case "StageLevel":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.StageLevel = nil
			} else {
				if z.StageLevel == nil {
					z.StageLevel = new(UrlStruct)
				}
				bts, err = z.StageLevel.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "StageLevel")
					return
				}
			}
		case "SmallIcon":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.SmallIcon = nil
			} else {
				if z.SmallIcon == nil {
					z.SmallIcon = new(UrlStruct)
				}
				bts, err = z.SmallIcon.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "SmallIcon")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *AnchorLevelStats) Msgsize() (s int) {
	s = 1 + 6
	if z.Level == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 11
	if z.Experience == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 26
	if z.LowestExperienceThisLevel == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 27
	if z.HighestExperienceThisLevel == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 20
	if z.TaskStartExperience == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 14
	if z.TaskStartTime == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 23
	if z.TaskDecreaseExperience == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 21
	if z.TaskTargetExperience == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 12
	if z.TaskEndTime == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 16
	if z.ProfileDialogBg == nil {
		s += msgp.NilSize
	} else {
		s += z.ProfileDialogBg.Msgsize()
	}
	s += 20
	if z.ProfileDialogBgBack == nil {
		s += msgp.NilSize
	} else {
		s += z.ProfileDialogBgBack.Msgsize()
	}
	s += 11
	if z.StageLevel == nil {
		s += msgp.NilSize
	} else {
		s += z.StageLevel.Msgsize()
	}
	s += 10
	if z.SmallIcon == nil {
		s += msgp.NilSize
	} else {
		s += z.SmallIcon.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *BaseResp) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "StatusMessage":
			z.StatusMessage, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "StatusMessage")
				return
			}
		case "StatusCode":
			z.StatusCode, err = dc.ReadInt32()
			if err != nil {
				err = msgp.WrapError(err, "StatusCode")
				return
			}
		case "Extra":
			var zb0002 uint32
			zb0002, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "Extra")
				return
			}
			if z.Extra == nil {
				z.Extra = make(map[string]string, zb0002)
			} else if len(z.Extra) > 0 {
				for key := range z.Extra {
					delete(z.Extra, key)
				}
			}
			for zb0002 > 0 {
				zb0002--
				var za0001 string
				var za0002 string
				za0001, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Extra")
					return
				}
				za0002, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Extra", za0001)
					return
				}
				z.Extra[za0001] = za0002
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *BaseResp) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "StatusMessage"
	err = en.Append(0x83, 0xad, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.StatusMessage)
	if err != nil {
		err = msgp.WrapError(err, "StatusMessage")
		return
	}
	// write "StatusCode"
	err = en.Append(0xaa, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65)
	if err != nil {
		return
	}
	err = en.WriteInt32(z.StatusCode)
	if err != nil {
		err = msgp.WrapError(err, "StatusCode")
		return
	}
	// write "Extra"
	err = en.Append(0xa5, 0x45, 0x78, 0x74, 0x72, 0x61)
	if err != nil {
		return
	}
	err = en.WriteMapHeader(uint32(len(z.Extra)))
	if err != nil {
		err = msgp.WrapError(err, "Extra")
		return
	}
	for za0001, za0002 := range z.Extra {
		err = en.WriteString(za0001)
		if err != nil {
			err = msgp.WrapError(err, "Extra")
			return
		}
		err = en.WriteString(za0002)
		if err != nil {
			err = msgp.WrapError(err, "Extra", za0001)
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *BaseResp) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "StatusMessage"
	o = append(o, 0x83, 0xad, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65)
	o = msgp.AppendString(o, z.StatusMessage)
	// string "StatusCode"
	o = append(o, 0xaa, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65)
	o = msgp.AppendInt32(o, z.StatusCode)
	// string "Extra"
	o = append(o, 0xa5, 0x45, 0x78, 0x74, 0x72, 0x61)
	o = msgp.AppendMapHeader(o, uint32(len(z.Extra)))
	for za0001, za0002 := range z.Extra {
		o = msgp.AppendString(o, za0001)
		o = msgp.AppendString(o, za0002)
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *BaseResp) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "StatusMessage":
			z.StatusMessage, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "StatusMessage")
				return
			}
		case "StatusCode":
			z.StatusCode, bts, err = msgp.ReadInt32Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "StatusCode")
				return
			}
		case "Extra":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Extra")
				return
			}
			if z.Extra == nil {
				z.Extra = make(map[string]string, zb0002)
			} else if len(z.Extra) > 0 {
				for key := range z.Extra {
					delete(z.Extra, key)
				}
			}
			for zb0002 > 0 {
				var za0001 string
				var za0002 string
				zb0002--
				za0001, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Extra")
					return
				}
				za0002, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Extra", za0001)
					return
				}
				z.Extra[za0001] = za0002
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *BaseResp) Msgsize() (s int) {
	s = 1 + 14 + msgp.StringPrefixSize + len(z.StatusMessage) + 11 + msgp.Int32Size + 6 + msgp.MapHeaderSize
	if z.Extra != nil {
		for za0001, za0002 := range z.Extra {
			_ = za0002
			s += msgp.StringPrefixSize + len(za0001) + msgp.StringPrefixSize + len(za0002)
		}
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *CoverLibrary) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Checking":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Checking")
					return
				}
				z.Checking = nil
			} else {
				if z.Checking == nil {
					z.Checking = new(string)
				}
				*z.Checking, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Checking")
					return
				}
			}
		case "Checked":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Checked")
					return
				}
				z.Checked = nil
			} else {
				if z.Checked == nil {
					z.Checked = new(string)
				}
				*z.Checked, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Checked")
					return
				}
			}
		case "Auditor":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Auditor")
					return
				}
				z.Auditor = nil
			} else {
				if z.Auditor == nil {
					z.Auditor = new(string)
				}
				*z.Auditor, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Auditor")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *CoverLibrary) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "Checking"
	err = en.Append(0x83, 0xa8, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x67)
	if err != nil {
		return
	}
	if z.Checking == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Checking)
		if err != nil {
			err = msgp.WrapError(err, "Checking")
			return
		}
	}
	// write "Checked"
	err = en.Append(0xa7, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64)
	if err != nil {
		return
	}
	if z.Checked == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Checked)
		if err != nil {
			err = msgp.WrapError(err, "Checked")
			return
		}
	}
	// write "Auditor"
	err = en.Append(0xa7, 0x41, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72)
	if err != nil {
		return
	}
	if z.Auditor == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Auditor)
		if err != nil {
			err = msgp.WrapError(err, "Auditor")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *CoverLibrary) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "Checking"
	o = append(o, 0x83, 0xa8, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x69, 0x6e, 0x67)
	if z.Checking == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Checking)
	}
	// string "Checked"
	o = append(o, 0xa7, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64)
	if z.Checked == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Checked)
	}
	// string "Auditor"
	o = append(o, 0xa7, 0x41, 0x75, 0x64, 0x69, 0x74, 0x6f, 0x72)
	if z.Auditor == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Auditor)
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *CoverLibrary) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Checking":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Checking = nil
			} else {
				if z.Checking == nil {
					z.Checking = new(string)
				}
				*z.Checking, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Checking")
					return
				}
			}
		case "Checked":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Checked = nil
			} else {
				if z.Checked == nil {
					z.Checked = new(string)
				}
				*z.Checked, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Checked")
					return
				}
			}
		case "Auditor":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Auditor = nil
			} else {
				if z.Auditor == nil {
					z.Auditor = new(string)
				}
				*z.Auditor, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Auditor")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *CoverLibrary) Msgsize() (s int) {
	s = 1 + 9
	if z.Checking == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Checking)
	}
	s += 8
	if z.Checked == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Checked)
	}
	s += 8
	if z.Auditor == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Auditor)
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *DataStruct) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "UserInfo":
			var zb0002 uint32
			zb0002, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "UserInfo")
				return
			}
			if z.UserInfo == nil {
				z.UserInfo = make(map[string]*UserStruct, zb0002)
			} else if len(z.UserInfo) > 0 {
				for key := range z.UserInfo {
					delete(z.UserInfo, key)
				}
			}
			for zb0002 > 0 {
				zb0002--
				var za0001 string
				var za0002 *UserStruct
				za0001, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "UserInfo")
					return
				}
				if dc.IsNil() {
					err = dc.ReadNil()
					if err != nil {
						err = msgp.WrapError(err, "UserInfo", za0001)
						return
					}
					za0002 = nil
				} else {
					if za0002 == nil {
						za0002 = new(UserStruct)
					}
					err = za0002.DecodeMsg(dc)
					if err != nil {
						err = msgp.WrapError(err, "UserInfo", za0001)
						return
					}
				}
				z.UserInfo[za0001] = za0002
			}
		case "BaseResp":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "BaseResp")
					return
				}
				z.BaseResp = nil
			} else {
				if z.BaseResp == nil {
					z.BaseResp = new(BaseResp)
				}
				err = z.BaseResp.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "BaseResp")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *DataStruct) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "UserInfo"
	err = en.Append(0x82, 0xa8, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f)
	if err != nil {
		return
	}
	err = en.WriteMapHeader(uint32(len(z.UserInfo)))
	if err != nil {
		err = msgp.WrapError(err, "UserInfo")
		return
	}
	for za0001, za0002 := range z.UserInfo {
		err = en.WriteString(za0001)
		if err != nil {
			err = msgp.WrapError(err, "UserInfo")
			return
		}
		if za0002 == nil {
			err = en.WriteNil()
			if err != nil {
				return
			}
		} else {
			err = za0002.EncodeMsg(en)
			if err != nil {
				err = msgp.WrapError(err, "UserInfo", za0001)
				return
			}
		}
	}
	// write "BaseResp"
	err = en.Append(0xa8, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70)
	if err != nil {
		return
	}
	if z.BaseResp == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.BaseResp.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "BaseResp")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *DataStruct) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "UserInfo"
	o = append(o, 0x82, 0xa8, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f)
	o = msgp.AppendMapHeader(o, uint32(len(z.UserInfo)))
	for za0001, za0002 := range z.UserInfo {
		o = msgp.AppendString(o, za0001)
		if za0002 == nil {
			o = msgp.AppendNil(o)
		} else {
			o, err = za0002.MarshalMsg(o)
			if err != nil {
				err = msgp.WrapError(err, "UserInfo", za0001)
				return
			}
		}
	}
	// string "BaseResp"
	o = append(o, 0xa8, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70)
	if z.BaseResp == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.BaseResp.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "BaseResp")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *DataStruct) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "UserInfo":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "UserInfo")
				return
			}
			if z.UserInfo == nil {
				z.UserInfo = make(map[string]*UserStruct, zb0002)
			} else if len(z.UserInfo) > 0 {
				for key := range z.UserInfo {
					delete(z.UserInfo, key)
				}
			}
			for zb0002 > 0 {
				var za0001 string
				var za0002 *UserStruct
				zb0002--
				za0001, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserInfo")
					return
				}
				if msgp.IsNil(bts) {
					bts, err = msgp.ReadNilBytes(bts)
					if err != nil {
						return
					}
					za0002 = nil
				} else {
					if za0002 == nil {
						za0002 = new(UserStruct)
					}
					bts, err = za0002.UnmarshalMsg(bts)
					if err != nil {
						err = msgp.WrapError(err, "UserInfo", za0001)
						return
					}
				}
				z.UserInfo[za0001] = za0002
			}
		case "BaseResp":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.BaseResp = nil
			} else {
				if z.BaseResp == nil {
					z.BaseResp = new(BaseResp)
				}
				bts, err = z.BaseResp.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "BaseResp")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *DataStruct) Msgsize() (s int) {
	s = 1 + 9 + msgp.MapHeaderSize
	if z.UserInfo != nil {
		for za0001, za0002 := range z.UserInfo {
			_ = za0002
			s += msgp.StringPrefixSize + len(za0001)
			if za0002 == nil {
				s += msgp.NilSize
			} else {
				s += za0002.Msgsize()
			}
		}
	}
	s += 9
	if z.BaseResp == nil {
		s += msgp.NilSize
	} else {
		s += z.BaseResp.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *GenderEnum) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 int64
		zb0001, err = dc.ReadInt64()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = GenderEnum(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z GenderEnum) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteInt64(int64(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z GenderEnum) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendInt64(o, int64(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *GenderEnum) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 int64
		zb0001, bts, err = msgp.ReadInt64Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = GenderEnum(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z GenderEnum) Msgsize() (s int) {
	s = msgp.Int64Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *ImageTypeEnum) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 int64
		zb0001, err = dc.ReadInt64()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = ImageTypeEnum(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z ImageTypeEnum) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteInt64(int64(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z ImageTypeEnum) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendInt64(o, int64(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *ImageTypeEnum) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 int64
		zb0001, bts, err = msgp.ReadInt64Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = ImageTypeEnum(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z ImageTypeEnum) Msgsize() (s int) {
	s = msgp.Int64Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *MultiGetUserResponse) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "UserInfo":
			var zb0002 uint32
			zb0002, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "UserInfo")
				return
			}
			if z.UserInfo == nil {
				z.UserInfo = make(map[string]*UserStruct, zb0002)
			} else if len(z.UserInfo) > 0 {
				for key := range z.UserInfo {
					delete(z.UserInfo, key)
				}
			}
			for zb0002 > 0 {
				zb0002--
				var za0001 string
				var za0002 *UserStruct
				za0001, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "UserInfo")
					return
				}
				if dc.IsNil() {
					err = dc.ReadNil()
					if err != nil {
						err = msgp.WrapError(err, "UserInfo", za0001)
						return
					}
					za0002 = nil
				} else {
					if za0002 == nil {
						za0002 = new(UserStruct)
					}
					err = za0002.DecodeMsg(dc)
					if err != nil {
						err = msgp.WrapError(err, "UserInfo", za0001)
						return
					}
				}
				z.UserInfo[za0001] = za0002
			}
		case "BaseResp":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "BaseResp")
					return
				}
				z.BaseResp = nil
			} else {
				if z.BaseResp == nil {
					z.BaseResp = new(BaseResp)
				}
				err = z.BaseResp.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "BaseResp")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *MultiGetUserResponse) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "UserInfo"
	err = en.Append(0x82, 0xa8, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f)
	if err != nil {
		return
	}
	err = en.WriteMapHeader(uint32(len(z.UserInfo)))
	if err != nil {
		err = msgp.WrapError(err, "UserInfo")
		return
	}
	for za0001, za0002 := range z.UserInfo {
		err = en.WriteString(za0001)
		if err != nil {
			err = msgp.WrapError(err, "UserInfo")
			return
		}
		if za0002 == nil {
			err = en.WriteNil()
			if err != nil {
				return
			}
		} else {
			err = za0002.EncodeMsg(en)
			if err != nil {
				err = msgp.WrapError(err, "UserInfo", za0001)
				return
			}
		}
	}
	// write "BaseResp"
	err = en.Append(0xa8, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70)
	if err != nil {
		return
	}
	if z.BaseResp == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.BaseResp.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "BaseResp")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *MultiGetUserResponse) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "UserInfo"
	o = append(o, 0x82, 0xa8, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f)
	o = msgp.AppendMapHeader(o, uint32(len(z.UserInfo)))
	for za0001, za0002 := range z.UserInfo {
		o = msgp.AppendString(o, za0001)
		if za0002 == nil {
			o = msgp.AppendNil(o)
		} else {
			o, err = za0002.MarshalMsg(o)
			if err != nil {
				err = msgp.WrapError(err, "UserInfo", za0001)
				return
			}
		}
	}
	// string "BaseResp"
	o = append(o, 0xa8, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70)
	if z.BaseResp == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.BaseResp.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "BaseResp")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *MultiGetUserResponse) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "UserInfo":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "UserInfo")
				return
			}
			if z.UserInfo == nil {
				z.UserInfo = make(map[string]*UserStruct, zb0002)
			} else if len(z.UserInfo) > 0 {
				for key := range z.UserInfo {
					delete(z.UserInfo, key)
				}
			}
			for zb0002 > 0 {
				var za0001 string
				var za0002 *UserStruct
				zb0002--
				za0001, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserInfo")
					return
				}
				if msgp.IsNil(bts) {
					bts, err = msgp.ReadNilBytes(bts)
					if err != nil {
						return
					}
					za0002 = nil
				} else {
					if za0002 == nil {
						za0002 = new(UserStruct)
					}
					bts, err = za0002.UnmarshalMsg(bts)
					if err != nil {
						err = msgp.WrapError(err, "UserInfo", za0001)
						return
					}
				}
				z.UserInfo[za0001] = za0002
			}
		case "BaseResp":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.BaseResp = nil
			} else {
				if z.BaseResp == nil {
					z.BaseResp = new(BaseResp)
				}
				bts, err = z.BaseResp.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "BaseResp")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *MultiGetUserResponse) Msgsize() (s int) {
	s = 1 + 9 + msgp.MapHeaderSize
	if z.UserInfo != nil {
		for za0001, za0002 := range z.UserInfo {
			_ = za0002
			s += msgp.StringPrefixSize + len(za0001)
			if za0002 == nil {
				s += msgp.NilSize
			} else {
				s += za0002.Msgsize()
			}
		}
	}
	s += 9
	if z.BaseResp == nil {
		s += msgp.NilSize
	} else {
		s += z.BaseResp.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *UrlContent) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Name":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Name")
					return
				}
				z.Name = nil
			} else {
				if z.Name == nil {
					z.Name = new(string)
				}
				*z.Name, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Name")
					return
				}
			}
		case "FontColor":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "FontColor")
					return
				}
				z.FontColor = nil
			} else {
				if z.FontColor == nil {
					z.FontColor = new(string)
				}
				*z.FontColor, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "FontColor")
					return
				}
			}
		case "Level":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Level")
					return
				}
				z.Level = nil
			} else {
				if z.Level == nil {
					z.Level = new(int64)
				}
				*z.Level, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "Level")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *UrlContent) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "Name"
	err = en.Append(0x83, 0xa4, 0x4e, 0x61, 0x6d, 0x65)
	if err != nil {
		return
	}
	if z.Name == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Name)
		if err != nil {
			err = msgp.WrapError(err, "Name")
			return
		}
	}
	// write "FontColor"
	err = en.Append(0xa9, 0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72)
	if err != nil {
		return
	}
	if z.FontColor == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.FontColor)
		if err != nil {
			err = msgp.WrapError(err, "FontColor")
			return
		}
	}
	// write "Level"
	err = en.Append(0xa5, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	if z.Level == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.Level)
		if err != nil {
			err = msgp.WrapError(err, "Level")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *UrlContent) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "Name"
	o = append(o, 0x83, 0xa4, 0x4e, 0x61, 0x6d, 0x65)
	if z.Name == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Name)
	}
	// string "FontColor"
	o = append(o, 0xa9, 0x46, 0x6f, 0x6e, 0x74, 0x43, 0x6f, 0x6c, 0x6f, 0x72)
	if z.FontColor == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.FontColor)
	}
	// string "Level"
	o = append(o, 0xa5, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if z.Level == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.Level)
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *UrlContent) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Name":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Name = nil
			} else {
				if z.Name == nil {
					z.Name = new(string)
				}
				*z.Name, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Name")
					return
				}
			}
		case "FontColor":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.FontColor = nil
			} else {
				if z.FontColor == nil {
					z.FontColor = new(string)
				}
				*z.FontColor, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "FontColor")
					return
				}
			}
		case "Level":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Level = nil
			} else {
				if z.Level == nil {
					z.Level = new(int64)
				}
				*z.Level, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Level")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *UrlContent) Msgsize() (s int) {
	s = 1 + 5
	if z.Name == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Name)
	}
	s += 10
	if z.FontColor == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.FontColor)
	}
	s += 6
	if z.Level == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *UrlStruct) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Uri":
			z.Uri, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Uri")
				return
			}
		case "UrlList":
			var zb0002 uint32
			zb0002, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "UrlList")
				return
			}
			if cap(z.UrlList) >= int(zb0002) {
				z.UrlList = (z.UrlList)[:zb0002]
			} else {
				z.UrlList = make([]string, zb0002)
			}
			for za0001 := range z.UrlList {
				z.UrlList[za0001], err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "UrlList", za0001)
					return
				}
			}
		case "Width":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Width")
					return
				}
				z.Width = nil
			} else {
				if z.Width == nil {
					z.Width = new(int64)
				}
				*z.Width, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "Width")
					return
				}
			}
		case "Height":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Height")
					return
				}
				z.Height = nil
			} else {
				if z.Height == nil {
					z.Height = new(int64)
				}
				*z.Height, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "Height")
					return
				}
			}
		case "ImageType":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ImageType")
					return
				}
				z.ImageType = nil
			} else {
				if z.ImageType == nil {
					z.ImageType = new(ImageTypeEnum)
				}
				{
					var zb0003 int64
					zb0003, err = dc.ReadInt64()
					if err != nil {
						err = msgp.WrapError(err, "ImageType")
						return
					}
					*z.ImageType = ImageTypeEnum(zb0003)
				}
			}
		case "OpenSchema":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "OpenSchema")
					return
				}
				z.OpenSchema = nil
			} else {
				if z.OpenSchema == nil {
					z.OpenSchema = new(string)
				}
				*z.OpenSchema, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "OpenSchema")
					return
				}
			}
		case "Content":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Content")
					return
				}
				z.Content = nil
			} else {
				if z.Content == nil {
					z.Content = new(UrlContent)
				}
				err = z.Content.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Content")
					return
				}
			}
		case "IsAnimated":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "IsAnimated")
					return
				}
				z.IsAnimated = nil
			} else {
				if z.IsAnimated == nil {
					z.IsAnimated = new(bool)
				}
				*z.IsAnimated, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "IsAnimated")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *UrlStruct) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 8
	// write "Uri"
	err = en.Append(0x88, 0xa3, 0x55, 0x72, 0x69)
	if err != nil {
		return
	}
	err = en.WriteString(z.Uri)
	if err != nil {
		err = msgp.WrapError(err, "Uri")
		return
	}
	// write "UrlList"
	err = en.Append(0xa7, 0x55, 0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.UrlList)))
	if err != nil {
		err = msgp.WrapError(err, "UrlList")
		return
	}
	for za0001 := range z.UrlList {
		err = en.WriteString(z.UrlList[za0001])
		if err != nil {
			err = msgp.WrapError(err, "UrlList", za0001)
			return
		}
	}
	// write "Width"
	err = en.Append(0xa5, 0x57, 0x69, 0x64, 0x74, 0x68)
	if err != nil {
		return
	}
	if z.Width == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.Width)
		if err != nil {
			err = msgp.WrapError(err, "Width")
			return
		}
	}
	// write "Height"
	err = en.Append(0xa6, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74)
	if err != nil {
		return
	}
	if z.Height == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.Height)
		if err != nil {
			err = msgp.WrapError(err, "Height")
			return
		}
	}
	// write "ImageType"
	err = en.Append(0xa9, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65)
	if err != nil {
		return
	}
	if z.ImageType == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(int64(*z.ImageType))
		if err != nil {
			err = msgp.WrapError(err, "ImageType")
			return
		}
	}
	// write "OpenSchema"
	err = en.Append(0xaa, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61)
	if err != nil {
		return
	}
	if z.OpenSchema == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.OpenSchema)
		if err != nil {
			err = msgp.WrapError(err, "OpenSchema")
			return
		}
	}
	// write "Content"
	err = en.Append(0xa7, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74)
	if err != nil {
		return
	}
	if z.Content == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.Content.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Content")
			return
		}
	}
	// write "IsAnimated"
	err = en.Append(0xaa, 0x49, 0x73, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64)
	if err != nil {
		return
	}
	if z.IsAnimated == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.IsAnimated)
		if err != nil {
			err = msgp.WrapError(err, "IsAnimated")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *UrlStruct) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 8
	// string "Uri"
	o = append(o, 0x88, 0xa3, 0x55, 0x72, 0x69)
	o = msgp.AppendString(o, z.Uri)
	// string "UrlList"
	o = append(o, 0xa7, 0x55, 0x72, 0x6c, 0x4c, 0x69, 0x73, 0x74)
	o = msgp.AppendArrayHeader(o, uint32(len(z.UrlList)))
	for za0001 := range z.UrlList {
		o = msgp.AppendString(o, z.UrlList[za0001])
	}
	// string "Width"
	o = append(o, 0xa5, 0x57, 0x69, 0x64, 0x74, 0x68)
	if z.Width == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.Width)
	}
	// string "Height"
	o = append(o, 0xa6, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74)
	if z.Height == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.Height)
	}
	// string "ImageType"
	o = append(o, 0xa9, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65)
	if z.ImageType == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, int64(*z.ImageType))
	}
	// string "OpenSchema"
	o = append(o, 0xaa, 0x4f, 0x70, 0x65, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61)
	if z.OpenSchema == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.OpenSchema)
	}
	// string "Content"
	o = append(o, 0xa7, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74)
	if z.Content == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.Content.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Content")
			return
		}
	}
	// string "IsAnimated"
	o = append(o, 0xaa, 0x49, 0x73, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64)
	if z.IsAnimated == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.IsAnimated)
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *UrlStruct) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "Uri":
			z.Uri, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Uri")
				return
			}
		case "UrlList":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "UrlList")
				return
			}
			if cap(z.UrlList) >= int(zb0002) {
				z.UrlList = (z.UrlList)[:zb0002]
			} else {
				z.UrlList = make([]string, zb0002)
			}
			for za0001 := range z.UrlList {
				z.UrlList[za0001], bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UrlList", za0001)
					return
				}
			}
		case "Width":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Width = nil
			} else {
				if z.Width == nil {
					z.Width = new(int64)
				}
				*z.Width, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Width")
					return
				}
			}
		case "Height":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Height = nil
			} else {
				if z.Height == nil {
					z.Height = new(int64)
				}
				*z.Height, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Height")
					return
				}
			}
		case "ImageType":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ImageType = nil
			} else {
				if z.ImageType == nil {
					z.ImageType = new(ImageTypeEnum)
				}
				{
					var zb0003 int64
					zb0003, bts, err = msgp.ReadInt64Bytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "ImageType")
						return
					}
					*z.ImageType = ImageTypeEnum(zb0003)
				}
			}
		case "OpenSchema":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.OpenSchema = nil
			} else {
				if z.OpenSchema == nil {
					z.OpenSchema = new(string)
				}
				*z.OpenSchema, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "OpenSchema")
					return
				}
			}
		case "Content":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Content = nil
			} else {
				if z.Content == nil {
					z.Content = new(UrlContent)
				}
				bts, err = z.Content.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Content")
					return
				}
			}
		case "IsAnimated":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.IsAnimated = nil
			} else {
				if z.IsAnimated == nil {
					z.IsAnimated = new(bool)
				}
				*z.IsAnimated, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "IsAnimated")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *UrlStruct) Msgsize() (s int) {
	s = 1 + 4 + msgp.StringPrefixSize + len(z.Uri) + 8 + msgp.ArrayHeaderSize
	for za0001 := range z.UrlList {
		s += msgp.StringPrefixSize + len(z.UrlList[za0001])
	}
	s += 6
	if z.Width == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 7
	if z.Height == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 10
	if z.ImageType == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 11
	if z.OpenSchema == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.OpenSchema)
	}
	s += 8
	if z.Content == nil {
		s += msgp.NilSize
	} else {
		s += z.Content.Msgsize()
	}
	s += 11
	if z.IsAnimated == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *UserStruct) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			z.ID, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "ID")
				return
			}
		case "ShortID":
			z.ShortID, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "ShortID")
				return
			}
		case "Nickname":
			z.Nickname, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Nickname")
				return
			}
		case "Gender":
			{
				var zb0002 int64
				zb0002, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "Gender")
					return
				}
				z.Gender = GenderEnum(zb0002)
			}
		case "Signature":
			z.Signature, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Signature")
				return
			}
		case "Level":
			z.Level, err = dc.ReadInt16()
			if err != nil {
				err = msgp.WrapError(err, "Level")
				return
			}
		case "Birthday":
			z.Birthday, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "Birthday")
				return
			}
		case "AvatarUri":
			z.AvatarUri, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "AvatarUri")
				return
			}
		case "Telephone":
			z.Telephone, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Telephone")
				return
			}
		case "AvatarLarge":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AvatarLarge")
					return
				}
				z.AvatarLarge = nil
			} else {
				if z.AvatarLarge == nil {
					z.AvatarLarge = new(UrlStruct)
				}
				err = z.AvatarLarge.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "AvatarLarge")
					return
				}
			}
		case "AvatarThumb":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AvatarThumb")
					return
				}
				z.AvatarThumb = nil
			} else {
				if z.AvatarThumb == nil {
					z.AvatarThumb = new(UrlStruct)
				}
				err = z.AvatarThumb.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "AvatarThumb")
					return
				}
			}
		case "AvatarMedium":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AvatarMedium")
					return
				}
				z.AvatarMedium = nil
			} else {
				if z.AvatarMedium == nil {
					z.AvatarMedium = new(UrlStruct)
				}
				err = z.AvatarMedium.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "AvatarMedium")
					return
				}
			}
		case "IsVerified":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "IsVerified")
					return
				}
				z.IsVerified = nil
			} else {
				if z.IsVerified == nil {
					z.IsVerified = new(bool)
				}
				*z.IsVerified, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "IsVerified")
					return
				}
			}
		case "Experience":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Experience")
					return
				}
				z.Experience = nil
			} else {
				if z.Experience == nil {
					z.Experience = new(int32)
				}
				*z.Experience, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "Experience")
					return
				}
			}
		case "City":
			z.City, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "City")
				return
			}
		case "Status":
			z.Status, err = dc.ReadInt16()
			if err != nil {
				err = msgp.WrapError(err, "Status")
				return
			}
		case "CreateTime":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "CreateTime")
					return
				}
				z.CreateTime = nil
			} else {
				if z.CreateTime == nil {
					z.CreateTime = new(int64)
				}
				*z.CreateTime, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "CreateTime")
					return
				}
			}
		case "ModifyTime":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ModifyTime")
					return
				}
				z.ModifyTime = nil
			} else {
				if z.ModifyTime == nil {
					z.ModifyTime = new(int64)
				}
				*z.ModifyTime, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "ModifyTime")
					return
				}
			}
		case "AvatarMeta":
			z.AvatarMeta, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "AvatarMeta")
				return
			}
		case "Secret":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Secret")
					return
				}
				z.Secret = nil
			} else {
				if z.Secret == nil {
					z.Secret = new(int16)
				}
				*z.Secret, err = dc.ReadInt16()
				if err != nil {
					err = msgp.WrapError(err, "Secret")
					return
				}
			}
		case "ShareQrcodeUri":
			z.ShareQrcodeUri, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "ShareQrcodeUri")
				return
			}
		case "IncomeSharePercent":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "IncomeSharePercent")
					return
				}
				z.IncomeSharePercent = nil
			} else {
				if z.IncomeSharePercent == nil {
					z.IncomeSharePercent = new(int16)
				}
				*z.IncomeSharePercent, err = dc.ReadInt16()
				if err != nil {
					err = msgp.WrapError(err, "IncomeSharePercent")
					return
				}
			}
		case "Internal":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Internal")
					return
				}
				z.Internal = nil
			} else {
				if z.Internal == nil {
					z.Internal = new(int16)
				}
				*z.Internal, err = dc.ReadInt16()
				if err != nil {
					err = msgp.WrapError(err, "Internal")
					return
				}
			}
		case "RiskFlag":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "RiskFlag")
					return
				}
				z.RiskFlag = nil
			} else {
				if z.RiskFlag == nil {
					z.RiskFlag = new(int16)
				}
				*z.RiskFlag, err = dc.ReadInt16()
				if err != nil {
					err = msgp.WrapError(err, "RiskFlag")
					return
				}
			}
		case "FilterRisk":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "FilterRisk")
					return
				}
				z.FilterRisk = nil
			} else {
				if z.FilterRisk == nil {
					z.FilterRisk = new(bool)
				}
				*z.FilterRisk, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "FilterRisk")
					return
				}
			}
		case "CityCode":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "CityCode")
					return
				}
				z.CityCode = nil
			} else {
				if z.CityCode == nil {
					z.CityCode = new(int32)
				}
				*z.CityCode, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "CityCode")
					return
				}
			}
		case "SpainUser":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "SpainUser")
					return
				}
				z.SpainUser = nil
			} else {
				if z.SpainUser == nil {
					z.SpainUser = new(int64)
				}
				*z.SpainUser, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "SpainUser")
					return
				}
			}
		case "Campaign":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Campaign")
					return
				}
				z.Campaign = nil
			} else {
				if z.Campaign == nil {
					z.Campaign = new(string)
				}
				*z.Campaign, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Campaign")
					return
				}
			}
		case "PayDiamondBak":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "PayDiamondBak")
					return
				}
				z.PayDiamondBak = nil
			} else {
				if z.PayDiamondBak == nil {
					z.PayDiamondBak = new(int64)
				}
				*z.PayDiamondBak, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "PayDiamondBak")
					return
				}
			}
		case "IsOfficial":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "IsOfficial")
					return
				}
				z.IsOfficial = nil
			} else {
				if z.IsOfficial == nil {
					z.IsOfficial = new(bool)
				}
				*z.IsOfficial, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "IsOfficial")
					return
				}
			}
		case "LiveCommentOnlyMeSee":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "LiveCommentOnlyMeSee")
					return
				}
				z.LiveCommentOnlyMeSee = nil
			} else {
				if z.LiveCommentOnlyMeSee == nil {
					z.LiveCommentOnlyMeSee = new(string)
				}
				*z.LiveCommentOnlyMeSee, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "LiveCommentOnlyMeSee")
					return
				}
			}
		case "AllowStatus":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AllowStatus")
					return
				}
				z.AllowStatus = nil
			} else {
				if z.AllowStatus == nil {
					z.AllowStatus = new(int32)
				}
				*z.AllowStatus, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "AllowStatus")
					return
				}
			}
		case "AllowBeLocated":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AllowBeLocated")
					return
				}
				z.AllowBeLocated = nil
			} else {
				if z.AllowBeLocated == nil {
					z.AllowBeLocated = new(bool)
				}
				*z.AllowBeLocated, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "AllowBeLocated")
					return
				}
			}
		case "HideLocation":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "HideLocation")
					return
				}
				z.HideLocation = nil
			} else {
				if z.HideLocation == nil {
					z.HideLocation = new(bool)
				}
				*z.HideLocation, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "HideLocation")
					return
				}
			}
		case "CoverLibraryInfo":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "CoverLibraryInfo")
					return
				}
				z.CoverLibraryInfo = nil
			} else {
				if z.CoverLibraryInfo == nil {
					z.CoverLibraryInfo = new(CoverLibrary)
				}
				err = z.CoverLibraryInfo.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "CoverLibraryInfo")
					return
				}
			}
		case "UserMode":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "UserMode")
					return
				}
				z.UserMode = nil
			} else {
				if z.UserMode == nil {
					z.UserMode = new(int32)
				}
				*z.UserMode, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "UserMode")
					return
				}
			}
		case "UserPeroid":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "UserPeroid")
					return
				}
				z.UserPeroid = nil
			} else {
				if z.UserPeroid == nil {
					z.UserPeroid = new(int32)
				}
				*z.UserPeroid, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "UserPeroid")
					return
				}
			}
		case "UserRate":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "UserRate")
					return
				}
				z.UserRate = nil
			} else {
				if z.UserRate == nil {
					z.UserRate = new(int32)
				}
				*z.UserRate, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "UserRate")
					return
				}
			}
		case "LabelIDs":
			var zb0003 uint32
			zb0003, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "LabelIDs")
				return
			}
			if cap(z.LabelIDs) >= int(zb0003) {
				z.LabelIDs = (z.LabelIDs)[:zb0003]
			} else {
				z.LabelIDs = make([]int64, zb0003)
			}
			for za0001 := range z.LabelIDs {
				z.LabelIDs[za0001], err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "LabelIDs", za0001)
					return
				}
			}
		case "DisplayID":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "DisplayID")
					return
				}
				z.DisplayID = nil
			} else {
				if z.DisplayID == nil {
					z.DisplayID = new(string)
				}
				*z.DisplayID, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "DisplayID")
					return
				}
			}
		case "LinkMicStats":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "LinkMicStats")
					return
				}
				z.LinkMicStats = nil
			} else {
				if z.LinkMicStats == nil {
					z.LinkMicStats = new(int64)
				}
				*z.LinkMicStats, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "LinkMicStats")
					return
				}
			}
		case "LiveAgreement":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "LiveAgreement")
					return
				}
				z.LiveAgreement = nil
			} else {
				if z.LiveAgreement == nil {
					z.LiveAgreement = new(int64)
				}
				*z.LiveAgreement, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "LiveAgreement")
					return
				}
			}
		case "LiveAgreementTime":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "LiveAgreementTime")
					return
				}
				z.LiveAgreementTime = nil
			} else {
				if z.LiveAgreementTime == nil {
					z.LiveAgreementTime = new(int64)
				}
				*z.LiveAgreementTime, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "LiveAgreementTime")
					return
				}
			}
		case "IsPhoneBinded":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "IsPhoneBinded")
					return
				}
				z.IsPhoneBinded = nil
			} else {
				if z.IsPhoneBinded == nil {
					z.IsPhoneBinded = new(bool)
				}
				*z.IsPhoneBinded, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "IsPhoneBinded")
					return
				}
			}
		case "LiveVerify":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "LiveVerify")
					return
				}
				z.LiveVerify = nil
			} else {
				if z.LiveVerify == nil {
					z.LiveVerify = new(int32)
				}
				*z.LiveVerify, err = dc.ReadInt32()
				if err != nil {
					err = msgp.WrapError(err, "LiveVerify")
					return
				}
			}
		case "WithCommercePermission":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "WithCommercePermission")
					return
				}
				z.WithCommercePermission = nil
			} else {
				if z.WithCommercePermission == nil {
					z.WithCommercePermission = new(bool)
				}
				*z.WithCommercePermission, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "WithCommercePermission")
					return
				}
			}
		case "WithFusionShopEntry":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "WithFusionShopEntry")
					return
				}
				z.WithFusionShopEntry = nil
			} else {
				if z.WithFusionShopEntry == nil {
					z.WithFusionShopEntry = new(bool)
				}
				*z.WithFusionShopEntry, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "WithFusionShopEntry")
					return
				}
			}
		case "AnchorLevel":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AnchorLevel")
					return
				}
				z.AnchorLevel = nil
			} else {
				if z.AnchorLevel == nil {
					z.AnchorLevel = new(AnchorLevelStats)
				}
				err = z.AnchorLevel.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "AnchorLevel")
					return
				}
			}
		case "VerifiedContent":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "VerifiedContent")
					return
				}
				z.VerifiedContent = nil
			} else {
				if z.VerifiedContent == nil {
					z.VerifiedContent = new(string)
				}
				*z.VerifiedContent, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "VerifiedContent")
					return
				}
			}
		case "HasPrivilege":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "HasPrivilege")
					return
				}
				z.HasPrivilege = nil
			} else {
				if z.HasPrivilege == nil {
					z.HasPrivilege = new(bool)
				}
				*z.HasPrivilege, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "HasPrivilege")
					return
				}
			}
		case "SpecialID":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "SpecialID")
					return
				}
				z.SpecialID = nil
			} else {
				if z.SpecialID == nil {
					z.SpecialID = new(string)
				}
				*z.SpecialID, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "SpecialID")
					return
				}
			}
		case "PayScores":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "PayScores")
					return
				}
				z.PayScores = nil
			} else {
				if z.PayScores == nil {
					z.PayScores = new(int64)
				}
				*z.PayScores, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "PayScores")
					return
				}
			}
		case "BgImgUrl":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "BgImgUrl")
					return
				}
				z.BgImgUrl = nil
			} else {
				if z.BgImgUrl == nil {
					z.BgImgUrl = new(string)
				}
				*z.BgImgUrl, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "BgImgUrl")
					return
				}
			}
		case "RoomAutoGiftThanks":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "RoomAutoGiftThanks")
					return
				}
				z.RoomAutoGiftThanks = nil
			} else {
				if z.RoomAutoGiftThanks == nil {
					z.RoomAutoGiftThanks = new(bool)
				}
				*z.RoomAutoGiftThanks, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "RoomAutoGiftThanks")
					return
				}
			}
		case "FakeUser":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "FakeUser")
					return
				}
				z.FakeUser = nil
			} else {
				if z.FakeUser == nil {
					z.FakeUser = new(bool)
				}
				*z.FakeUser, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "FakeUser")
					return
				}
			}
		case "SecUid":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "SecUid")
					return
				}
				z.SecUid = nil
			} else {
				if z.SecUid == nil {
					z.SecUid = new(string)
				}
				*z.SecUid, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "SecUid")
					return
				}
			}
		case "ForbidWithdrawal":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ForbidWithdrawal")
					return
				}
				z.ForbidWithdrawal = nil
			} else {
				if z.ForbidWithdrawal == nil {
					z.ForbidWithdrawal = new(int16)
				}
				*z.ForbidWithdrawal, err = dc.ReadInt16()
				if err != nil {
					err = msgp.WrapError(err, "ForbidWithdrawal")
					return
				}
			}
		case "Language":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Language")
					return
				}
				z.Language = nil
			} else {
				if z.Language == nil {
					z.Language = new(string)
				}
				*z.Language, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Language")
					return
				}
			}
		case "AppID":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "AppID")
					return
				}
				z.AppID = nil
			} else {
				if z.AppID == nil {
					z.AppID = new(int64)
				}
				*z.AppID, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "AppID")
					return
				}
			}
		case "Region":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Region")
					return
				}
				z.Region = nil
			} else {
				if z.Region == nil {
					z.Region = new(string)
				}
				*z.Region, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "Region")
					return
				}
			}
		case "WithCarManagementPermission":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "WithCarManagementPermission")
					return
				}
				z.WithCarManagementPermission = nil
			} else {
				if z.WithCarManagementPermission == nil {
					z.WithCarManagementPermission = new(bool)
				}
				*z.WithCarManagementPermission, err = dc.ReadBool()
				if err != nil {
					err = msgp.WrapError(err, "WithCarManagementPermission")
					return
				}
			}
		case "UserRateMap":
			var zb0004 uint32
			zb0004, err = dc.ReadMapHeader()
			if err != nil {
				err = msgp.WrapError(err, "UserRateMap")
				return
			}
			if z.UserRateMap == nil {
				z.UserRateMap = make(map[string]int64, zb0004)
			} else if len(z.UserRateMap) > 0 {
				for key := range z.UserRateMap {
					delete(z.UserRateMap, key)
				}
			}
			for zb0004 > 0 {
				zb0004--
				var za0002 string
				var za0003 int64
				za0002, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "UserRateMap")
					return
				}
				za0003, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "UserRateMap", za0002)
					return
				}
				z.UserRateMap[za0002] = za0003
			}
		case "CustomVerify":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "CustomVerify")
					return
				}
				z.CustomVerify = nil
			} else {
				if z.CustomVerify == nil {
					z.CustomVerify = new(string)
				}
				*z.CustomVerify, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "CustomVerify")
					return
				}
			}
		case "EnterpriseVerifyReason":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "EnterpriseVerifyReason")
					return
				}
				z.EnterpriseVerifyReason = nil
			} else {
				if z.EnterpriseVerifyReason == nil {
					z.EnterpriseVerifyReason = new(string)
				}
				*z.EnterpriseVerifyReason, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "EnterpriseVerifyReason")
					return
				}
			}
		case "ShowGenderStrategy":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "ShowGenderStrategy")
					return
				}
				z.ShowGenderStrategy = nil
			} else {
				if z.ShowGenderStrategy == nil {
					z.ShowGenderStrategy = new(string)
				}
				*z.ShowGenderStrategy, err = dc.ReadString()
				if err != nil {
					err = msgp.WrapError(err, "ShowGenderStrategy")
					return
				}
			}
		case "RegisterFrom":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "RegisterFrom")
					return
				}
				z.RegisterFrom = nil
			} else {
				if z.RegisterFrom == nil {
					z.RegisterFrom = new(VCDRegisterFrom)
				}
				{
					var zb0005 int64
					zb0005, err = dc.ReadInt64()
					if err != nil {
						err = msgp.WrapError(err, "RegisterFrom")
						return
					}
					*z.RegisterFrom = VCDRegisterFrom(zb0005)
				}
			}
		case "BaseInfoAuth":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "BaseInfoAuth")
					return
				}
				z.BaseInfoAuth = nil
			} else {
				if z.BaseInfoAuth == nil {
					z.BaseInfoAuth = new(VCDAuth)
				}
				{
					var zb0006 int64
					zb0006, err = dc.ReadInt64()
					if err != nil {
						err = msgp.WrapError(err, "BaseInfoAuth")
						return
					}
					*z.BaseInfoAuth = VCDAuth(zb0006)
				}
			}
		case "RealtionAuth":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "RealtionAuth")
					return
				}
				z.RealtionAuth = nil
			} else {
				if z.RealtionAuth == nil {
					z.RealtionAuth = new(VCDAuth)
				}
				{
					var zb0007 int64
					zb0007, err = dc.ReadInt64()
					if err != nil {
						err = msgp.WrapError(err, "RealtionAuth")
						return
					}
					*z.RealtionAuth = VCDAuth(zb0007)
				}
			}
		case "CityCodeI64":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "CityCodeI64")
					return
				}
				z.CityCodeI64 = nil
			} else {
				if z.CityCodeI64 == nil {
					z.CityCodeI64 = new(int64)
				}
				*z.CityCodeI64, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, "CityCodeI64")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *UserStruct) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 69
	// write "ID"
	err = en.Append(0xde, 0x0, 0x45, 0xa2, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.ID)
	if err != nil {
		err = msgp.WrapError(err, "ID")
		return
	}
	// write "ShortID"
	err = en.Append(0xa7, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x49, 0x44)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.ShortID)
	if err != nil {
		err = msgp.WrapError(err, "ShortID")
		return
	}
	// write "Nickname"
	err = en.Append(0xa8, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Nickname)
	if err != nil {
		err = msgp.WrapError(err, "Nickname")
		return
	}
	// write "Gender"
	err = en.Append(0xa6, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72)
	if err != nil {
		return
	}
	err = en.WriteInt64(int64(z.Gender))
	if err != nil {
		err = msgp.WrapError(err, "Gender")
		return
	}
	// write "Signature"
	err = en.Append(0xa9, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Signature)
	if err != nil {
		err = msgp.WrapError(err, "Signature")
		return
	}
	// write "Level"
	err = en.Append(0xa5, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	err = en.WriteInt16(z.Level)
	if err != nil {
		err = msgp.WrapError(err, "Level")
		return
	}
	// write "Birthday"
	err = en.Append(0xa8, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.Birthday)
	if err != nil {
		err = msgp.WrapError(err, "Birthday")
		return
	}
	// write "AvatarUri"
	err = en.Append(0xa9, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x69)
	if err != nil {
		return
	}
	err = en.WriteString(z.AvatarUri)
	if err != nil {
		err = msgp.WrapError(err, "AvatarUri")
		return
	}
	// write "Telephone"
	err = en.Append(0xa9, 0x54, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65)
	if err != nil {
		return
	}
	err = en.WriteString(z.Telephone)
	if err != nil {
		err = msgp.WrapError(err, "Telephone")
		return
	}
	// write "AvatarLarge"
	err = en.Append(0xab, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x61, 0x72, 0x67, 0x65)
	if err != nil {
		return
	}
	if z.AvatarLarge == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.AvatarLarge.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "AvatarLarge")
			return
		}
	}
	// write "AvatarThumb"
	err = en.Append(0xab, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x68, 0x75, 0x6d, 0x62)
	if err != nil {
		return
	}
	if z.AvatarThumb == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.AvatarThumb.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "AvatarThumb")
			return
		}
	}
	// write "AvatarMedium"
	err = en.Append(0xac, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d)
	if err != nil {
		return
	}
	if z.AvatarMedium == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.AvatarMedium.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "AvatarMedium")
			return
		}
	}
	// write "IsVerified"
	err = en.Append(0xaa, 0x49, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64)
	if err != nil {
		return
	}
	if z.IsVerified == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.IsVerified)
		if err != nil {
			err = msgp.WrapError(err, "IsVerified")
			return
		}
	}
	// write "Experience"
	err = en.Append(0xaa, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if err != nil {
		return
	}
	if z.Experience == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.Experience)
		if err != nil {
			err = msgp.WrapError(err, "Experience")
			return
		}
	}
	// write "City"
	err = en.Append(0xa4, 0x43, 0x69, 0x74, 0x79)
	if err != nil {
		return
	}
	err = en.WriteString(z.City)
	if err != nil {
		err = msgp.WrapError(err, "City")
		return
	}
	// write "Status"
	err = en.Append(0xa6, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73)
	if err != nil {
		return
	}
	err = en.WriteInt16(z.Status)
	if err != nil {
		err = msgp.WrapError(err, "Status")
		return
	}
	// write "CreateTime"
	err = en.Append(0xaa, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	if z.CreateTime == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.CreateTime)
		if err != nil {
			err = msgp.WrapError(err, "CreateTime")
			return
		}
	}
	// write "ModifyTime"
	err = en.Append(0xaa, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	if z.ModifyTime == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.ModifyTime)
		if err != nil {
			err = msgp.WrapError(err, "ModifyTime")
			return
		}
	}
	// write "AvatarMeta"
	err = en.Append(0xaa, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x74, 0x61)
	if err != nil {
		return
	}
	err = en.WriteString(z.AvatarMeta)
	if err != nil {
		err = msgp.WrapError(err, "AvatarMeta")
		return
	}
	// write "Secret"
	err = en.Append(0xa6, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74)
	if err != nil {
		return
	}
	if z.Secret == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt16(*z.Secret)
		if err != nil {
			err = msgp.WrapError(err, "Secret")
			return
		}
	}
	// write "ShareQrcodeUri"
	err = en.Append(0xae, 0x53, 0x68, 0x61, 0x72, 0x65, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x55, 0x72, 0x69)
	if err != nil {
		return
	}
	err = en.WriteString(z.ShareQrcodeUri)
	if err != nil {
		err = msgp.WrapError(err, "ShareQrcodeUri")
		return
	}
	// write "IncomeSharePercent"
	err = en.Append(0xb2, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74)
	if err != nil {
		return
	}
	if z.IncomeSharePercent == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt16(*z.IncomeSharePercent)
		if err != nil {
			err = msgp.WrapError(err, "IncomeSharePercent")
			return
		}
	}
	// write "Internal"
	err = en.Append(0xa8, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c)
	if err != nil {
		return
	}
	if z.Internal == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt16(*z.Internal)
		if err != nil {
			err = msgp.WrapError(err, "Internal")
			return
		}
	}
	// write "RiskFlag"
	err = en.Append(0xa8, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x6c, 0x61, 0x67)
	if err != nil {
		return
	}
	if z.RiskFlag == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt16(*z.RiskFlag)
		if err != nil {
			err = msgp.WrapError(err, "RiskFlag")
			return
		}
	}
	// write "FilterRisk"
	err = en.Append(0xaa, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x69, 0x73, 0x6b)
	if err != nil {
		return
	}
	if z.FilterRisk == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.FilterRisk)
		if err != nil {
			err = msgp.WrapError(err, "FilterRisk")
			return
		}
	}
	// write "CityCode"
	err = en.Append(0xa8, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65)
	if err != nil {
		return
	}
	if z.CityCode == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.CityCode)
		if err != nil {
			err = msgp.WrapError(err, "CityCode")
			return
		}
	}
	// write "SpainUser"
	err = en.Append(0xa9, 0x53, 0x70, 0x61, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72)
	if err != nil {
		return
	}
	if z.SpainUser == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.SpainUser)
		if err != nil {
			err = msgp.WrapError(err, "SpainUser")
			return
		}
	}
	// write "Campaign"
	err = en.Append(0xa8, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e)
	if err != nil {
		return
	}
	if z.Campaign == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Campaign)
		if err != nil {
			err = msgp.WrapError(err, "Campaign")
			return
		}
	}
	// write "PayDiamondBak"
	err = en.Append(0xad, 0x50, 0x61, 0x79, 0x44, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x42, 0x61, 0x6b)
	if err != nil {
		return
	}
	if z.PayDiamondBak == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.PayDiamondBak)
		if err != nil {
			err = msgp.WrapError(err, "PayDiamondBak")
			return
		}
	}
	// write "IsOfficial"
	err = en.Append(0xaa, 0x49, 0x73, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c)
	if err != nil {
		return
	}
	if z.IsOfficial == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.IsOfficial)
		if err != nil {
			err = msgp.WrapError(err, "IsOfficial")
			return
		}
	}
	// write "LiveCommentOnlyMeSee"
	err = en.Append(0xb4, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x6c, 0x79, 0x4d, 0x65, 0x53, 0x65, 0x65)
	if err != nil {
		return
	}
	if z.LiveCommentOnlyMeSee == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.LiveCommentOnlyMeSee)
		if err != nil {
			err = msgp.WrapError(err, "LiveCommentOnlyMeSee")
			return
		}
	}
	// write "AllowStatus"
	err = en.Append(0xab, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73)
	if err != nil {
		return
	}
	if z.AllowStatus == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.AllowStatus)
		if err != nil {
			err = msgp.WrapError(err, "AllowStatus")
			return
		}
	}
	// write "AllowBeLocated"
	err = en.Append(0xae, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64)
	if err != nil {
		return
	}
	if z.AllowBeLocated == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.AllowBeLocated)
		if err != nil {
			err = msgp.WrapError(err, "AllowBeLocated")
			return
		}
	}
	// write "HideLocation"
	err = en.Append(0xac, 0x48, 0x69, 0x64, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e)
	if err != nil {
		return
	}
	if z.HideLocation == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.HideLocation)
		if err != nil {
			err = msgp.WrapError(err, "HideLocation")
			return
		}
	}
	// write "CoverLibraryInfo"
	err = en.Append(0xb0, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f)
	if err != nil {
		return
	}
	if z.CoverLibraryInfo == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.CoverLibraryInfo.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "CoverLibraryInfo")
			return
		}
	}
	// write "UserMode"
	err = en.Append(0xa8, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65)
	if err != nil {
		return
	}
	if z.UserMode == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.UserMode)
		if err != nil {
			err = msgp.WrapError(err, "UserMode")
			return
		}
	}
	// write "UserPeroid"
	err = en.Append(0xaa, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6f, 0x69, 0x64)
	if err != nil {
		return
	}
	if z.UserPeroid == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.UserPeroid)
		if err != nil {
			err = msgp.WrapError(err, "UserPeroid")
			return
		}
	}
	// write "UserRate"
	err = en.Append(0xa8, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65)
	if err != nil {
		return
	}
	if z.UserRate == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.UserRate)
		if err != nil {
			err = msgp.WrapError(err, "UserRate")
			return
		}
	}
	// write "LabelIDs"
	err = en.Append(0xa8, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x44, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.LabelIDs)))
	if err != nil {
		err = msgp.WrapError(err, "LabelIDs")
		return
	}
	for za0001 := range z.LabelIDs {
		err = en.WriteInt64(z.LabelIDs[za0001])
		if err != nil {
			err = msgp.WrapError(err, "LabelIDs", za0001)
			return
		}
	}
	// write "DisplayID"
	err = en.Append(0xa9, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x44)
	if err != nil {
		return
	}
	if z.DisplayID == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.DisplayID)
		if err != nil {
			err = msgp.WrapError(err, "DisplayID")
			return
		}
	}
	// write "LinkMicStats"
	err = en.Append(0xac, 0x4c, 0x69, 0x6e, 0x6b, 0x4d, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x73)
	if err != nil {
		return
	}
	if z.LinkMicStats == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.LinkMicStats)
		if err != nil {
			err = msgp.WrapError(err, "LinkMicStats")
			return
		}
	}
	// write "LiveAgreement"
	err = en.Append(0xad, 0x4c, 0x69, 0x76, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74)
	if err != nil {
		return
	}
	if z.LiveAgreement == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.LiveAgreement)
		if err != nil {
			err = msgp.WrapError(err, "LiveAgreement")
			return
		}
	}
	// write "LiveAgreementTime"
	err = en.Append(0xb1, 0x4c, 0x69, 0x76, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65)
	if err != nil {
		return
	}
	if z.LiveAgreementTime == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.LiveAgreementTime)
		if err != nil {
			err = msgp.WrapError(err, "LiveAgreementTime")
			return
		}
	}
	// write "IsPhoneBinded"
	err = en.Append(0xad, 0x49, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x65, 0x64)
	if err != nil {
		return
	}
	if z.IsPhoneBinded == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.IsPhoneBinded)
		if err != nil {
			err = msgp.WrapError(err, "IsPhoneBinded")
			return
		}
	}
	// write "LiveVerify"
	err = en.Append(0xaa, 0x4c, 0x69, 0x76, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79)
	if err != nil {
		return
	}
	if z.LiveVerify == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt32(*z.LiveVerify)
		if err != nil {
			err = msgp.WrapError(err, "LiveVerify")
			return
		}
	}
	// write "WithCommercePermission"
	err = en.Append(0xb6, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e)
	if err != nil {
		return
	}
	if z.WithCommercePermission == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.WithCommercePermission)
		if err != nil {
			err = msgp.WrapError(err, "WithCommercePermission")
			return
		}
	}
	// write "WithFusionShopEntry"
	err = en.Append(0xb3, 0x57, 0x69, 0x74, 0x68, 0x46, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x6f, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79)
	if err != nil {
		return
	}
	if z.WithFusionShopEntry == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.WithFusionShopEntry)
		if err != nil {
			err = msgp.WrapError(err, "WithFusionShopEntry")
			return
		}
	}
	// write "AnchorLevel"
	err = en.Append(0xab, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if err != nil {
		return
	}
	if z.AnchorLevel == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.AnchorLevel.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "AnchorLevel")
			return
		}
	}
	// write "VerifiedContent"
	err = en.Append(0xaf, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74)
	if err != nil {
		return
	}
	if z.VerifiedContent == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.VerifiedContent)
		if err != nil {
			err = msgp.WrapError(err, "VerifiedContent")
			return
		}
	}
	// write "HasPrivilege"
	err = en.Append(0xac, 0x48, 0x61, 0x73, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65)
	if err != nil {
		return
	}
	if z.HasPrivilege == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.HasPrivilege)
		if err != nil {
			err = msgp.WrapError(err, "HasPrivilege")
			return
		}
	}
	// write "SpecialID"
	err = en.Append(0xa9, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x44)
	if err != nil {
		return
	}
	if z.SpecialID == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.SpecialID)
		if err != nil {
			err = msgp.WrapError(err, "SpecialID")
			return
		}
	}
	// write "PayScores"
	err = en.Append(0xa9, 0x50, 0x61, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73)
	if err != nil {
		return
	}
	if z.PayScores == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.PayScores)
		if err != nil {
			err = msgp.WrapError(err, "PayScores")
			return
		}
	}
	// write "BgImgUrl"
	err = en.Append(0xa8, 0x42, 0x67, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c)
	if err != nil {
		return
	}
	if z.BgImgUrl == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.BgImgUrl)
		if err != nil {
			err = msgp.WrapError(err, "BgImgUrl")
			return
		}
	}
	// write "RoomAutoGiftThanks"
	err = en.Append(0xb2, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x75, 0x74, 0x6f, 0x47, 0x69, 0x66, 0x74, 0x54, 0x68, 0x61, 0x6e, 0x6b, 0x73)
	if err != nil {
		return
	}
	if z.RoomAutoGiftThanks == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.RoomAutoGiftThanks)
		if err != nil {
			err = msgp.WrapError(err, "RoomAutoGiftThanks")
			return
		}
	}
	// write "FakeUser"
	err = en.Append(0xa8, 0x46, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72)
	if err != nil {
		return
	}
	if z.FakeUser == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.FakeUser)
		if err != nil {
			err = msgp.WrapError(err, "FakeUser")
			return
		}
	}
	// write "SecUid"
	err = en.Append(0xa6, 0x53, 0x65, 0x63, 0x55, 0x69, 0x64)
	if err != nil {
		return
	}
	if z.SecUid == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.SecUid)
		if err != nil {
			err = msgp.WrapError(err, "SecUid")
			return
		}
	}
	// write "ForbidWithdrawal"
	err = en.Append(0xb0, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c)
	if err != nil {
		return
	}
	if z.ForbidWithdrawal == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt16(*z.ForbidWithdrawal)
		if err != nil {
			err = msgp.WrapError(err, "ForbidWithdrawal")
			return
		}
	}
	// write "Language"
	err = en.Append(0xa8, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65)
	if err != nil {
		return
	}
	if z.Language == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Language)
		if err != nil {
			err = msgp.WrapError(err, "Language")
			return
		}
	}
	// write "AppID"
	err = en.Append(0xa5, 0x41, 0x70, 0x70, 0x49, 0x44)
	if err != nil {
		return
	}
	if z.AppID == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.AppID)
		if err != nil {
			err = msgp.WrapError(err, "AppID")
			return
		}
	}
	// write "Region"
	err = en.Append(0xa6, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e)
	if err != nil {
		return
	}
	if z.Region == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.Region)
		if err != nil {
			err = msgp.WrapError(err, "Region")
			return
		}
	}
	// write "WithCarManagementPermission"
	err = en.Append(0xbb, 0x57, 0x69, 0x74, 0x68, 0x43, 0x61, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e)
	if err != nil {
		return
	}
	if z.WithCarManagementPermission == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteBool(*z.WithCarManagementPermission)
		if err != nil {
			err = msgp.WrapError(err, "WithCarManagementPermission")
			return
		}
	}
	// write "UserRateMap"
	err = en.Append(0xab, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70)
	if err != nil {
		return
	}
	err = en.WriteMapHeader(uint32(len(z.UserRateMap)))
	if err != nil {
		err = msgp.WrapError(err, "UserRateMap")
		return
	}
	for za0002, za0003 := range z.UserRateMap {
		err = en.WriteString(za0002)
		if err != nil {
			err = msgp.WrapError(err, "UserRateMap")
			return
		}
		err = en.WriteInt64(za0003)
		if err != nil {
			err = msgp.WrapError(err, "UserRateMap", za0002)
			return
		}
	}
	// write "CustomVerify"
	err = en.Append(0xac, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79)
	if err != nil {
		return
	}
	if z.CustomVerify == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.CustomVerify)
		if err != nil {
			err = msgp.WrapError(err, "CustomVerify")
			return
		}
	}
	// write "EnterpriseVerifyReason"
	err = en.Append(0xb6, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e)
	if err != nil {
		return
	}
	if z.EnterpriseVerifyReason == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.EnterpriseVerifyReason)
		if err != nil {
			err = msgp.WrapError(err, "EnterpriseVerifyReason")
			return
		}
	}
	// write "ShowGenderStrategy"
	err = en.Append(0xb2, 0x53, 0x68, 0x6f, 0x77, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79)
	if err != nil {
		return
	}
	if z.ShowGenderStrategy == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteString(*z.ShowGenderStrategy)
		if err != nil {
			err = msgp.WrapError(err, "ShowGenderStrategy")
			return
		}
	}
	// write "RegisterFrom"
	err = en.Append(0xac, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d)
	if err != nil {
		return
	}
	if z.RegisterFrom == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(int64(*z.RegisterFrom))
		if err != nil {
			err = msgp.WrapError(err, "RegisterFrom")
			return
		}
	}
	// write "BaseInfoAuth"
	err = en.Append(0xac, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x75, 0x74, 0x68)
	if err != nil {
		return
	}
	if z.BaseInfoAuth == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(int64(*z.BaseInfoAuth))
		if err != nil {
			err = msgp.WrapError(err, "BaseInfoAuth")
			return
		}
	}
	// write "RealtionAuth"
	err = en.Append(0xac, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x75, 0x74, 0x68)
	if err != nil {
		return
	}
	if z.RealtionAuth == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(int64(*z.RealtionAuth))
		if err != nil {
			err = msgp.WrapError(err, "RealtionAuth")
			return
		}
	}
	// write "CityCodeI64"
	err = en.Append(0xab, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x36, 0x34)
	if err != nil {
		return
	}
	if z.CityCodeI64 == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = en.WriteInt64(*z.CityCodeI64)
		if err != nil {
			err = msgp.WrapError(err, "CityCodeI64")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *UserStruct) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 69
	// string "ID"
	o = append(o, 0xde, 0x0, 0x45, 0xa2, 0x49, 0x44)
	o = msgp.AppendInt64(o, z.ID)
	// string "ShortID"
	o = append(o, 0xa7, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x49, 0x44)
	o = msgp.AppendInt64(o, z.ShortID)
	// string "Nickname"
	o = append(o, 0xa8, 0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65)
	o = msgp.AppendString(o, z.Nickname)
	// string "Gender"
	o = append(o, 0xa6, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72)
	o = msgp.AppendInt64(o, int64(z.Gender))
	// string "Signature"
	o = append(o, 0xa9, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65)
	o = msgp.AppendString(o, z.Signature)
	// string "Level"
	o = append(o, 0xa5, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	o = msgp.AppendInt16(o, z.Level)
	// string "Birthday"
	o = append(o, 0xa8, 0x42, 0x69, 0x72, 0x74, 0x68, 0x64, 0x61, 0x79)
	o = msgp.AppendInt64(o, z.Birthday)
	// string "AvatarUri"
	o = append(o, 0xa9, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x69)
	o = msgp.AppendString(o, z.AvatarUri)
	// string "Telephone"
	o = append(o, 0xa9, 0x54, 0x65, 0x6c, 0x65, 0x70, 0x68, 0x6f, 0x6e, 0x65)
	o = msgp.AppendString(o, z.Telephone)
	// string "AvatarLarge"
	o = append(o, 0xab, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x61, 0x72, 0x67, 0x65)
	if z.AvatarLarge == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.AvatarLarge.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "AvatarLarge")
			return
		}
	}
	// string "AvatarThumb"
	o = append(o, 0xab, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x68, 0x75, 0x6d, 0x62)
	if z.AvatarThumb == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.AvatarThumb.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "AvatarThumb")
			return
		}
	}
	// string "AvatarMedium"
	o = append(o, 0xac, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d)
	if z.AvatarMedium == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.AvatarMedium.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "AvatarMedium")
			return
		}
	}
	// string "IsVerified"
	o = append(o, 0xaa, 0x49, 0x73, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64)
	if z.IsVerified == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.IsVerified)
	}
	// string "Experience"
	o = append(o, 0xaa, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x65, 0x6e, 0x63, 0x65)
	if z.Experience == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.Experience)
	}
	// string "City"
	o = append(o, 0xa4, 0x43, 0x69, 0x74, 0x79)
	o = msgp.AppendString(o, z.City)
	// string "Status"
	o = append(o, 0xa6, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73)
	o = msgp.AppendInt16(o, z.Status)
	// string "CreateTime"
	o = append(o, 0xaa, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65)
	if z.CreateTime == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.CreateTime)
	}
	// string "ModifyTime"
	o = append(o, 0xaa, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65)
	if z.ModifyTime == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.ModifyTime)
	}
	// string "AvatarMeta"
	o = append(o, 0xaa, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4d, 0x65, 0x74, 0x61)
	o = msgp.AppendString(o, z.AvatarMeta)
	// string "Secret"
	o = append(o, 0xa6, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74)
	if z.Secret == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt16(o, *z.Secret)
	}
	// string "ShareQrcodeUri"
	o = append(o, 0xae, 0x53, 0x68, 0x61, 0x72, 0x65, 0x51, 0x72, 0x63, 0x6f, 0x64, 0x65, 0x55, 0x72, 0x69)
	o = msgp.AppendString(o, z.ShareQrcodeUri)
	// string "IncomeSharePercent"
	o = append(o, 0xb2, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74)
	if z.IncomeSharePercent == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt16(o, *z.IncomeSharePercent)
	}
	// string "Internal"
	o = append(o, 0xa8, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c)
	if z.Internal == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt16(o, *z.Internal)
	}
	// string "RiskFlag"
	o = append(o, 0xa8, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x6c, 0x61, 0x67)
	if z.RiskFlag == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt16(o, *z.RiskFlag)
	}
	// string "FilterRisk"
	o = append(o, 0xaa, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x69, 0x73, 0x6b)
	if z.FilterRisk == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.FilterRisk)
	}
	// string "CityCode"
	o = append(o, 0xa8, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65)
	if z.CityCode == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.CityCode)
	}
	// string "SpainUser"
	o = append(o, 0xa9, 0x53, 0x70, 0x61, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72)
	if z.SpainUser == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.SpainUser)
	}
	// string "Campaign"
	o = append(o, 0xa8, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e)
	if z.Campaign == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Campaign)
	}
	// string "PayDiamondBak"
	o = append(o, 0xad, 0x50, 0x61, 0x79, 0x44, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x42, 0x61, 0x6b)
	if z.PayDiamondBak == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.PayDiamondBak)
	}
	// string "IsOfficial"
	o = append(o, 0xaa, 0x49, 0x73, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x69, 0x61, 0x6c)
	if z.IsOfficial == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.IsOfficial)
	}
	// string "LiveCommentOnlyMeSee"
	o = append(o, 0xb4, 0x4c, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x6e, 0x6c, 0x79, 0x4d, 0x65, 0x53, 0x65, 0x65)
	if z.LiveCommentOnlyMeSee == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.LiveCommentOnlyMeSee)
	}
	// string "AllowStatus"
	o = append(o, 0xab, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73)
	if z.AllowStatus == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.AllowStatus)
	}
	// string "AllowBeLocated"
	o = append(o, 0xae, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x42, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64)
	if z.AllowBeLocated == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.AllowBeLocated)
	}
	// string "HideLocation"
	o = append(o, 0xac, 0x48, 0x69, 0x64, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e)
	if z.HideLocation == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.HideLocation)
	}
	// string "CoverLibraryInfo"
	o = append(o, 0xb0, 0x43, 0x6f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f)
	if z.CoverLibraryInfo == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.CoverLibraryInfo.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "CoverLibraryInfo")
			return
		}
	}
	// string "UserMode"
	o = append(o, 0xa8, 0x55, 0x73, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65)
	if z.UserMode == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.UserMode)
	}
	// string "UserPeroid"
	o = append(o, 0xaa, 0x55, 0x73, 0x65, 0x72, 0x50, 0x65, 0x72, 0x6f, 0x69, 0x64)
	if z.UserPeroid == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.UserPeroid)
	}
	// string "UserRate"
	o = append(o, 0xa8, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65)
	if z.UserRate == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.UserRate)
	}
	// string "LabelIDs"
	o = append(o, 0xa8, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x44, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.LabelIDs)))
	for za0001 := range z.LabelIDs {
		o = msgp.AppendInt64(o, z.LabelIDs[za0001])
	}
	// string "DisplayID"
	o = append(o, 0xa9, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x49, 0x44)
	if z.DisplayID == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.DisplayID)
	}
	// string "LinkMicStats"
	o = append(o, 0xac, 0x4c, 0x69, 0x6e, 0x6b, 0x4d, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x73)
	if z.LinkMicStats == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.LinkMicStats)
	}
	// string "LiveAgreement"
	o = append(o, 0xad, 0x4c, 0x69, 0x76, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74)
	if z.LiveAgreement == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.LiveAgreement)
	}
	// string "LiveAgreementTime"
	o = append(o, 0xb1, 0x4c, 0x69, 0x76, 0x65, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65)
	if z.LiveAgreementTime == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.LiveAgreementTime)
	}
	// string "IsPhoneBinded"
	o = append(o, 0xad, 0x49, 0x73, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x65, 0x64)
	if z.IsPhoneBinded == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.IsPhoneBinded)
	}
	// string "LiveVerify"
	o = append(o, 0xaa, 0x4c, 0x69, 0x76, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79)
	if z.LiveVerify == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt32(o, *z.LiveVerify)
	}
	// string "WithCommercePermission"
	o = append(o, 0xb6, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x72, 0x63, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e)
	if z.WithCommercePermission == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.WithCommercePermission)
	}
	// string "WithFusionShopEntry"
	o = append(o, 0xb3, 0x57, 0x69, 0x74, 0x68, 0x46, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x53, 0x68, 0x6f, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79)
	if z.WithFusionShopEntry == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.WithFusionShopEntry)
	}
	// string "AnchorLevel"
	o = append(o, 0xab, 0x41, 0x6e, 0x63, 0x68, 0x6f, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c)
	if z.AnchorLevel == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.AnchorLevel.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "AnchorLevel")
			return
		}
	}
	// string "VerifiedContent"
	o = append(o, 0xaf, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74)
	if z.VerifiedContent == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.VerifiedContent)
	}
	// string "HasPrivilege"
	o = append(o, 0xac, 0x48, 0x61, 0x73, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65)
	if z.HasPrivilege == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.HasPrivilege)
	}
	// string "SpecialID"
	o = append(o, 0xa9, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x44)
	if z.SpecialID == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.SpecialID)
	}
	// string "PayScores"
	o = append(o, 0xa9, 0x50, 0x61, 0x79, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x73)
	if z.PayScores == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.PayScores)
	}
	// string "BgImgUrl"
	o = append(o, 0xa8, 0x42, 0x67, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c)
	if z.BgImgUrl == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.BgImgUrl)
	}
	// string "RoomAutoGiftThanks"
	o = append(o, 0xb2, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x75, 0x74, 0x6f, 0x47, 0x69, 0x66, 0x74, 0x54, 0x68, 0x61, 0x6e, 0x6b, 0x73)
	if z.RoomAutoGiftThanks == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.RoomAutoGiftThanks)
	}
	// string "FakeUser"
	o = append(o, 0xa8, 0x46, 0x61, 0x6b, 0x65, 0x55, 0x73, 0x65, 0x72)
	if z.FakeUser == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.FakeUser)
	}
	// string "SecUid"
	o = append(o, 0xa6, 0x53, 0x65, 0x63, 0x55, 0x69, 0x64)
	if z.SecUid == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.SecUid)
	}
	// string "ForbidWithdrawal"
	o = append(o, 0xb0, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x57, 0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x61, 0x6c)
	if z.ForbidWithdrawal == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt16(o, *z.ForbidWithdrawal)
	}
	// string "Language"
	o = append(o, 0xa8, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65)
	if z.Language == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Language)
	}
	// string "AppID"
	o = append(o, 0xa5, 0x41, 0x70, 0x70, 0x49, 0x44)
	if z.AppID == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.AppID)
	}
	// string "Region"
	o = append(o, 0xa6, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e)
	if z.Region == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.Region)
	}
	// string "WithCarManagementPermission"
	o = append(o, 0xbb, 0x57, 0x69, 0x74, 0x68, 0x43, 0x61, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e)
	if z.WithCarManagementPermission == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendBool(o, *z.WithCarManagementPermission)
	}
	// string "UserRateMap"
	o = append(o, 0xab, 0x55, 0x73, 0x65, 0x72, 0x52, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x70)
	o = msgp.AppendMapHeader(o, uint32(len(z.UserRateMap)))
	for za0002, za0003 := range z.UserRateMap {
		o = msgp.AppendString(o, za0002)
		o = msgp.AppendInt64(o, za0003)
	}
	// string "CustomVerify"
	o = append(o, 0xac, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79)
	if z.CustomVerify == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.CustomVerify)
	}
	// string "EnterpriseVerifyReason"
	o = append(o, 0xb6, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x70, 0x72, 0x69, 0x73, 0x65, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e)
	if z.EnterpriseVerifyReason == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.EnterpriseVerifyReason)
	}
	// string "ShowGenderStrategy"
	o = append(o, 0xb2, 0x53, 0x68, 0x6f, 0x77, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79)
	if z.ShowGenderStrategy == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendString(o, *z.ShowGenderStrategy)
	}
	// string "RegisterFrom"
	o = append(o, 0xac, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x46, 0x72, 0x6f, 0x6d)
	if z.RegisterFrom == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, int64(*z.RegisterFrom))
	}
	// string "BaseInfoAuth"
	o = append(o, 0xac, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x75, 0x74, 0x68)
	if z.BaseInfoAuth == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, int64(*z.BaseInfoAuth))
	}
	// string "RealtionAuth"
	o = append(o, 0xac, 0x52, 0x65, 0x61, 0x6c, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x75, 0x74, 0x68)
	if z.RealtionAuth == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, int64(*z.RealtionAuth))
	}
	// string "CityCodeI64"
	o = append(o, 0xab, 0x43, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x36, 0x34)
	if z.CityCodeI64 == nil {
		o = msgp.AppendNil(o)
	} else {
		o = msgp.AppendInt64(o, *z.CityCodeI64)
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *UserStruct) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "ID":
			z.ID, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ID")
				return
			}
		case "ShortID":
			z.ShortID, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ShortID")
				return
			}
		case "Nickname":
			z.Nickname, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Nickname")
				return
			}
		case "Gender":
			{
				var zb0002 int64
				zb0002, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Gender")
					return
				}
				z.Gender = GenderEnum(zb0002)
			}
		case "Signature":
			z.Signature, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Signature")
				return
			}
		case "Level":
			z.Level, bts, err = msgp.ReadInt16Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Level")
				return
			}
		case "Birthday":
			z.Birthday, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Birthday")
				return
			}
		case "AvatarUri":
			z.AvatarUri, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "AvatarUri")
				return
			}
		case "Telephone":
			z.Telephone, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Telephone")
				return
			}
		case "AvatarLarge":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AvatarLarge = nil
			} else {
				if z.AvatarLarge == nil {
					z.AvatarLarge = new(UrlStruct)
				}
				bts, err = z.AvatarLarge.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "AvatarLarge")
					return
				}
			}
		case "AvatarThumb":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AvatarThumb = nil
			} else {
				if z.AvatarThumb == nil {
					z.AvatarThumb = new(UrlStruct)
				}
				bts, err = z.AvatarThumb.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "AvatarThumb")
					return
				}
			}
		case "AvatarMedium":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AvatarMedium = nil
			} else {
				if z.AvatarMedium == nil {
					z.AvatarMedium = new(UrlStruct)
				}
				bts, err = z.AvatarMedium.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "AvatarMedium")
					return
				}
			}
		case "IsVerified":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.IsVerified = nil
			} else {
				if z.IsVerified == nil {
					z.IsVerified = new(bool)
				}
				*z.IsVerified, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "IsVerified")
					return
				}
			}
		case "Experience":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Experience = nil
			} else {
				if z.Experience == nil {
					z.Experience = new(int32)
				}
				*z.Experience, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Experience")
					return
				}
			}
		case "City":
			z.City, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "City")
				return
			}
		case "Status":
			z.Status, bts, err = msgp.ReadInt16Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Status")
				return
			}
		case "CreateTime":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.CreateTime = nil
			} else {
				if z.CreateTime == nil {
					z.CreateTime = new(int64)
				}
				*z.CreateTime, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "CreateTime")
					return
				}
			}
		case "ModifyTime":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ModifyTime = nil
			} else {
				if z.ModifyTime == nil {
					z.ModifyTime = new(int64)
				}
				*z.ModifyTime, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "ModifyTime")
					return
				}
			}
		case "AvatarMeta":
			z.AvatarMeta, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "AvatarMeta")
				return
			}
		case "Secret":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Secret = nil
			} else {
				if z.Secret == nil {
					z.Secret = new(int16)
				}
				*z.Secret, bts, err = msgp.ReadInt16Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Secret")
					return
				}
			}
		case "ShareQrcodeUri":
			z.ShareQrcodeUri, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "ShareQrcodeUri")
				return
			}
		case "IncomeSharePercent":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.IncomeSharePercent = nil
			} else {
				if z.IncomeSharePercent == nil {
					z.IncomeSharePercent = new(int16)
				}
				*z.IncomeSharePercent, bts, err = msgp.ReadInt16Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "IncomeSharePercent")
					return
				}
			}
		case "Internal":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Internal = nil
			} else {
				if z.Internal == nil {
					z.Internal = new(int16)
				}
				*z.Internal, bts, err = msgp.ReadInt16Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Internal")
					return
				}
			}
		case "RiskFlag":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.RiskFlag = nil
			} else {
				if z.RiskFlag == nil {
					z.RiskFlag = new(int16)
				}
				*z.RiskFlag, bts, err = msgp.ReadInt16Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "RiskFlag")
					return
				}
			}
		case "FilterRisk":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.FilterRisk = nil
			} else {
				if z.FilterRisk == nil {
					z.FilterRisk = new(bool)
				}
				*z.FilterRisk, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "FilterRisk")
					return
				}
			}
		case "CityCode":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.CityCode = nil
			} else {
				if z.CityCode == nil {
					z.CityCode = new(int32)
				}
				*z.CityCode, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "CityCode")
					return
				}
			}
		case "SpainUser":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.SpainUser = nil
			} else {
				if z.SpainUser == nil {
					z.SpainUser = new(int64)
				}
				*z.SpainUser, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "SpainUser")
					return
				}
			}
		case "Campaign":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Campaign = nil
			} else {
				if z.Campaign == nil {
					z.Campaign = new(string)
				}
				*z.Campaign, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Campaign")
					return
				}
			}
		case "PayDiamondBak":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.PayDiamondBak = nil
			} else {
				if z.PayDiamondBak == nil {
					z.PayDiamondBak = new(int64)
				}
				*z.PayDiamondBak, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "PayDiamondBak")
					return
				}
			}
		case "IsOfficial":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.IsOfficial = nil
			} else {
				if z.IsOfficial == nil {
					z.IsOfficial = new(bool)
				}
				*z.IsOfficial, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "IsOfficial")
					return
				}
			}
		case "LiveCommentOnlyMeSee":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.LiveCommentOnlyMeSee = nil
			} else {
				if z.LiveCommentOnlyMeSee == nil {
					z.LiveCommentOnlyMeSee = new(string)
				}
				*z.LiveCommentOnlyMeSee, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LiveCommentOnlyMeSee")
					return
				}
			}
		case "AllowStatus":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AllowStatus = nil
			} else {
				if z.AllowStatus == nil {
					z.AllowStatus = new(int32)
				}
				*z.AllowStatus, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "AllowStatus")
					return
				}
			}
		case "AllowBeLocated":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AllowBeLocated = nil
			} else {
				if z.AllowBeLocated == nil {
					z.AllowBeLocated = new(bool)
				}
				*z.AllowBeLocated, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "AllowBeLocated")
					return
				}
			}
		case "HideLocation":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.HideLocation = nil
			} else {
				if z.HideLocation == nil {
					z.HideLocation = new(bool)
				}
				*z.HideLocation, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "HideLocation")
					return
				}
			}
		case "CoverLibraryInfo":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.CoverLibraryInfo = nil
			} else {
				if z.CoverLibraryInfo == nil {
					z.CoverLibraryInfo = new(CoverLibrary)
				}
				bts, err = z.CoverLibraryInfo.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "CoverLibraryInfo")
					return
				}
			}
		case "UserMode":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.UserMode = nil
			} else {
				if z.UserMode == nil {
					z.UserMode = new(int32)
				}
				*z.UserMode, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserMode")
					return
				}
			}
		case "UserPeroid":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.UserPeroid = nil
			} else {
				if z.UserPeroid == nil {
					z.UserPeroid = new(int32)
				}
				*z.UserPeroid, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserPeroid")
					return
				}
			}
		case "UserRate":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.UserRate = nil
			} else {
				if z.UserRate == nil {
					z.UserRate = new(int32)
				}
				*z.UserRate, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserRate")
					return
				}
			}
		case "LabelIDs":
			var zb0003 uint32
			zb0003, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "LabelIDs")
				return
			}
			if cap(z.LabelIDs) >= int(zb0003) {
				z.LabelIDs = (z.LabelIDs)[:zb0003]
			} else {
				z.LabelIDs = make([]int64, zb0003)
			}
			for za0001 := range z.LabelIDs {
				z.LabelIDs[za0001], bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LabelIDs", za0001)
					return
				}
			}
		case "DisplayID":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.DisplayID = nil
			} else {
				if z.DisplayID == nil {
					z.DisplayID = new(string)
				}
				*z.DisplayID, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "DisplayID")
					return
				}
			}
		case "LinkMicStats":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.LinkMicStats = nil
			} else {
				if z.LinkMicStats == nil {
					z.LinkMicStats = new(int64)
				}
				*z.LinkMicStats, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LinkMicStats")
					return
				}
			}
		case "LiveAgreement":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.LiveAgreement = nil
			} else {
				if z.LiveAgreement == nil {
					z.LiveAgreement = new(int64)
				}
				*z.LiveAgreement, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LiveAgreement")
					return
				}
			}
		case "LiveAgreementTime":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.LiveAgreementTime = nil
			} else {
				if z.LiveAgreementTime == nil {
					z.LiveAgreementTime = new(int64)
				}
				*z.LiveAgreementTime, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LiveAgreementTime")
					return
				}
			}
		case "IsPhoneBinded":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.IsPhoneBinded = nil
			} else {
				if z.IsPhoneBinded == nil {
					z.IsPhoneBinded = new(bool)
				}
				*z.IsPhoneBinded, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "IsPhoneBinded")
					return
				}
			}
		case "LiveVerify":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.LiveVerify = nil
			} else {
				if z.LiveVerify == nil {
					z.LiveVerify = new(int32)
				}
				*z.LiveVerify, bts, err = msgp.ReadInt32Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "LiveVerify")
					return
				}
			}
		case "WithCommercePermission":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.WithCommercePermission = nil
			} else {
				if z.WithCommercePermission == nil {
					z.WithCommercePermission = new(bool)
				}
				*z.WithCommercePermission, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "WithCommercePermission")
					return
				}
			}
		case "WithFusionShopEntry":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.WithFusionShopEntry = nil
			} else {
				if z.WithFusionShopEntry == nil {
					z.WithFusionShopEntry = new(bool)
				}
				*z.WithFusionShopEntry, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "WithFusionShopEntry")
					return
				}
			}
		case "AnchorLevel":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AnchorLevel = nil
			} else {
				if z.AnchorLevel == nil {
					z.AnchorLevel = new(AnchorLevelStats)
				}
				bts, err = z.AnchorLevel.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "AnchorLevel")
					return
				}
			}
		case "VerifiedContent":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.VerifiedContent = nil
			} else {
				if z.VerifiedContent == nil {
					z.VerifiedContent = new(string)
				}
				*z.VerifiedContent, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "VerifiedContent")
					return
				}
			}
		case "HasPrivilege":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.HasPrivilege = nil
			} else {
				if z.HasPrivilege == nil {
					z.HasPrivilege = new(bool)
				}
				*z.HasPrivilege, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "HasPrivilege")
					return
				}
			}
		case "SpecialID":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.SpecialID = nil
			} else {
				if z.SpecialID == nil {
					z.SpecialID = new(string)
				}
				*z.SpecialID, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "SpecialID")
					return
				}
			}
		case "PayScores":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.PayScores = nil
			} else {
				if z.PayScores == nil {
					z.PayScores = new(int64)
				}
				*z.PayScores, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "PayScores")
					return
				}
			}
		case "BgImgUrl":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.BgImgUrl = nil
			} else {
				if z.BgImgUrl == nil {
					z.BgImgUrl = new(string)
				}
				*z.BgImgUrl, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "BgImgUrl")
					return
				}
			}
		case "RoomAutoGiftThanks":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.RoomAutoGiftThanks = nil
			} else {
				if z.RoomAutoGiftThanks == nil {
					z.RoomAutoGiftThanks = new(bool)
				}
				*z.RoomAutoGiftThanks, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "RoomAutoGiftThanks")
					return
				}
			}
		case "FakeUser":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.FakeUser = nil
			} else {
				if z.FakeUser == nil {
					z.FakeUser = new(bool)
				}
				*z.FakeUser, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "FakeUser")
					return
				}
			}
		case "SecUid":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.SecUid = nil
			} else {
				if z.SecUid == nil {
					z.SecUid = new(string)
				}
				*z.SecUid, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "SecUid")
					return
				}
			}
		case "ForbidWithdrawal":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ForbidWithdrawal = nil
			} else {
				if z.ForbidWithdrawal == nil {
					z.ForbidWithdrawal = new(int16)
				}
				*z.ForbidWithdrawal, bts, err = msgp.ReadInt16Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "ForbidWithdrawal")
					return
				}
			}
		case "Language":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Language = nil
			} else {
				if z.Language == nil {
					z.Language = new(string)
				}
				*z.Language, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Language")
					return
				}
			}
		case "AppID":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.AppID = nil
			} else {
				if z.AppID == nil {
					z.AppID = new(int64)
				}
				*z.AppID, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "AppID")
					return
				}
			}
		case "Region":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Region = nil
			} else {
				if z.Region == nil {
					z.Region = new(string)
				}
				*z.Region, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Region")
					return
				}
			}
		case "WithCarManagementPermission":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.WithCarManagementPermission = nil
			} else {
				if z.WithCarManagementPermission == nil {
					z.WithCarManagementPermission = new(bool)
				}
				*z.WithCarManagementPermission, bts, err = msgp.ReadBoolBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "WithCarManagementPermission")
					return
				}
			}
		case "UserRateMap":
			var zb0004 uint32
			zb0004, bts, err = msgp.ReadMapHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "UserRateMap")
				return
			}
			if z.UserRateMap == nil {
				z.UserRateMap = make(map[string]int64, zb0004)
			} else if len(z.UserRateMap) > 0 {
				for key := range z.UserRateMap {
					delete(z.UserRateMap, key)
				}
			}
			for zb0004 > 0 {
				var za0002 string
				var za0003 int64
				zb0004--
				za0002, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserRateMap")
					return
				}
				za0003, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "UserRateMap", za0002)
					return
				}
				z.UserRateMap[za0002] = za0003
			}
		case "CustomVerify":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.CustomVerify = nil
			} else {
				if z.CustomVerify == nil {
					z.CustomVerify = new(string)
				}
				*z.CustomVerify, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "CustomVerify")
					return
				}
			}
		case "EnterpriseVerifyReason":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.EnterpriseVerifyReason = nil
			} else {
				if z.EnterpriseVerifyReason == nil {
					z.EnterpriseVerifyReason = new(string)
				}
				*z.EnterpriseVerifyReason, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "EnterpriseVerifyReason")
					return
				}
			}
		case "ShowGenderStrategy":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.ShowGenderStrategy = nil
			} else {
				if z.ShowGenderStrategy == nil {
					z.ShowGenderStrategy = new(string)
				}
				*z.ShowGenderStrategy, bts, err = msgp.ReadStringBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "ShowGenderStrategy")
					return
				}
			}
		case "RegisterFrom":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.RegisterFrom = nil
			} else {
				if z.RegisterFrom == nil {
					z.RegisterFrom = new(VCDRegisterFrom)
				}
				{
					var zb0005 int64
					zb0005, bts, err = msgp.ReadInt64Bytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "RegisterFrom")
						return
					}
					*z.RegisterFrom = VCDRegisterFrom(zb0005)
				}
			}
		case "BaseInfoAuth":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.BaseInfoAuth = nil
			} else {
				if z.BaseInfoAuth == nil {
					z.BaseInfoAuth = new(VCDAuth)
				}
				{
					var zb0006 int64
					zb0006, bts, err = msgp.ReadInt64Bytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "BaseInfoAuth")
						return
					}
					*z.BaseInfoAuth = VCDAuth(zb0006)
				}
			}
		case "RealtionAuth":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.RealtionAuth = nil
			} else {
				if z.RealtionAuth == nil {
					z.RealtionAuth = new(VCDAuth)
				}
				{
					var zb0007 int64
					zb0007, bts, err = msgp.ReadInt64Bytes(bts)
					if err != nil {
						err = msgp.WrapError(err, "RealtionAuth")
						return
					}
					*z.RealtionAuth = VCDAuth(zb0007)
				}
			}
		case "CityCodeI64":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.CityCodeI64 = nil
			} else {
				if z.CityCodeI64 == nil {
					z.CityCodeI64 = new(int64)
				}
				*z.CityCodeI64, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "CityCodeI64")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *UserStruct) Msgsize() (s int) {
	s = 3 + 3 + msgp.Int64Size + 8 + msgp.Int64Size + 9 + msgp.StringPrefixSize + len(z.Nickname) + 7 + msgp.Int64Size + 10 + msgp.StringPrefixSize + len(z.Signature) + 6 + msgp.Int16Size + 9 + msgp.Int64Size + 10 + msgp.StringPrefixSize + len(z.AvatarUri) + 10 + msgp.StringPrefixSize + len(z.Telephone) + 12
	if z.AvatarLarge == nil {
		s += msgp.NilSize
	} else {
		s += z.AvatarLarge.Msgsize()
	}
	s += 12
	if z.AvatarThumb == nil {
		s += msgp.NilSize
	} else {
		s += z.AvatarThumb.Msgsize()
	}
	s += 13
	if z.AvatarMedium == nil {
		s += msgp.NilSize
	} else {
		s += z.AvatarMedium.Msgsize()
	}
	s += 11
	if z.IsVerified == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 11
	if z.Experience == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 5 + msgp.StringPrefixSize + len(z.City) + 7 + msgp.Int16Size + 11
	if z.CreateTime == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 11
	if z.ModifyTime == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 11 + msgp.StringPrefixSize + len(z.AvatarMeta) + 7
	if z.Secret == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int16Size
	}
	s += 15 + msgp.StringPrefixSize + len(z.ShareQrcodeUri) + 19
	if z.IncomeSharePercent == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int16Size
	}
	s += 9
	if z.Internal == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int16Size
	}
	s += 9
	if z.RiskFlag == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int16Size
	}
	s += 11
	if z.FilterRisk == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 9
	if z.CityCode == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 10
	if z.SpainUser == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 9
	if z.Campaign == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Campaign)
	}
	s += 14
	if z.PayDiamondBak == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 11
	if z.IsOfficial == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 21
	if z.LiveCommentOnlyMeSee == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.LiveCommentOnlyMeSee)
	}
	s += 12
	if z.AllowStatus == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 15
	if z.AllowBeLocated == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 13
	if z.HideLocation == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 17
	if z.CoverLibraryInfo == nil {
		s += msgp.NilSize
	} else {
		s += z.CoverLibraryInfo.Msgsize()
	}
	s += 9
	if z.UserMode == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 11
	if z.UserPeroid == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 9
	if z.UserRate == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 9 + msgp.ArrayHeaderSize + (len(z.LabelIDs) * (msgp.Int64Size)) + 10
	if z.DisplayID == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.DisplayID)
	}
	s += 13
	if z.LinkMicStats == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 14
	if z.LiveAgreement == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 18
	if z.LiveAgreementTime == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 14
	if z.IsPhoneBinded == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 11
	if z.LiveVerify == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int32Size
	}
	s += 23
	if z.WithCommercePermission == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 20
	if z.WithFusionShopEntry == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 12
	if z.AnchorLevel == nil {
		s += msgp.NilSize
	} else {
		s += z.AnchorLevel.Msgsize()
	}
	s += 16
	if z.VerifiedContent == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.VerifiedContent)
	}
	s += 13
	if z.HasPrivilege == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 10
	if z.SpecialID == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.SpecialID)
	}
	s += 10
	if z.PayScores == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 9
	if z.BgImgUrl == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.BgImgUrl)
	}
	s += 19
	if z.RoomAutoGiftThanks == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 9
	if z.FakeUser == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 7
	if z.SecUid == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.SecUid)
	}
	s += 17
	if z.ForbidWithdrawal == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int16Size
	}
	s += 9
	if z.Language == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Language)
	}
	s += 6
	if z.AppID == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 7
	if z.Region == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.Region)
	}
	s += 28
	if z.WithCarManagementPermission == nil {
		s += msgp.NilSize
	} else {
		s += msgp.BoolSize
	}
	s += 12 + msgp.MapHeaderSize
	if z.UserRateMap != nil {
		for za0002, za0003 := range z.UserRateMap {
			_ = za0003
			s += msgp.StringPrefixSize + len(za0002) + msgp.Int64Size
		}
	}
	s += 13
	if z.CustomVerify == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.CustomVerify)
	}
	s += 23
	if z.EnterpriseVerifyReason == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.EnterpriseVerifyReason)
	}
	s += 19
	if z.ShowGenderStrategy == nil {
		s += msgp.NilSize
	} else {
		s += msgp.StringPrefixSize + len(*z.ShowGenderStrategy)
	}
	s += 13
	if z.RegisterFrom == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 13
	if z.BaseInfoAuth == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 13
	if z.RealtionAuth == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	s += 12
	if z.CityCodeI64 == nil {
		s += msgp.NilSize
	} else {
		s += msgp.Int64Size
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *VCDAuth) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 int64
		zb0001, err = dc.ReadInt64()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = VCDAuth(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z VCDAuth) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteInt64(int64(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z VCDAuth) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendInt64(o, int64(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *VCDAuth) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 int64
		zb0001, bts, err = msgp.ReadInt64Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = VCDAuth(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z VCDAuth) Msgsize() (s int) {
	s = msgp.Int64Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *VCDRegisterFrom) DecodeMsg(dc *msgp.Reader) (err error) {
	{
		var zb0001 int64
		zb0001, err = dc.ReadInt64()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = VCDRegisterFrom(zb0001)
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z VCDRegisterFrom) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteInt64(int64(z))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z VCDRegisterFrom) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendInt64(o, int64(z))
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *VCDRegisterFrom) UnmarshalMsg(bts []byte) (o []byte, err error) {
	{
		var zb0001 int64
		zb0001, bts, err = msgp.ReadInt64Bytes(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		(*z) = VCDRegisterFrom(zb0001)
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z VCDRegisterFrom) Msgsize() (s int) {
	s = msgp.Int64Size
	return
}
