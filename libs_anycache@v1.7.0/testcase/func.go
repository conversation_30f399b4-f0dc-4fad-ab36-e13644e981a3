package testcase

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sync/atomic"
	"time"
)

func init() {
	rand.Seed(time.Now().UnixNano())
}

// Demo
type Diamond struct {
	Id          int64
	LiveId      int64
	DiamondType int64
	Random      int64
}

type DiamondSlice []Diamond

func (p DiamondSlice) Len() int           { return len(p) }
func (p DiamondSlice) Less(i, j int) bool { return p[i].Id < p[j].Id }
func (p DiamondSlice) Swap(i, j int)      { p[i], p[j] = p[j], p[i] }

// 一般的情况
func GetDiamondByDiamondType(ctx context.Context, liveId int64, diamondType int64) ([]Diamond, error) {
	if ctx.Value("bad_source") != nil {
		return nil, errors.New("bad_source")
	}

	if ctx.Value("latency") != nil {
		time.Sleep(ctx.Value("latency").(time.Duration))
	}

	return []Diamond{
		{
			1, liveId, diamondType, rand.Int63(),
		},
		{
			2, liveId, diamondType, rand.Int63(),
		},
		{
			3, liveId, diamondType, rand.Int63(),
		},
	}, nil
}

type GetDiamondByDiamondTypeContext struct {
	ctx         context.Context
	liveId      int64
	diamondType int64
	CallCount   int64
}

func NewGetDiamondByDiamondTypeContext(ctx context.Context, liveId int64, diamondType int64) *GetDiamondByDiamondTypeContext {
	return &GetDiamondByDiamondTypeContext{
		ctx:         ctx,
		liveId:      liveId,
		diamondType: diamondType,
	}
}

func (c *GetDiamondByDiamondTypeContext) Fetch(ctx context.Context) (interface{}, error) {
	atomic.AddInt64(&c.CallCount, 1)
	return GetDiamondByDiamondType(c.ctx, c.liveId, c.diamondType)
}
func (c *GetDiamondByDiamondTypeContext) GenKey(ctx context.Context) string {
	return fmt.Sprintf("%d-%d", c.liveId, c.diamondType)
}
