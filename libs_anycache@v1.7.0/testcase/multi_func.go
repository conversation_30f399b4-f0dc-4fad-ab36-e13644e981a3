package testcase

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"sync/atomic"
	"time"
)

// 批量的情况
func GetDiamondList(ctx context.Context, liveId int64, diamondIds []int64) ([]Diamond, error) {
	if ctx.Value("bad_source") != nil {
		return nil, errors.New("bad_source")
	}

	if ctx.Value("latency") != nil {
		time.Sleep(ctx.Value("latency").(time.Duration))
	}

	var diamonds []Diamond
	for _, id := range diamondIds {
		diamonds = append(diamonds, Diamond{
			id, liveId, 1, rand.Int63(),
		})
	}
	return diamonds, nil
}

type GetDiamondListContext struct {
	ctx        context.Context
	liveId     int64
	diamondIds []int64
	CallCount  int64
}

func (c *GetDiamondListContext) genKeyFromParams(ctx context.Context, item interface{}) string {
	id := item.(int64)
	return fmt.Sprintf("%d:%d", c.liveId, id)
}

func (c *GetDiamondListContext) genKeyFromResults(ctx context.Context, outItem interface{}) string {
	diamond := outItem.(Diamond)
	return fmt.Sprintf("%d:%d", diamond.LiveId, diamond.Id)
}

func NewGetDiamondListContext(ctx context.Context, liveId int64, diamondIds []int64) *GetDiamondListContext {
	return &GetDiamondListContext{
		ctx:        ctx,
		liveId:     liveId,
		diamondIds: diamondIds,
		CallCount:  0,
	}
}

func (c *GetDiamondListContext) MultiFetch(ctx context.Context, missedItems interface{}) (interface{}, error) {
	diamondIds := missedItems.([]int64)
	atomic.AddInt64(&c.CallCount, 1)
	diamondList, err := GetDiamondList(c.ctx, c.liveId, diamondIds)
	if err != nil {
		return nil, err
	}

	return diamondList, nil
}

func (c *GetDiamondListContext) GetInItems(ctx context.Context) interface{} {
	return c.diamondIds
}
