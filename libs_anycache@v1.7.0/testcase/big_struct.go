package testcase

import (
	"encoding/json"
)

//go:generate msgp
type DataStruct MultiGetUserResponse

type MultiGetUserResponse struct {
	UserInfo map[string]*UserStruct `thrift:"UserInfo,1,required" json:"UserInfo"`
	// unused fields # 2 to 254
	BaseResp *BaseResp `thrift:"BaseResp,255,required" json:"BaseResp"`
}

type BaseResp struct {
	StatusMessage string            `thrift:"StatusMessage,1" json:"StatusMessage"`
	StatusCode    int32             `thrift:"StatusCode,2" json:"StatusCode"`
	Extra         map[string]string `thrift:"Extra,3" json:"Extra"`
}

type UserStruct struct {
	ID                          int64             `thrift:"ID,1,required" json:"ID"`
	ShortID                     int64             `thrift:"ShortID,2,required" json:"ShortID"`
	Nickname                    string            `thrift:"Nickname,3,required" json:"Nickname"`
	Gender                      GenderEnum        `thrift:"Gender,4,required" json:"Gender"`
	Signature                   string            `thrift:"Signature,5,required" json:"Signature"`
	Level                       int16             `thrift:"Level,6,required" json:"Level"`
	Birthday                    int64             `thrift:"Birthday,7,required" json:"Birthday"`
	AvatarUri                   string            `thrift:"AvatarUri,8,required" json:"AvatarUri"`
	Telephone                   string            `thrift:"Telephone,9,required" json:"Telephone"`
	AvatarLarge                 *UrlStruct        `thrift:"AvatarLarge,10,required" json:"AvatarLarge"`
	AvatarThumb                 *UrlStruct        `thrift:"AvatarThumb,11,required" json:"AvatarThumb"`
	AvatarMedium                *UrlStruct        `thrift:"AvatarMedium,12,required" json:"AvatarMedium"`
	IsVerified                  *bool             `thrift:"IsVerified,13" json:"IsVerified"`
	Experience                  *int32            `thrift:"Experience,14" json:"Experience"`
	City                        string            `thrift:"City,15" json:"City"`
	Status                      int16             `thrift:"Status,16" json:"Status"`
	CreateTime                  *int64            `thrift:"CreateTime,17" json:"CreateTime"`
	ModifyTime                  *int64            `thrift:"ModifyTime,18" json:"ModifyTime"`
	AvatarMeta                  string            `thrift:"AvatarMeta,19" json:"AvatarMeta"`
	Secret                      *int16            `thrift:"Secret,20" json:"Secret"`
	ShareQrcodeUri              string            `thrift:"ShareQrcodeUri,21" json:"ShareQrcodeUri"`
	IncomeSharePercent          *int16            `thrift:"IncomeSharePercent,22" json:"IncomeSharePercent"`
	Internal                    *int16            `thrift:"Internal,23" json:"Internal"`
	RiskFlag                    *int16            `thrift:"RiskFlag,24" json:"RiskFlag"`
	FilterRisk                  *bool             `thrift:"FilterRisk,25" json:"FilterRisk"`
	CityCode                    *int32            `thrift:"CityCode,26" json:"CityCode"`
	SpainUser                   *int64            `thrift:"SpainUser,27" json:"SpainUser"`
	Campaign                    *string           `thrift:"Campaign,28" json:"Campaign"`
	PayDiamondBak               *int64            `thrift:"PayDiamondBak,29" json:"PayDiamondBak"`
	IsOfficial                  *bool             `thrift:"IsOfficial,30" json:"IsOfficial"`
	LiveCommentOnlyMeSee        *string           `thrift:"LiveCommentOnlyMeSee,31" json:"LiveCommentOnlyMeSee"`
	AllowStatus                 *int32            `thrift:"AllowStatus,32" json:"AllowStatus"`
	AllowBeLocated              *bool             `thrift:"AllowBeLocated,33" json:"AllowBeLocated"`
	HideLocation                *bool             `thrift:"HideLocation,34" json:"HideLocation"`
	CoverLibraryInfo            *CoverLibrary     `thrift:"CoverLibraryInfo,35" json:"CoverLibraryInfo"`
	UserMode                    *int32            `thrift:"UserMode,36" json:"UserMode"`
	UserPeroid                  *int32            `thrift:"UserPeroid,37" json:"UserPeroid"`
	UserRate                    *int32            `thrift:"UserRate,38" json:"UserRate"`
	LabelIDs                    []int64           `thrift:"LabelIDs,39" json:"LabelIDs"`
	DisplayID                   *string           `thrift:"DisplayID,40" json:"DisplayID"`
	LinkMicStats                *int64            `thrift:"LinkMicStats,41" json:"LinkMicStats"`
	LiveAgreement               *int64            `thrift:"LiveAgreement,42" json:"LiveAgreement"`
	LiveAgreementTime           *int64            `thrift:"LiveAgreementTime,43" json:"LiveAgreementTime"`
	IsPhoneBinded               *bool             `thrift:"IsPhoneBinded,44" json:"IsPhoneBinded"`
	LiveVerify                  *int32            `thrift:"LiveVerify,45" json:"LiveVerify"`
	WithCommercePermission      *bool             `thrift:"WithCommercePermission,46" json:"WithCommercePermission"`
	WithFusionShopEntry         *bool             `thrift:"WithFusionShopEntry,47" json:"WithFusionShopEntry"`
	AnchorLevel                 *AnchorLevelStats `thrift:"AnchorLevel,48" json:"AnchorLevel"`
	VerifiedContent             *string           `thrift:"VerifiedContent,49" json:"VerifiedContent"`
	HasPrivilege                *bool             `thrift:"HasPrivilege,50" json:"HasPrivilege"`
	SpecialID                   *string           `thrift:"SpecialID,51" json:"SpecialID"`
	PayScores                   *int64            `thrift:"PayScores,52" json:"PayScores"`
	BgImgUrl                    *string           `thrift:"BgImgUrl,53" json:"BgImgUrl"`
	RoomAutoGiftThanks          *bool             `thrift:"RoomAutoGiftThanks,54" json:"RoomAutoGiftThanks"`
	FakeUser                    *bool             `thrift:"FakeUser,55" json:"FakeUser"`
	SecUid                      *string           `thrift:"SecUid,56" json:"SecUid"`
	ForbidWithdrawal            *int16            `thrift:"ForbidWithdrawal,57" json:"ForbidWithdrawal"`
	Language                    *string           `thrift:"Language,58" json:"Language"`
	AppID                       *int64            `thrift:"AppID,59" json:"AppID"`
	Region                      *string           `thrift:"region,60" json:"region"`
	WithCarManagementPermission *bool             `thrift:"WithCarManagementPermission,61" json:"WithCarManagementPermission"`
	UserRateMap                 map[string]int64  `thrift:"UserRateMap,62" json:"UserRateMap"`
	CustomVerify                *string           `thrift:"CustomVerify,63" json:"CustomVerify"`
	EnterpriseVerifyReason      *string           `thrift:"EnterpriseVerifyReason,64" json:"EnterpriseVerifyReason"`
	ShowGenderStrategy          *string           `thrift:"ShowGenderStrategy,65" json:"ShowGenderStrategy"`
	RegisterFrom                *VCDRegisterFrom  `thrift:"RegisterFrom,66" json:"RegisterFrom"`
	BaseInfoAuth                *VCDAuth          `thrift:"BaseInfoAuth,67" json:"BaseInfoAuth"`
	RealtionAuth                *VCDAuth          `thrift:"RealtionAuth,68" json:"RealtionAuth"`
	CityCodeI64                 *int64            `thrift:"CityCodeI64,69" json:"CityCodeI64"`
}

type GenderEnum int64

type UrlStruct struct {
	Uri        string         `thrift:"Uri,1,required" json:"Uri"`
	UrlList    []string       `thrift:"UrlList,2,required" json:"UrlList"`
	Width      *int64         `thrift:"Width,3" json:"Width"`
	Height     *int64         `thrift:"Height,4" json:"Height"`
	ImageType  *ImageTypeEnum `thrift:"ImageType,5" json:"ImageType"`
	OpenSchema *string        `thrift:"OpenSchema,6" json:"OpenSchema"`
	Content    *UrlContent    `thrift:"Content,7" json:"Content"`
	IsAnimated *bool          `thrift:"IsAnimated,8" json:"IsAnimated"`
}
type ImageTypeEnum int64

type UrlContent struct {
	Name      *string `thrift:"Name,1" json:"Name"`
	FontColor *string `thrift:"FontColor,2" json:"FontColor"`
	Level     *int64  `thrift:"Level,3" json:"Level"`
}

type CoverLibrary struct {
	Checking *string `thrift:"Checking,1" json:"Checking"`
	Checked  *string `thrift:"Checked,2" json:"Checked"`
	Auditor  *string `thrift:"Auditor,3" json:"Auditor"`
}

type AnchorLevelStats struct {
	Level                      *int64     `thrift:"Level,1" json:"Level"`
	Experience                 *int64     `thrift:"Experience,2" json:"Experience"`
	LowestExperienceThisLevel  *int64     `thrift:"LowestExperienceThisLevel,3" json:"LowestExperienceThisLevel"`
	HighestExperienceThisLevel *int64     `thrift:"HighestExperienceThisLevel,4" json:"HighestExperienceThisLevel"`
	TaskStartExperience        *int64     `thrift:"TaskStartExperience,5" json:"TaskStartExperience"`
	TaskStartTime              *int64     `thrift:"TaskStartTime,6" json:"TaskStartTime"`
	TaskDecreaseExperience     *int64     `thrift:"TaskDecreaseExperience,7" json:"TaskDecreaseExperience"`
	TaskTargetExperience       *int64     `thrift:"TaskTargetExperience,8" json:"TaskTargetExperience"`
	TaskEndTime                *int64     `thrift:"TaskEndTime,9" json:"TaskEndTime"`
	ProfileDialogBg            *UrlStruct `thrift:"ProfileDialogBg,10" json:"ProfileDialogBg"`
	ProfileDialogBgBack        *UrlStruct `thrift:"ProfileDialogBgBack,11" json:"ProfileDialogBgBack"`
	StageLevel                 *UrlStruct `thrift:"StageLevel,12" json:"StageLevel"`
	SmallIcon                  *UrlStruct `thrift:"SmallIcon,13" json:"SmallIcon"`
}

type VCDRegisterFrom int64

type VCDAuth int64

var (
	dataInBytes = []byte(`
{"BaseResp": {"Extra": null, "StatusCode": 0, "StatusMessage": "success"}, "UserInfo": {"3966641327840952": {"CityCodeI64": null, "RealtionAuth": 0, "BaseInfoAuth": 0, "RegisterFrom": 2, "ShowGenderStrategy": "", "EnterpriseVerifyReason": "", "CustomVerify": "", "UserRateMap": {}, "WithCarManagementPermission": null, "region": "", "AppID": 1128, "Language": null, "ForbidWithdrawal": null, "SecUid": "MS4wLjABAAAAzVmHyO39HQF3p-h0P1Er7t6RivJfCqMqL49QKqH4C2j_8FK6I8Fr4dj1u4fET8LY", "FakeUser": null, "RoomAutoGiftThanks": null, "BgImgUrl": null, "PayScores": null, "SpecialID": null, "HasPrivilege": null, "VerifiedContent": null, "AnchorLevel": null, "WithFusionShopEntry": false, "WithCommercePermission": false, "LiveVerify": 0, "IsPhoneBinded": false, "LiveAgreementTime": 0, "LiveAgreement": 0, "LinkMicStats": 1, "DisplayID": "dysznjcoia5t", "LabelIDs": null, "UserRate": 0, "UserPeroid": 0, "UserMode": 0, "CoverLibraryInfo": {"Auditor": "", "Checked": "", "Checking": ""}, "HideLocation": null, "AllowBeLocated": null, "AllowStatus": null, "LiveCommentOnlyMeSee": "", "IsOfficial": false, "PayDiamondBak": 0, "Campaign": "", "SpainUser": 0, "CityCode": null, "FilterRisk": null, "RiskFlag": 0, "Internal": 0, "IncomeSharePercent": null, "ShareQrcodeUri": "", "Secret": 0, "AvatarMeta": "", "ModifyTime": 1581540981, "CreateTime": 1581540981, "Status": 1, "City": "", "Experience": null, "IsVerified": true, "AvatarMedium": {"IsAnimated": null, "Content": null, "OpenSchema": null, "ImageType": null, "Height": null, "Width": null, "UrlList": ["https://sf16-va.tiktokcdn.com/img/mosaic-legacy/3792/5112637127~120x256.image"], "Uri": "https://sf16-va.tiktokcdn.com/img/mosaic-legacy/3792/5112637127~120x256.image"}, "AvatarThumb": {"IsAnimated": null, "Content": null, "OpenSchema": null, "ImageType": null, "Height": null, "Width": null, "UrlList": ["https://sf16-va.tiktokcdn.com/img/mosaic-legacy/3792/5112637127~120x256.image"], "Uri": "https://sf16-va.tiktokcdn.com/img/mosaic-legacy/3792/5112637127~120x256.image"}, "AvatarLarge": {"IsAnimated": null, "Content": null, "OpenSchema": null, "ImageType": null, "Height": null, "Width": null, "UrlList": ["https://sf16-va.tiktokcdn.com/img/mosaic-legacy/3792/5112637127~120x256.image"], "Uri": "https://sf16-va.tiktokcdn.com/img/mosaic-legacy/3792/5112637127~120x256.image"}, "Telephone": "", "AvatarUri": "", "Birthday": 0, "Level": 0, "Signature": "", "Gender": 0, "Nickname": "用户5199320411106", "ShortID": 1021599, "ID": 3966641327840952}}}
`)
	DataInStruct = new(DataStruct)
)

func init() {
	err := json.Unmarshal(dataInBytes, DataInStruct)
	if err != nil {
		panic(err)
	}
}
