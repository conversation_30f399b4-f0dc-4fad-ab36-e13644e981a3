/*
FAQ

1. BuildFetcherByLoader ，BuildBatchFetcherByLoader ， BuildBatchFetcherByBatchLoader 的区别？
	loader 是指只需要对函数结果整体缓存的函数
	batchLoader 是指批量获取信息的函数，函数结果会做细粒度的缓存，例如batchGetUserInfo，通过多个uid批量获取用户信息，然后按照uid维度缓存用户信息
	Fetcher 提供 Get接口和Set接口， 是整体回源包装后的处理对象。
	BatchFetcher 提供MGet接口和MSet接口， 是批量回源包装后的处理对象。
	BuildBatchFetcherByLoader ， BuildBatchFetcherByBatchLoader 的区别在于回源函数是是已经支持批量获取还是当个函数并行获取

Example

	更多例子查看 anycache 的单测
*/
package anycache
