package anycache

import (
	"context"
	"runtime/debug"
	"sync"

	"code.byted.org/webcast/logw"
)

// loaderFuncWrap 是函数缓存，简单缓存函数结果的情况下使用
//
// - genKeyFromParamsFn 是生成缓存key 的函数
//
// - loaderFn 是需要直接缓存函数结果的简单回源函数
func loaderFuncWrap(genKeyFromParamsFn genKeyFunc, loaderFn loaderFunc) Loader {
	return &loaderFuncContext{
		loaderFn: loaderFunc(func(ctx context.Context, missedItem interface{}, extraParam interface{}) (v interface{}, err error) {
			defer func() {
				if e := recover(); e != nil {
					err = ErrPanic
					logw.CtxErrorKVs(ctx, "[panic] catch ", "err", e, "err_stack", debug.Stack())
				}
			}()

			return loaderFn(ctx, missedItem, extraParam)
		}),
		genKeyFromParamsFn: genKeyFromParamsFn,
	}
}

func loaderFuncBatchWrap(genKeyFromParamsFn genKeyFunc, loaderFn loaderFunc) BatchLoader {
	return &loaderFuncContext{
		loaderFn: loaderFunc(func(ctx context.Context, missedItem interface{}, extraParam interface{}) (v interface{}, err error) {
			defer func() {
				if e := recover(); e != nil {
					err = ErrPanic
					logw.CtxErrorKVs(ctx, "[panic] catch ", "err", e, "err_stack", debug.Stack())
				}
			}()

			return loaderFn(ctx, missedItem, extraParam)
		}),
		genKeyFromParamsFn: genKeyFromParamsFn,
	}
}

// batchLoaderFuncWrap 是函数缓存，有批量需求，并且回源函数支持批量回源的情况下使用
//
// - GenKeyFromResultFn 是生成 item 对应的缓存 key 的函数
//
// - batchLoaderFn 是批量回源函数，区分 items 是否在缓存中，不在缓存中的 item 需要回源，命中缓存的不需要回源
//
// - items 是具体的入参，供 batchLoaderFn 和 GenKeyFromResultFn 使用
func batchLoaderFuncWrap(genKeyFromParamsFn, genKeyFromResultFn genKeyFunc, batchLoaderFn batchLoaderFunc) BatchLoader {
	return &batchLoaderFuncContext{
		batchLoaderFn: batchLoaderFunc(func(ctx context.Context, missedItems interface{}, extraParam interface{}) (v interface{}, err error) {
			defer func() {
				if e := recover(); e != nil {
					err = ErrPanic
					logw.CtxErrorKVs(ctx, "[panic] catch ", "err", e, "err_stack", debug.Stack())
				}
			}()

			return batchLoaderFn(ctx, missedItems, extraParam)
		}),
		genKeyFromParamsFn: genKeyFromParamsFn,
		GenKeyFromResultFn: genKeyFromResultFn,
	}
}

// loaderFunc
type loaderFunc func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error)
type genKeyFunc func(ctx context.Context, item interface{}, extraParam interface{}) string
type loaderFuncContext struct {
	loaderFn           loaderFunc
	genKeyFromParamsFn genKeyFunc
}

func (f *loaderFuncContext) genKeyFromResults(ctx context.Context, outItem interface{}, extraParam interface{}) string {
	panic("implement me")
}

func (f *loaderFuncContext) load(ctx context.Context, inItem interface{}, extraParam interface{}) (v interface{}, err error) {
	return f.loaderFn(ctx, inItem, extraParam)
}

func (f *loaderFuncContext) genKeyFromParams(ctx context.Context, inItem interface{}, extraParam interface{}) string {
	return f.genKeyFromParamsFn(ctx, inItem, extraParam)
}

func (f *loaderFuncContext) batchLoad(ctx context.Context, missedItems interface{}, extraParam interface{}) (v interface{}, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = ErrPanic
			logw.CtxErrorKVs(ctx, "[panic] catch ", "err", e, "err_stack", debug.Stack())
		}
	}()

	missedItemList := interface2SliceReflect(missedItems)

	resList := make([]interface{}, len(missedItemList))
	errList := make([]error, len(missedItemList))

	group := sync.WaitGroup{}
	group.Add(len(missedItemList))

	for i, item := range missedItemList {
		go func(i int, item interface{}) {
			defer group.Done()
			res, err := f.loaderFn(ctx, item, extraParam)
			errList[i] = err
			if err == nil {
				resList[i] = res
			}
		}(i, item)
	}
	group.Wait()

	// TODO 处理 errList
	for i := range missedItemList {
		if errList[i] != nil {
			return nil, errList[i]
		}
	}

	resMap := make(map[string]interface{}, len(missedItemList))
	for i := range missedItemList {
		item := missedItemList[i]
		// FIXME res==nil
		res := resList[i]
		resMap[f.genKeyFromParams(ctx, item, extraParam)] = res
	}

	return resMap, nil
}

// batchLoaderFunc
type batchLoaderFunc func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error)
type batchLoaderFuncContext struct {
	batchLoaderFn                          batchLoaderFunc
	genKeyFromParamsFn, GenKeyFromResultFn genKeyFunc
	items                                  interface{}
}

func (m *batchLoaderFuncContext) batchLoad(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
	return m.batchLoaderFn(ctx, missedItems, extraParam)
}

func (m *batchLoaderFuncContext) genKeyFromParams(ctx context.Context, item interface{}, extraParam interface{}) string {
	return m.genKeyFromParamsFn(ctx, item, extraParam)
}

func (m *batchLoaderFuncContext) genKeyFromResults(ctx context.Context, outItem interface{}, extraParam interface{}) string {
	return m.GenKeyFromResultFn(ctx, outItem, extraParam)
}

func warmUpFuncWrap(genKeyFromParamsFn, genKeyFromResultFn genKeyFunc, warnUpFn warmUpFunc) warmUpLoader {
	return &warmUpFuncContext{
		warmUpFn:           warnUpFn,
		genKeyFromParamsFn: genKeyFromParamsFn,
		genKeyFromResultFn: genKeyFromResultFn,
	}
}

// PageWarmUpLoad 支持分页，返回值有三个，第一个表示需要缓存的数据；第二个表示是否获取完成，用于分页的情况；第三个表示是否发生错误
type pageWarmUpLoad func(ctx context.Context) (interface{}, bool, error)
type warmUpFunc func(ctx context.Context) pageWarmUpLoad
type warmUpFuncContext struct {
	warmUpFn                               warmUpFunc
	genKeyFromParamsFn, genKeyFromResultFn genKeyFunc
}

func (w *warmUpFuncContext) batchLoad(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
	panic("implement me")
}

func (w *warmUpFuncContext) genKeyFromParams(ctx context.Context, item interface{}, extraParam interface{}) string {
	return w.genKeyFromParamsFn(ctx, item, extraParam)
}

func (w *warmUpFuncContext) genKeyFromResults(ctx context.Context, outItem interface{}, extraParam interface{}) string {
	return w.genKeyFromResultFn(ctx, outItem, extraParam)
}

func (w *warmUpFuncContext) warmUpLoad(ctx context.Context) pageWarmUpLoad {
	return w.warmUpFn(ctx)
}
