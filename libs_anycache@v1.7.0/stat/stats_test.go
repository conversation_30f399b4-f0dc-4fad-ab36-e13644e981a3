package stat

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestStats(t *testing.T) {
	// go test -timeout 30s -run ^TestStats$ code.byted.org/webcast/libs_anycache/stat -v --count=1
	ctx, deferFn := InitStats(context.Background(), "TestStats", true, false, "", "")
	defer deferFn()

	WithInfo(ctx, "foo", "1")
	WithInfo(ctx, "bar", "hello")
	WithInfo(ctx, "hello", time.Second.String())

	WithMetrics(ctx, TagHit, "1")

	stat, err := valueStat(ctx)
	assert.NoError(t, err)
	assert.Equal(t, &statStruct{
		info: map[string]string{
			"foo":           "1",
			"bar":           "hello",
			"hello":         time.Second.String(),
			"key":           "",
			"sample":        "0",
			"single_flight": "0",
		},
		metricsInfo: map[string]string{
			TagMethod:     "TestStats",
			TagCacheLevel: "0",

			TagHit:       "1",
			TagPartHit:   "0",
			TagSource:    "0",
			"cache_name": "",
			"codec_name": "",
		},
	}, stat)
}
