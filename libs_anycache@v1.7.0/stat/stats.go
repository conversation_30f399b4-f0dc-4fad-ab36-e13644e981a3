package stat

import (
	"context"
	"errors"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/webcast/logw"
)

const (
	statKey               = "stat"
	AnyCacheMetricsPrefix = "byted.anycache.sdk"
)

type statStruct struct {
	metricsInfo map[string]string
	info        map[string]string
	errList     []labelErr
}

type labelErr struct {
	err   error
	label string
}

func (e labelErr) Error() string {
	if e.err == nil {
		return ""
	}

	return e.label + ":" + e.err.Error()
}

func (ss *statStruct) addErr(label string, err error) {
	if err != nil {
		ss.errList = append(ss.errList, labelErr{label: label, err: err})
	}
}

const (
	TagHit     = "sp_hit"
	TagPartHit = "sp_part_hit"
	TagSource  = "sp_source"

	TagMethod     = "method"
	TagCacheLevel = "cache_level"
	TagNS         = "cache_namespace"
	TagAsynFull   = "async_full"
	TagCacheName  = "cache_name"
	TagCodecName  = "codec_name"
)

const (
	InfoKey          = "key"
	InfoSingleFlight = "single_flight"
	InfoSample       = "sample"
	InfoValueSize    = "value_size"
)

var (
	metricsCli   *metrics.MetricsClientV2
	metricsCliV2 *metrics.MetricsClientV2
)

func init() {
	rand.Seed(time.Now().Unix())
	metricsCli = metrics.NewDefaultMetricsClientV2(env.PSM(), true)
	metricsCliV2 = metrics.NewDefaultMetricsClientV2(AnyCacheMetricsPrefix, true)
}

func InitStats(ctx context.Context, methodName string, needLog, metricsV1 bool, cacheName, codecName string) (context.Context, func()) {
	stat := &statStruct{
		info: map[string]string{
			InfoKey:          "",
			InfoSingleFlight: "0",
			InfoSample:       "0",
		},
		metricsInfo: map[string]string{
			TagMethod:     methodName,
			TagCacheName:  cacheName,
			TagCodecName:  codecName,
			TagCacheLevel: "0",

			TagHit:     "0",
			TagPartHit: "0",
			TagSource:  "0",
		},
	}
	ctx = context.WithValue(ctx, statKey, stat)

	start := time.Now()
	return ctx, func() {
		elapsed := time.Since(start)

		if stat.metricsInfo[TagNS] == "" {
			stat.metricsInfo[TagNS] = "none"
		}

		if needLog {
			logw.CtxNotice(ctx, "anycache metrics: [namespace: %s , method: %s, cache_level: %s, hit: %s, part_hit: %s, source: %s] "+
				"[keys: %s, single_flight: %s, sample: %s, value_size: %s] "+
				"elapsed: %dus",
				stat.metricsInfo[TagNS],
				stat.metricsInfo[TagMethod],
				stat.metricsInfo[TagCacheLevel],
				stat.metricsInfo[TagHit],
				stat.metricsInfo[TagPartHit],
				stat.metricsInfo[TagSource],

				stat.info[InfoKey],
				stat.info[InfoSingleFlight],
				stat.info[InfoSample],
				stat.info[InfoValueSize],

				elapsed.Microseconds(),
			)
		}

		if len(stat.errList) > 0 && needLog {
			var errBuilder strings.Builder
			for i, err := range stat.errList {
				errBuilder.WriteString(err.Error())
				if i < len(stat.errList)-1 {
					errBuilder.WriteString(" || ")
				}
			}

			logw.CtxInfoKVs(ctx, "anycache errors", "err", errBuilder.String())
		}

		tags := metrics.Map2Tags(stat.metricsInfo)
		tagsV2 := []metrics.T{
			{Name: "_psm", Value: env.PSM()},
			{Name: "_cluster", Value: env.Cluster()},
			{Name: "_dc", Value: env.IDC()},
			{Name: "namespace", Value: stat.metricsInfo[TagNS]},
			{Name: "method", Value: stat.metricsInfo[TagMethod]},
			{Name: "is_hit", Value: stat.metricsInfo[TagHit]},
			{Name: "is_partial_hit", Value: stat.metricsInfo[TagPartHit]},
			{Name: "version", Value: AnyCacheSDKVersion},
			{Name: "cache_name", Value: stat.metricsInfo[TagCacheName]},
			{Name: "codec_name", Value: stat.metricsInfo[TagCodecName]},
			{Name: "cache_level", Value: stat.metricsInfo[TagCacheLevel]},
		}

		if stat.info[InfoValueSize] != "" {
			valueSize, _ := strconv.Atoi(stat.info[InfoValueSize])
			if metricsV1 {
				_ = metricsCli.EmitTimer("anycache.value_size", valueSize, tags...)
			}

			_ = metricsCliV2.EmitTimer("value_size", valueSize, tagsV2...)
		}

		if metricsV1 {
			_ = metricsCli.EmitCounter("anycache.counter", 1, tags...)
			_ = metricsCli.EmitTimer("anycache.latency", elapsed.Microseconds(), tags...)
		}

		_ = metricsCliV2.EmitRateCounter("rate", 1, tagsV2...)
		_ = metricsCliV2.EmitTimer("latency.us", elapsed.Microseconds(), tagsV2...)

		if rand.Intn(99) == 0 {
			_ = metricsCliV2.EmitRateCounter("psms", 1, tagsV2...)
		}
	}
}

func WithMetrics(ctx context.Context, key string, val string) {
	if v := ctx.Value(statKey); v != nil {
		v.(*statStruct).metricsInfo[key] = val
	}
}

func WithInfo(ctx context.Context, key string, val string) {
	if v := ctx.Value(statKey); v != nil {
		v.(*statStruct).info[key] = val
	}
}

func WithError(ctx context.Context, label string, err error) {
	if v := ctx.Value(statKey); v != nil {
		v.(*statStruct).addErr(label, err)
	}
}

func valueStat(ctx context.Context) (*statStruct, error) {
	v := ctx.Value(statKey)
	if v == nil {
		return nil, errors.New("stat not found")
	}
	stat, ok := v.(*statStruct)
	if !ok {
		return nil, errors.New("stat type error")
	}
	return stat, nil
}
