// Code generated by "stringer -type=ResultSource"; DO NOT EDIT.

package anycache

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[FromCache-0]
	_ = x[FromLoader-1]
	_ = x[FromLoaderPartially-2]
	_ = x[FromExpiredCache-3]
}

const _ResultSource_name = "FromCacheFromLoaderFromLoaderPartiallyFromExpiredCache"

var _ResultSource_index = [...]uint8{0, 9, 19, 38, 54}

func (i ResultSource) String() string {
	if i < 0 || i >= ResultSource(len(_ResultSource_index)-1) {
		return "ResultSource(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _ResultSource_name[_ResultSource_index[i]:_ResultSource_index[i+1]]
}
