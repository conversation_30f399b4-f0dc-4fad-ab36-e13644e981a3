package anycache

import (
	"testing"
)

//func Benchmark_slice2Interface(b *testing.B) {
//	var iList []interface{}
//	for i := 0; i < 100; i++ {
//		iList = append(iList, i)
//	}
//	b.ResetTimer()
//	for i := 0; i < b.N; i++ {
//		_ = slice2Interface(iList)
//	}
//}
//func Benchmark_slice2InterfacePtr(b *testing.B) {
//	var iList []interface{}
//	for i := 0; i < 100; i++ {
//		j := i
//		iList = append(iList, &j)
//	}
//	b.ResetTimer()
//	for i := 0; i < b.N; i++ {
//		_ = slice2Interface(iList)
//	}
//}

func Benchmark_slice2InterfaceReflect(b *testing.B) {
	var iList []interface{}
	for i := 0; i < 100; i++ {
		iList = append(iList, i)
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = slice2InterfaceReflect(iList)
	}
}
func Benchmark_slice2InterfaceReflectPtr(b *testing.B) {
	var iList []interface{}
	for i := 0; i < 100; i++ {
		j := i
		iList = append(iList, &j)
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = slice2InterfaceReflect(iList)
	}
}

//func Benchmark_interface2Slice(b *testing.B) {
//	var iList []int
//	for i := 0; i < 100; i++ {
//		iList = append(iList, i)
//	}
//	b.ResetTimer()
//	for i := 0; i < b.N; i++ {
//		_ = interface2Slice(iList, reflect.TypeOf(1), convert2Eface(1))
//	}
//}
//func Benchmark_interface2SliceReuse(b *testing.B) {
//	var iList []int
//	for i := 0; i < 100; i++ {
//		iList = append(iList, i)
//	}
//	i_, e := reflect.TypeOf(1), convert2Eface(1)
//	b.ResetTimer()
//	for i := 0; i < b.N; i++ {
//		_ = interface2Slice(iList, i_, e)
//	}
//}
func Benchmark_interface2SliceReflect(b *testing.B) {
	var iList []int
	for i := 0; i < 100; i++ {
		iList = append(iList, i)
	}
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = interface2SliceReflect(iList)
	}
}
