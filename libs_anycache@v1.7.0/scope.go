package anycache

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/stat"
	"code.byted.org/webcast/logw"
)

// -- private code ---

func (ac *AnyCache) get(ctx context.Context, inItem interface{}, rv reflect.Value) (ResultSource, error) {
	rv = indirect(rv)
	key := ac.option.loader.genKeyFromParams(ctx, inItem, ac.option.extraParam)
	stat.WithInfo(ctx, stat.InfoKey, key)

	// if ac.option.smartKeySync != nil {
	// 	ac.option.smartKeySync.Access(key)
	// }

	switch ac.option.sourceStrategy {
	case SsCacheFirst:
		if err := ac.getFromCache(ctx, key, rv); err == nil {
			return FromCache, nil
		} else if err == ErrNil {
			return FromCache, ErrNil
		} else {
			stat.WithError(ctx, "get:from_cache", err)
			if ac.option.returnFastWhenCacheErrFn(ctx, err) {
				return FromCache, err
			}
		}
		return FromLoader, ac.getFromLoader(ctx, key, inItem, rv, true)
	case SsSourceFirst:
		if err := ac.getFromLoader(ctx, key, inItem, rv, true); err == nil {
			return FromLoader, nil
		} else if err == ErrNil {
			return FromLoader, ErrNil
		} else {
			stat.WithError(ctx, "get:from_source", err)
		}

		return FromCache, ac.getFromCache(ctx, key, rv)
	case SsOnlyCache:
		return FromCache, ac.getFromCache(ctx, key, rv)
	case SsOnlySource:
		return FromLoader, ac.getFromLoader(ctx, key, inItem, rv, false)
	case SsExpiredDataBackup:
		return ac.getFromExpiredDataBackup(ctx, key, inItem, rv)
	case SsExpiredDataAndAsyncSource:
		return ac.getFromExpiredDataAndAsyncSource(ctx, key, inItem, rv)
	}

	panic("anycache: unknown source strategy")
}

func (ac *AnyCache) getFromExpiredDataBackup(ctx context.Context, key string, inItem interface{}, rv reflect.Value) (ResultSource, error) {
	var useExpiredData bool
	var entryNil bool

	if e, err := ac.cache.Get(cache.WithNeedExpiredData(ctx), ac.packKey(ctx, key)); err == nil {
		if err := ac.unmarshalCacheEntry(e, rv.Addr().Interface()); err == nil {
			// 如果缓存中的数据没有过期，直接返回
			if !e.IsDecayed() {
				stat.WithMetrics(ctx, stat.TagHit, "1")

				if ac.isNilEntry(e) {
					return FromCache, ErrNil
				} else {
					return FromCache, nil
				}
			}
			useExpiredData = true
			entryNil = ac.isNilEntry(e)
		} else {
			stat.WithError(ctx, "getFromExpiredDataBackup:cache_unmarshal", err)
		}
	} else {
		stat.WithError(ctx, "getFromExpiredDataBackup:cache", err)
	}

	// 如果回源成功，直接返回
	if err := ac.getFromLoader(ctx, key, inItem, rv, true); err == nil {
		return FromLoader, nil
	} else if err == ErrNil {
		return FromLoader, ErrNil
	} else {
		if useExpiredData {
			// 如果回源失败，使用过期缓存
			stat.WithMetrics(ctx, stat.TagHit, "1")

			if entryNil {
				return FromExpiredCache, ErrNil
			} else {
				return FromExpiredCache, nil
			}
		}
		return FromLoader, err
	}
}

func (ac *AnyCache) getFromExpiredDataAndAsyncSource(ctx context.Context, key string, inItem interface{}, rv reflect.Value) (ResultSource, error) {
	var useExpiredData bool
	var entryNil bool

	if e, err := ac.cache.Get(cache.WithNeedExpiredData(ctx), ac.packKey(ctx, key)); err == nil {
		if err0 := ac.unmarshalCacheEntry(e, rv.Addr().Interface()); err0 == nil {
			// 如果缓存中的数据没有过期，直接返回
			entryNil = ac.isNilEntry(e)

			stat.WithInfo(ctx, stat.InfoValueSize, strconv.Itoa(len(e.GetVal())))
			if !e.IsDecayed() {
				stat.WithMetrics(ctx, stat.TagHit, "1")

				if entryNil {
					return FromCache, ErrNil
				} else {
					return FromCache, nil
				}
			}
			useExpiredData = true
		} else {
			stat.WithError(ctx, "getFromExpiredDataAndAsyncSource:cache_unmarshal", err0)
		}
	} else {
		stat.WithError(ctx, "getFromExpiredDataAndAsyncSource:cache", err)
	}

	if useExpiredData {
		if !ac.option.refreshWorker.AddNonBlock(convertToAsyncRefreshParam(ctx, ac, inItem, ac.option.extraParam)) {
			stat.WithMetrics(ctx, stat.TagAsynFull, "1")
		}
		stat.WithMetrics(ctx, stat.TagHit, "1")
		if entryNil {
			return FromExpiredCache, ErrNil
		} else {
			return FromExpiredCache, nil
		}
	}

	// 如果回源成功，直接返回
	if err := ac.getFromLoader(ctx, key, inItem, rv, true); err == nil {
		return FromLoader, nil
	} else if err == ErrNil {
		stat.WithInfo(ctx, stat.InfoValueSize, "0")
		return FromLoader, ErrNil
	} else {
		return FromLoader, err
	}
}

func (ac *AnyCache) getFromLoader(ctx context.Context, key string, inItem interface{}, rv reflect.Value, needCache bool) error {
	stat.WithMetrics(ctx, stat.TagSource, "1")

	// 回源 singleflight
	resp, err, shared := ac.g.Do(ac.packKey(ctx, key), func() (v interface{}, err error) {
		return ac.option.loader.load(ctx, inItem, ac.option.extraParam)
	})
	if err != nil {
		return err
	}

	stat.WithInfo(ctx, stat.InfoSingleFlight, toggleStr(shared))

	// 返回值是 nil 并且 nil 是无效的
	if IsInterfaceNil(resp) && !ac.option.validNil {
		if ac.option.deleteNil {
			if err := ac.cache.MDel(ctx, ac.packKey(ctx, key)); err != nil {
				stat.WithError(ctx, "getFromLoader:delete_nil", err)
			}
		}
		return ErrNil
	}

	// 如果 resp 不是 nil，判断类型是不是相同
	if !IsInterfaceNil(resp) {
		rResp := indirect(reflect.ValueOf(resp))
		if rv.Type() != rResp.Type() {
			return errors.New("v type error")
		}
	}

	// 序列化
	entry, err := ac.newCacheEntry(ctx, key, resp)
	if err != nil {
		return err
	}

	// 赋值，通过序列化深度 copy 一份，避免共享
	if !ac.isNilEntry(entry) {
		if err := ac.unmarshalCacheEntry(entry, rv.Addr().Interface()); err != nil {
			return err
		}
	}

	// 采样缓存

	if needCache && ac.option.sampleFn(ctx, resp) {
		stat.WithInfo(ctx, stat.InfoSample, "1")
		stat.WithInfo(ctx, stat.InfoValueSize, strconv.Itoa(len(entry.GetVal())))
		if ac.option.asyncSaveCache {
			ac.asyncSaveCacheChan <- asyncSaveCacheChanElem{ctx: ctx, entries: []cache.IEntry{entry}}
		} else {
			err := ac.cache.Set(ctx, entry)
			if err != nil {
				logw.CtxWarnKVs(ctx, "anycache: getFromLoader:set failed", "err", err)
				stat.WithError(ctx, "getFromLoader:set", err)
			}
		}
	}

	if IsInterfaceNil(resp) {
		return ErrNil
	}

	return nil
}

func (ac *AnyCache) getFromCache(ctx context.Context, key string, rv reflect.Value) error {
	e, err := ac.cache.Get(ctx, ac.packKey(ctx, key))

	if err != nil {
		return err
	}

	if ac.isNilEntry(e) {
		return ErrNil
	}
	stat.WithInfo(ctx, stat.InfoValueSize, strconv.Itoa(len(e.GetVal())))
	if err := ac.unmarshalCacheEntry(e, rv.Addr().Interface()); err != nil {
		return err
	}

	stat.WithMetrics(ctx, stat.TagHit, "1")
	return nil
}

func (ac *AnyCache) refresh(ctx context.Context, inItem interface{}) (interface{}, error) {
	val, err := ac.option.loader.load(ctx, inItem, ac.option.extraParam)
	if err != nil {
		return nil, err
	}

	key := ac.option.loader.genKeyFromParams(ctx, inItem, ac.option.extraParam)
	return val, ac.set(ctx, key, val)
}

func (ac *AnyCache) set(ctx context.Context, key string, val interface{}) error {
	entry, err := ac.newCacheEntry(ctx, key, val)
	if err != nil {
		return err
	}

	if ac.isNilEntry(entry) && !ac.option.validNil {
		return ac.cache.MDel(ctx, ac.packKey(ctx, key))
	}

	return ac.cache.Set(ctx, entry)
}

// -- multi start ---

func (ac *AnyCache) mGet(ctx context.Context, inItems interface{}, rv reflect.Value) (from ResultSource, err0 error) {
	outItemsV := rv.Elem()
	inItemList := interface2SliceReflect(inItems)
	itemT := outItemsV.Type().Elem()
	// 容量不足，扩容
	if outItemsV.Cap() < len(inItemList) {
		newSlice := reflect.MakeSlice(reflect.SliceOf(itemT), len(inItemList), len(inItemList))
		outItemsV.Set(newSlice)
	}
	// 确保 length 等于入参长度
	outItemsV.SetLen(len(inItemList))

	keys := make([]string, len(inItemList))
	keyInItemMap := make(map[string]interface{}, len(inItemList))
	keyIndexMap := make(map[string][]int, len(inItemList))
	for i, item := range inItemList {
		key := ac.option.batchLoader.genKeyFromParams(ctx, item, ac.option.extraParam)
		keys[i] = key
		keyInItemMap[key] = item
		keyIndexMap[key] = append(keyIndexMap[key], i)
	}
	stat.WithInfo(ctx, stat.InfoKey, strings.Join(keys, ", "))

	// FIXME 这里用 defer 实现合适吗？
	defer func() {
		if err0 == nil {
			if ac.option.filterNil {
				filterNil(ctx, rv.Elem())
			}
		}
	}()

	// if ac.option.smartKeySync != nil {
	// 	for _, key := range keys {
	// 		ac.option.smartKeySync.Access(key)
	// 	}
	// }

	switch ac.option.sourceStrategy {
	case SsCacheFirst:
		missedKeys, err := ac.mGetFromCache(ctx, keyInItemMap, keyIndexMap, itemT, outItemsV)
		if err != nil {
			stat.WithError(ctx, "mGet:from_cache", err)
			if ac.option.returnFastWhenCacheErrFn(ctx, err) {
				return FromCache, err
			}
			// 全量 key 都算 miss
			missedKeys = keys
		}
		// 如果没有 miss 的 key，就直接返回
		if len(missedKeys) == 0 {
			return FromCache, nil
		}

		from := FromLoader
		if len(missedKeys) < len(keyInItemMap) {
			from = FromLoaderPartially
		}

		missedKeyInItemMap := getMissedKeyInItemMap(keyInItemMap, missedKeys)

		_, err = ac.mGetFromLoader(ctx, missedKeyInItemMap, keyIndexMap, itemT, outItemsV, true)
		return from, err

	case SsSourceFirst:
		missedKeys, err := ac.mGetFromLoader(ctx, keyInItemMap, keyIndexMap, itemT, outItemsV, true)
		if err != nil {
			stat.WithError(ctx, "mGet:from_source", err)
			// 全量 key 都算 miss
			missedKeys = keys
		}
		// 如果没有 miss 的 key，就直接返回
		if len(missedKeys) == 0 {
			return FromLoader, nil
		}

		from := FromCache
		if len(missedKeys) < len(keyInItemMap) {
			from = FromLoaderPartially
		}
		missedKeyInItemMap := getMissedKeyInItemMap(keyInItemMap, missedKeys)
		missedKeys, err1 := ac.mGetFromCache(ctx, missedKeyInItemMap, keyIndexMap, itemT, outItemsV)
		// 缓存中数据是全的
		if err1 == nil && len(missedKeys) == 0 {
			return from, nil
		}

		// 缓存报错或数据不全时，把回源的错误返回
		return from, err
	case SsOnlySource:
		_, err := ac.mGetFromLoader(ctx, keyInItemMap, keyIndexMap, itemT, outItemsV, false)
		return FromLoader, err
	case SsOnlyCache:
		_, err := ac.mGetFromCache(ctx, keyInItemMap, keyIndexMap, itemT, outItemsV)
		return FromCache, err
	case SsExpiredDataBackup:
		return ac.mGetFromExpiredDataBackup(ctx, keyInItemMap, keyIndexMap, itemT, outItemsV)
	case SsExpiredDataAndAsyncSource:
		return ac.mGetFromExpiredDataAndAsyncSource(ctx, keyInItemMap, keyIndexMap, itemT, outItemsV)
	}

	panic("anycache: unknown source strategy")
}

func getMissedKeyInItemMap(keyInItemMap map[string]interface{}, missedKeys []string) map[string]interface{} {
	missedKeyInItemMap := make(map[string]interface{})
	for _, key := range missedKeys {
		missedKeyInItemMap[key] = keyInItemMap[key]
	}

	return missedKeyInItemMap
}

func (ac *AnyCache) mGetFromExpiredDataBackup(ctx context.Context, keyInItemMap map[string]interface{}, keyIndexMap map[string][]int, itemT reflect.Type, outItemsV reflect.Value) (ResultSource, error) {
	var (
		missedKeys []string
		cacheAll   bool // 缓存中是否有全部数据，不管是不是过期
	)

	keys := make([]string, 0, len(keyInItemMap))
	packedKeys := make([]string, 0, len(keyInItemMap))
	for key := range keyInItemMap {
		keys = append(keys, key)
		packedKeys = append(packedKeys, ac.packKey(ctx, key))
	}
	// 从缓存里拿数据，包括过期的数据
	entries, err := ac.cache.MGet(cache.WithNeedExpiredData(ctx), packedKeys...)
	if err == nil {
		cacheAll = true
		if len(keys) != len(entries) {
			panic("anycache: can not happen!")
		}

		for i := range entries {
			if entries[i] != nil {
				noErr := true
				for _, idx := range keyIndexMap[keys[i]] {
					newV, err := ac.unmarshalCacheEntry2(entries[i], itemT)
					noErr = noErr && err == nil
					if err == nil {
						outItemsV.Index(idx).Set(newV)
					} else {
						stat.WithError(ctx, "mGetFromExpiredDataBackup:cache_unmarshal", err)
					}
				}
				if noErr {
					if !entries[i].IsDecayed() {
						continue
					}
				}
			} else {
				cacheAll = false
			}

			missedKeys = append(missedKeys, keys[i])
		}
	} else {
		missedKeys = keys
		stat.WithError(ctx, "mGetFromExpiredDataBackup:cache", err)
	}

	// 没有 miss 的数据
	if len(missedKeys) == 0 {
		stat.WithMetrics(ctx, stat.TagHit, "1")
		return FromCache, nil
	}

	missedKeyInItemMap := getMissedKeyInItemMap(keyInItemMap, missedKeys)
	if _, err = ac.mGetFromLoader(ctx, missedKeyInItemMap, keyIndexMap, itemT, outItemsV, true); err == nil {
		if len(keyInItemMap) > len(missedKeys) {
			stat.WithMetrics(ctx, stat.TagPartHit, "1")
			return FromLoaderPartially, nil
		} else {
			stat.WithMetrics(ctx, stat.TagSource, "1")
			return FromLoader, nil
		}
	} else if cacheAll {
		// 如果回源失败，但缓存中有全部数据，就用过期数据 backup
		stat.WithMetrics(ctx, stat.TagHit, "1")
		return FromExpiredCache, nil
	} else {
		stat.WithMetrics(ctx, stat.TagPartHit, "1")
		return FromLoader, err
	}
}

func (ac *AnyCache) mGetFromExpiredDataAndAsyncSource(ctx context.Context, keyInItemMap map[string]interface{}, keyIndexMap map[string][]int, itemT reflect.Type, outItemsV reflect.Value) (ResultSource, error) {
	var (
		missedKeys []string
		cacheAll   bool // 缓存中是否有全部数据，不管是不是过期
	)

	keys := make([]string, 0, len(keyInItemMap))
	packedKeys := make([]string, 0, len(keyInItemMap))
	for key := range keyInItemMap {
		keys = append(keys, key)
		packedKeys = append(packedKeys, ac.packKey(ctx, key))
	}
	// 从缓存里拿数据，包括过期的数据
	entries, err := ac.cache.MGet(cache.WithNeedExpiredData(ctx), packedKeys...)
	if err == nil {
		cacheAll = true
		if len(keys) != len(entries) {
			panic("anycache: can not happen!")
		}

		for i := range entries {
			if entries[i] != nil {
				noErr := true
				for _, idx := range keyIndexMap[keys[i]] {
					newV, err := ac.unmarshalCacheEntry2(entries[i], itemT)
					noErr = noErr && err == nil
					if err == nil {
						outItemsV.Index(idx).Set(newV)
					} else {
						stat.WithError(ctx, "mGetFromExpiredDataBackup:cache_unmarshal", err)
					}
				}
				if noErr {
					if !entries[i].IsDecayed() {
						continue
					}
				}
			} else {
				cacheAll = false
			}

			missedKeys = append(missedKeys, keys[i])
		}
	} else {
		missedKeys = keys
		stat.WithError(ctx, "mGetFromExpiredDataBackup:cache", err)
	}

	// 没有 miss 的数据
	if len(missedKeys) == 0 {
		stat.WithMetrics(ctx, stat.TagHit, "1")
		return FromCache, nil
	}

	missedKeyInItemMap := getMissedKeyInItemMap(keyInItemMap, missedKeys)
	// 有过期数据，但没有 miss 数据
	if cacheAll {
		itemList := make([]interface{}, 0, len(missedKeyInItemMap))
		for k := range missedKeyInItemMap {
			itemList = append(itemList, missedKeyInItemMap[k])
		}
		if !ac.option.batchRefreshWorker.AddNonBlock(convertToAsyncRefreshParam(ctx, ac, itemList, ac.option.extraParam)) {
			stat.WithMetrics(ctx, stat.TagAsynFull, "1")
		}
		stat.WithMetrics(ctx, stat.TagHit, "1")
		return FromExpiredCache, nil
	}
	if _, err = ac.mGetFromLoader(ctx, missedKeyInItemMap, keyIndexMap, itemT, outItemsV, true); err == nil {
		if len(keyInItemMap) > len(missedKeys) {
			stat.WithMetrics(ctx, stat.TagPartHit, "1")
		} else {
			stat.WithMetrics(ctx, stat.TagSource, "1")
		}
		return FromLoader, nil
	} else {
		stat.WithMetrics(ctx, stat.TagPartHit, "1")
		return FromLoader, err
	}
}

func (ac *AnyCache) mGetFromLoader(ctx context.Context, keyInItemMap map[string]interface{}, keyIndexMap map[string][]int, itemT reflect.Type, outItemsV reflect.Value, needCache bool) ([]string, error) {
	stat.WithMetrics(ctx, stat.TagSource, "1")

	// 通过 singleflight 来回源
	tmpKeys := make([]string, 0, len(keyInItemMap))
	inItems := make([]interface{}, 0, len(keyInItemMap))
	for key := range keyInItemMap {
		tmpKeys = append(tmpKeys, key)
	}
	sort.Strings(tmpKeys)
	for _, key := range tmpKeys {
		inItems = append(inItems, keyInItemMap[key])
	}

	respIf, err, shared := ac.g.Do("batch:"+ac.packKey(ctx, strings.Join(tmpKeys, ":")), func() (interface{}, error) {
		return batchMultiFetch(ctx, inItems, ac.option.extraParam, ac.option)
	})
	if err != nil && !ac.option.partiallyCache {
		return nil, err
	}
	if err != nil {
		stat.WithError(ctx, "mGetFromLoader:batchMultiFetch:partiallyCache", err)
	}
	stat.WithInfo(ctx, stat.InfoSingleFlight, toggleStr(shared))

	respList := respIf.(map[string]interface{})

	var missedKey []string
	var entrySlice []cache.IEntry

	for key, val := range respList {
		// 不缓存 nil
		if IsInterfaceNil(val) && !ac.option.validNil {
			missedKey = append(missedKey, key)
			continue
		}

		// 类型判断
		if val != nil {
			rv := reflect.ValueOf(val)
			if rv.Type() != itemT {
				if rv.Kind() == reflect.Ptr && rv.Elem().Type() == itemT {
					rv = rv.Elem()
				} else if itemT.Kind() == reflect.Ptr && itemT.Elem() == rv.Type() {
					rv = reflect.NewAt(itemT.Elem(), unpackEface(val))
				}
			}

			if rv.Type() != itemT {
				panic(fmt.Sprintf("anycache: invalid type: %s != %s", rv.Type(), itemT))
			}
		}

		// 序列化
		entry, err := ac.newCacheEntry(ctx, key, val)
		if err != nil {
			stat.WithError(ctx, "mGetFromLoader:marshal", err)
			continue
		}

		// 赋值
		if !ac.isNilEntry(entry) {
			if idxs, ok := keyIndexMap[key]; ok {
				for _, idx := range idxs {
					if newV, err := ac.unmarshalCacheEntry2(entry, itemT); err == nil {
						outItemsV.Index(idx).Set(newV)
					} else {
						stat.WithError(ctx, fmt.Sprintf("mGetFromLoader:unmarshal, idx: %v", idx), err)
					}
				}
			}
		}

		// 采样写缓存
		if needCache && ac.option.sampleFn(ctx, val) {
			stat.WithInfo(ctx, stat.InfoSample, "1")
			stat.WithInfo(ctx, stat.InfoValueSize, strconv.Itoa(len(entry.GetVal())))
			entrySlice = append(entrySlice, entry)
		}
	}
	saveMGetFromLoaderCache(ctx, entrySlice, ac)
	if len(missedKey) > 0 {
		if !ac.option.validNil && ac.option.deleteNil {
			packedKeys := make([]string, len(missedKey))
			for i := range missedKey {
				packedKeys[i] = ac.packKey(ctx, missedKey[i])
			}
			if err := ac.cache.MDel(ctx, packedKeys...); err != nil {
				stat.WithError(ctx, "mGetFromLoader:delete_nil", err)
			}
		}
	}

	return missedKey, err
}

func saveMGetFromLoaderCache(ctx context.Context, entrySlice []cache.IEntry, ac *AnyCache) {
	entrySliceLen := len(entrySlice)
	if entrySliceLen > 0 {
		// 如果没有设置单次最大回源数或者本次回源的 item 没有超过最大回源数，直接回源
		if ac.option.maxFetchItemsCount <= 0 || entrySliceLen <= ac.option.maxFetchItemsCount {
			saveMGetFromLoaderCacheFromAsyncOrMSet(ctx, ac, entrySlice)
		} else {
			partitionCount := entrySliceLen / ac.option.maxFetchItemsCount
			if entrySliceLen%ac.option.maxFetchItemsCount != 0 {
				partitionCount++
			}
			for i := 0; i < partitionCount; i++ {
				var partitionEntrySlice []cache.IEntry
				start := i * ac.option.maxFetchItemsCount
				stop := (i + 1) * ac.option.maxFetchItemsCount
				if i < partitionCount-1 {
					partitionEntrySlice = entrySlice[start:stop]
				} else {
					partitionEntrySlice = entrySlice[start:]
				}
				saveMGetFromLoaderCacheFromAsyncOrMSet(ctx, ac, partitionEntrySlice)
			}
		}
	}
}

func saveMGetFromLoaderCacheFromAsyncOrMSet(ctx context.Context, ac *AnyCache, entrySlice []cache.IEntry) {
	if ac.option.asyncSaveCache {
		ac.asyncSaveCacheChan <- asyncSaveCacheChanElem{ctx: ctx, entries: entrySlice}
	} else {
		err := ac.cache.MSet(ctx, entrySlice...)
		if err != nil {
			stat.WithError(ctx, "mGetFromLoader:mSet", err)
		}
	}
}

func (ac *AnyCache) mGetFromCache(ctx context.Context, keyInItemMap map[string]interface{}, keyIndexMap map[string][]int, itemT reflect.Type, outItemsV reflect.Value) ([]string, error) {
	keys := make([]string, 0, len(keyInItemMap))
	packedKeys := make([]string, 0, len(keyInItemMap))
	for key := range keyInItemMap {
		keys = append(keys, key)
		packedKeys = append(packedKeys, ac.packKey(ctx, key))
	}

	// 从缓存里拿数据
	entries, err := ac.cache.MGet(ctx, packedKeys...)
	maxSize := 0
	for _, entrie := range entries {
		if entrie != nil {
			if len(entrie.GetVal()) > maxSize {
				maxSize = len(entrie.GetVal())
			}
		}
	}
	stat.WithInfo(ctx, stat.InfoValueSize, strconv.Itoa(maxSize))

	if err == nil {
		if len(keys) != len(entries) {
			panic("anycache: can not happen!")
		}

		var missedKeys []string

		for i := range entries {
			if entries[i] != nil {
				if newV, err := ac.unmarshalCacheEntry2(entries[i], itemT); err == nil {
					for _, idx := range keyIndexMap[keys[i]] {
						outItemsV.Index(idx).Set(newV)
					}
					continue
				} else {
					stat.WithError(ctx, fmt.Sprintf("mGetFromCache:cache_unmarshal, idx: %v", i), err)
				}
			}

			missedKeys = append(missedKeys, keys[i])
		}

		if len(missedKeys) == 0 {
			stat.WithMetrics(ctx, stat.TagHit, "1")
		} else if len(missedKeys) < len(keyInItemMap) {
			stat.WithMetrics(ctx, stat.TagPartHit, "1")
		}

		return missedKeys, nil
	}

	return nil, err
}

func (ac *AnyCache) mRefresh(ctx context.Context, items []interface{}) error {
	keyValMap, err := batchMultiFetch(ctx, items, ac.option.extraParam, ac.option)
	if err != nil && !ac.option.partiallyCache {
		return err
	}
	var logicError error
	if err != nil {
		logicError = err
	}
	if keyValMap == nil {
		return logicError
	}
	err = ac.mSet(ctx, keyValMap)
	if err != nil {
		return err
	}
	return logicError
}

func (ac *AnyCache) mSet(ctx context.Context, keyValMap map[string]interface{}) error {
	var entrySlice []cache.IEntry

	var packedNilKeys []string
	for key, val := range keyValMap {
		entry, err := ac.newCacheEntry(ctx, key, val)
		if err != nil {
			return err
		}
		if ac.isNilEntry(entry) && !ac.option.validNil {
			packedNilKeys = append(packedNilKeys, ac.packKey(ctx, key))
			continue
		}

		entrySlice = append(entrySlice, entry)
	}

	if len(entrySlice) > 0 {
		if err := ac.cache.MSet(ctx, entrySlice...); err != nil {
			stat.WithError(ctx, "mSet:to_cache", err)
			logw.CtxWarnKVs(ctx, "anycache: mSet:to_cache failed", "err", err)
			return err
		}
	}

	if len(packedNilKeys) > 0 {
		if !ac.option.validNil && ac.option.deleteNil {
			if err := ac.cache.MDel(ctx, packedNilKeys...); err != nil {
				stat.WithError(ctx, "mSet:delete_nil", err)
				return err
			}
		}
	}

	return nil
}

func (ac *AnyCache) getCustomTTL(ctx context.Context, val interface{}) (time.Duration, time.Duration) {
	if ac.option != nil && ac.option.customTTLFunc != nil {
		ttl, delTTL := ac.option.customTTLFunc(ctx, val)
		if ttl > 0 && delTTL > 0 {
			return ttl, delTTL
		}
	}
	return ac.option.ttl, ac.option.delTTL
}

func (ac *AnyCache) NewEntryWithObject(ctx context.Context, key string, value interface{}, ttl, delTTL time.Duration) cache.IEntry {
	if cache.HasCustomTTL(ctx) {
		customTTL, customDelTTL := ac.getCustomTTL(ctx, value)
		return cache.NewCustomTTLEntryWithObject(key, value, customTTL, customDelTTL, customTTL, customDelTTL)
	}
	return cache.NewEntryWithObject(key, value, ttl, delTTL)
}

func (ac *AnyCache) NewEntry(ctx context.Context, key string, val interface{}, data []byte, ttl, delTTL time.Duration) cache.IEntry {
	if cache.HasCustomTTL(ctx) {
		customTTL, customDelTTL := ac.getCustomTTL(ctx, val)
		return cache.NewCustomTTLEntry(key, data, customTTL, customDelTTL, customTTL, customDelTTL)
	}
	return cache.NewEntry(key, data, ttl, delTTL)
}

// -- common --
func (ac *AnyCache) newCacheEntry(ctx context.Context, key string, val interface{}) (cache.IEntry, error) {
	var (
		data = ac.option.nilCacheDefaultValue
		err  error
	)
	key = ac.packKey(ctx, key)
	ttl, delTTL := ac.getCustomTTL(ctx, val)
	if ac.option.objectCache {
		if IsInterfaceNil(val) {
			if ac.option.nilTTL != nil {
				return cache.NewEntryWithObject(key, nil, *ac.option.nilTTL, *ac.option.nilTTL), nil
			} else {
				return ac.NewEntryWithObject(ctx, key, nil, ttl, delTTL), nil
			}
		}

		return ac.NewEntryWithObject(ctx, key, indirect(reflect.ValueOf(val)).Interface(), ttl, delTTL), nil
	}

	if !IsInterfaceNil(val) {
		data, err = ac.codec.Marshal(val)
		if err != nil {
			return nil, err
		}
	}

	if ac.option.noCacheOverhead {
		return cache.NewEntryNoOverhead(key, data, ac.option.ttl), nil
	} else {
		if ac.option.nilTTL != nil && IsInterfaceNil(val) {
			return cache.NewEntry(key, data, *ac.option.nilTTL, *ac.option.nilTTL), nil
		} else {
			return ac.NewEntry(ctx, key, val, data, ttl, delTTL), nil
		}
	}
}

func (ac *AnyCache) unmarshalCacheEntry(entry cache.IEntry, v interface{}) error {
	if ac.isNilEntry(entry) {
		return nil
	}

	if entry.IsObjectCache() {
		rvPtr := reflect.ValueOf(v)
		rv := indirect(rvPtr)
		rv.Set(reflect.ValueOf(entry.GetObject()))
		return nil
	}

	return ac.codec.Unmarshal(entry.GetVal(), v)
}

func (ac *AnyCache) unmarshalCacheEntry2(entry cache.IEntry, rt reflect.Type) (reflect.Value, error) {
	// 如果是 nil entry，就不要往下走了；会发生指针的零值不是 nil 的情况
	if ac.isNilEntry(entry) {
		return reflect.New(rt).Elem(), nil
	}

	if entry.IsObjectCache() {
		return reflect.ValueOf(entry.GetObject()), nil
	}

	newV := reflect.New(indirectType(rt))
	err := ac.codec.Unmarshal(entry.GetVal(), newV.Interface())
	if err != nil {
		return reflect.Value{}, err
	}

	newV = convertTo(newV, rt)
	return newV, nil
}

func (ac *AnyCache) packKey(ctx context.Context, key string) string {
	if stressTag := getStressTag(ctx); stressTag != "" {
		key = "stress:" + key
	}

	if ac.option.ns == "" {
		return key
	}

	return ac.option.ns + ":" + key
}

func (ac *AnyCache) isNilEntry(entry cache.IEntry) bool {
	if entry.IsObjectCache() {
		return IsInterfaceNil(entry.GetObject())
	}

	return entry == nil || compareBytes(entry.GetVal(), ac.option.nilCacheDefaultValue)
}
