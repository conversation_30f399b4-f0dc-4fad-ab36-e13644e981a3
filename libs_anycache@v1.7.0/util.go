package anycache

import (
	"context"
	"reflect"
	"sync"
	"unsafe"
)

//go:generate stringer -type=ResultSource
type ResultSource int

const (
	FromCache           ResultSource = 0
	FromLoader          ResultSource = 1
	FromLoaderPartially ResultSource = 2
	FromExpiredCache    ResultSource = 3
)

func indirect(rv reflect.Value) reflect.Value {
	for rv.Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	return rv
}

func indirectType(rt reflect.Type) reflect.Type {
	for rt.Kind() == reflect.Ptr {
		rt = rt.Elem()
	}
	return rt
}

func convertTo(rv reflect.Value, rt reflect.Type) reflect.Value {
	if rv.Type() != rt {
		rv = rv.Elem()
	}

	i := 0
	for rv.Type() != rt {
		newVv := reflect.New(rv.Type())
		newVv.Elem().Set(rv)
		rv = newVv

		if i++; i > 2 {
			panic("rv can not convert to rt")
		}
	}

	return rv
}

type eface struct {
	Type, Data unsafe.Pointer
}

func unpackEface(i interface{}) unsafe.Pointer {
	ii := *(*eface)(unsafe.Pointer(&i))
	return ii.Data
}

func IsInterfaceNil(i interface{}) bool {
	if i == nil {
		return true
	}
	return unpackEface(i) == nil
}

func convert2Eface(i interface{}) *eface {
	ii := *(*eface)(unsafe.Pointer(&i))
	return &ii
}

func packEface(ptr *eface) interface{} {
	ii := *(*interface{})(unsafe.Pointer(ptr))
	return ii
}

// func slice2Interface(iList []interface{}) interface{} {
//	i1 := iList[0]
//	i1t := reflect.TypeOf(i1)
//	size := i1t.Size()
//
//	length := len(iList)
//	iSlice := reflect.MakeSlice(reflect.SliceOf(i1t), length, length).Interface()
//	sh := (*reflect.SliceHeader)(unpackEface(iSlice))
//	for i := 0; i < length; i++ {
//		ptr := sh.Data + size*uintptr(i)
//		dst, src := ptr, uintptr(unpackEface(iList[i]))
//		if i1t.Kind() == reflect.Ptr {
//			*(*uintptr)((unsafe.Pointer)(dst)) = src
//		} else {
//			memcopy(src, dst, size)
//		}
//	}
//
//	return iSlice
// }

func memcopy(src, dst uintptr, size uintptr) {
	var offset uintptr = 0
	for offset < size {
		left := size - offset
		switch {
		case left >= unsafe.Sizeof(int64(1)):
			*(*int64)((unsafe.Pointer)(dst + offset)) = *(*int64)((unsafe.Pointer)(src + offset))
			offset += unsafe.Sizeof(int64(1))
		case left >= unsafe.Sizeof(int32(1)):
			*(*int32)((unsafe.Pointer)(dst + offset)) = *(*int32)((unsafe.Pointer)(src + offset))
			offset += unsafe.Sizeof(int32(1))
		case left >= unsafe.Sizeof(int16(1)):
			*(*int16)((unsafe.Pointer)(dst + offset)) = *(*int16)((unsafe.Pointer)(src + offset))
			offset += unsafe.Sizeof(int16(1))
		case left >= unsafe.Sizeof(int8(1)):
			*(*int8)((unsafe.Pointer)(dst + offset)) = *(*int8)((unsafe.Pointer)(src + offset))
			offset += unsafe.Sizeof(int8(1))
		}
	}
}

func slice2InterfaceReflect(iList []interface{}) interface{} {
	i1 := iList[0]
	i1t := reflect.TypeOf(i1)
	length := len(iList)
	iSlice := reflect.MakeSlice(reflect.SliceOf(i1t), 0, length)

	for i := range iList {
		rv := reflect.ValueOf(iList[i])
		iSlice = reflect.Append(iSlice, rv)
	}

	return iSlice.Interface()
}

// func interface2Slice(i interface{}, i_ reflect.Type, e *eface) []interface{} {
//	header := (*reflect.SliceHeader)(unpackEface(i))
//	var iSlice []interface{}
//	for j := 0; j < header.Len; j++ {
//		rvEface := *e
//		rvEface.Data = unsafe.Pointer(header.Data + i_.Size()*uintptr(j))
//		iSlice = append(iSlice, packEface(&rvEface))
//	}
//
//	return iSlice
// }

func interface2MapReflect(i interface{}) map[string]interface{} {
	rv := reflect.ValueOf(i)
	if rv.Kind() != reflect.Map {
		panic("i must be map")
	}

	iMap := make(map[string]interface{}, rv.Len())
	mapIter := rv.MapRange()
	for mapIter.Next() {
		key := mapIter.Key().Interface().(string)
		value := mapIter.Value().Interface()
		iMap[key] = value
	}

	return iMap
}

func interface2SliceReflect(i interface{}) []interface{} {
	rv := reflect.ValueOf(i)
	if rv.Kind() != reflect.Slice {
		panic("i must be slice")
	}

	var iSlice []interface{}
	for i := 0; i < rv.Len(); i++ {
		iSlice = append(iSlice, rv.Index(i).Interface())
	}
	return iSlice
}

func batchMultiFetch(ctx context.Context, items []interface{}, extraParam interface{}, option *option) (map[string]interface{}, error) {
	resMap := make(map[string]interface{}, len(items))
	lock := &sync.Mutex{}
	mf := option.batchLoader
	maxItemsCount := option.maxFetchItemsCount
	partiallyCache := option.partiallyCache
	for _, item := range items {
		resMap[mf.genKeyFromParams(ctx, item, extraParam)] = nil
	}
	if len(items) == 0 {
		return resMap, nil
	}

	// 如果没有设置单次最大回源数或者本次回源的 item 没有超过最大回源数，直接回源
	if maxItemsCount <= 0 || len(items) <= maxItemsCount {
		res, err := mf.batchLoad(ctx, slice2InterfaceReflect(items), extraParam)
		if !partiallyCache && err != nil {
			return nil, err
		}
		if res == nil {
			return resMap, err
		}
		fillRes(ctx, lock, res, resMap, mf.genKeyFromResults, extraParam)
		return resMap, err
	}

	roundCount := len(items) / maxItemsCount
	if len(items)%maxItemsCount != 0 {
		roundCount += 1
	}

	errList := make([]error, roundCount)

	var wg sync.WaitGroup
	wg.Add(roundCount)
	for i := 0; i < roundCount; i++ {
		var partItems []interface{}
		start := i * maxItemsCount
		stop := (i + 1) * maxItemsCount
		if i < roundCount-1 {
			partItems = items[start:stop]
		} else {
			partItems = items[start:]
		}

		go func(i int, items []interface{}) {
			defer wg.Done()

			res, err := mf.batchLoad(ctx, slice2InterfaceReflect(items), extraParam)
			errList[i] = err
			if res == nil {
				return
			}
			if err == nil || partiallyCache {
				fillRes(ctx, lock, res, resMap, mf.genKeyFromResults, extraParam)
			}
		}(i, partItems)
	}
	wg.Wait()

	// TODO 错误处理
	var err error
	for i := 0; i < roundCount; i++ {
		if errList[i] != nil && !partiallyCache {
			return nil, errList[i]
		}
		if errList[i] != nil {
			err = errList[i]
			break
		}
	}

	return resMap, err
}

func fillRes(ctx context.Context, lock sync.Locker, res interface{}, resMap map[string]interface{}, genKeyForOutItem genKeyFunc, extraParam interface{}) {
	lock.Lock()
	defer lock.Unlock()

	resRv := reflect.ValueOf(res)
	switch resRv.Kind() {
	case reflect.Slice:
		resList := interface2SliceReflect(res)
		for _, v := range resList {
			if !IsInterfaceNil(v) {
				key := genKeyForOutItem(ctx, v, extraParam)
				resMap[key] = v
			}
		}
	case reflect.Map:
		m := interface2MapReflect(res)
		for k, v := range m {
			resMap[k] = v
		}
	default:
		panic("res must be slice or map")
	}
}

func filterNil(ctx context.Context, rv reflect.Value) {
	if rv.Kind() != reflect.Slice {
		panic("must be slice")
	}

	var notNilCount int
	for i := 0; i < rv.Len(); i++ {
		if rv.Index(i).IsNil() {
			continue
		}

		rv.Index(notNilCount).Set(rv.Index(i))
		notNilCount++
	}

	rv.SetLen(notNilCount)
}

func toggleStr(b bool) string {
	toggle := "0"
	if b {
		toggle = "1"
	}
	return toggle
}

func compareBytes(a, b []byte) bool {
	if len(a) != len(b) {
		return false
	}

	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}

	return true
}

const stressTag = "K_STRESS"

func getStressTag(ctx context.Context) string {
	if ctx == nil {
		return ""
	}
	v := ctx.Value(stressTag)
	switch v := v.(type) {
	case string:
		return v
	case *string:
		return *v
	}
	return ""
}
