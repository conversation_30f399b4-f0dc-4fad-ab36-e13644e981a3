package ctxvalues

import "context"

const (
	CtxKeyEnv   = "K_ENV"
	CtxKeyLogID = "K_LOGID"
)

func CopyContext(ctx context.Context, keys ...string) context.Context {
	newCtx := context.Background()
	for _, key := range keys {
		v, ok := getStringFromContext(ctx, key)
		if ok {
			newCtx = context.WithValue(newCtx, key, v)
		}
	}

	return newCtx
}

func getStringFromContext(ctx context.Context, key string) (string, bool) {
	if ctx == nil {
		return "", false
	}

	v := ctx.Value(key)
	if v == nil {
		return "", false
	}

	switch v := v.(type) {
	case string:
		return v, true
	case *string:
		if v == nil {
			return "", false
		}
		return *v, true
	}
	return "", false
}
