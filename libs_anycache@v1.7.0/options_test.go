package anycache

import (
	"context"
	"fmt"
	"strconv"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"code.byted.org/webcast/libs_anycache/testcase"
)

func TestAnyCache_WithFilterNilInSliceRet(t *testing.T) {
	cc := NewDefault().WithFilterNilInSliceRet(true).
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				v := item.(*testcase.Diamond)
				return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
			})

	var diamondList []*testcase.Diamond
	_, err1 := cc.MGet(context.TODO(), []int64{1, 2, 100, 3}, &diamondList)
	assert.NoError(t, err1)
	assert.Equal(t, 3, len(diamondList))
	for _, d := range diamondList {
		assert.NotNil(t, d)
	}
}

func TestAnyCache_WithAsyncSaveCache(t *testing.T) {
	cc := NewDefault().WithAsyncSaveCache(true).
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				v := item.(*testcase.Diamond)
				return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
			})

	ctx := context.TODO()

	var res1 []*testcase.Diamond
	from1, err1 := cc.MGet(ctx, []int64{1, 2, 3}, &res1)
	assert.NoError(t, err1)
	assert.Equal(t, FromLoader, from1)

	var res2 []*testcase.Diamond
	from2, err2 := cc.MGet(ctx, []int64{1, 2, 3}, &res2)
	assert.NoError(t, err2)
	assert.Equal(t, FromLoader, from2)

	// 每 1 秒写一次缓存
	time.Sleep(5 * time.Second)

	var res3 []*testcase.Diamond
	from3, err3 := cc.MGet(ctx, []int64{1, 2, 3}, &res3)
	assert.NoError(t, err3)
	assert.Equal(t, FromCache, from3)
}

func TestAnyCache_WithAsyncSaveCacheFalse(t *testing.T) {
	cc := NewDefault().WithAsyncSaveCache(false).
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				v := item.(*testcase.Diamond)
				return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
			})

	ctx := context.TODO()

	var res1 []*testcase.Diamond
	from1, err1 := cc.MGet(ctx, []int64{1, 2, 3}, &res1)
	assert.NoError(t, err1)
	assert.Equal(t, FromLoader, from1)

	var res2 []*testcase.Diamond
	from2, err2 := cc.MGet(ctx, []int64{1, 2, 3}, &res2)
	assert.NoError(t, err2)
	assert.Equal(t, FromCache, from2)

	time.Sleep(5 * time.Second)

	var res3 []*testcase.Diamond
	from3, err3 := cc.MGet(ctx, []int64{1, 2, 3}, &res3)
	assert.NoError(t, err3)
	assert.Equal(t, FromCache, from3)
}

func TestAnyCache_withBatchSize(t *testing.T) {
	count := 1
	cc := NewDefault().WithBatchSize(count).
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				v := item.(*testcase.Diamond)
				return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				v := missedItems.([]int64)
				assert.Equal(t, count, len(v))
				return testcase.GetDiamondPtrList(ctx, 1, v)
			})

	ctx := context.TODO()

	var res1 []*testcase.Diamond
	from1, err1 := cc.MGet(ctx, []int64{1, 2, 3, 2}, &res1)
	assert.NoError(t, err1)
	assert.Equal(t, FromLoader, from1)
	for _, res := range res1 {
		assert.NotNil(t, res)
	}
}

func TestAnyCache_WithCacheNil(t *testing.T) {
	testFn := func(cacheNil bool, ResultSource ResultSource) {
		cc := NewDefault().WithCacheNil(cacheNil).
			BuildBatchFetcherBySliceBatchLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("1-%d", item)
				},
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					v := item.(*testcase.Diamond)
					return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
				},
				func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
					return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
				})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(context.TODO(), []int64{100}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)
		assert.Nil(t, res1[0])

		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(context.TODO(), []int64{100}, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, ResultSource, from2)
		assert.Nil(t, res2[0])
	}

	testFn(true, FromCache)
	testFn(false, FromLoader)
}

func TestAnyCache_WithSourceStrategy_multi(t *testing.T) {
	ctx := context.Background()

	t.Run("SsCacheFirst", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsCacheFirst).BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				diamondList, err := testcase.GetDiamondPtrList(ctx, 1, []int64{missedItem.(int64)})
				if err != nil {
					return nil, err
				}

				return diamondList[0], nil
			})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(ctx, []int64{1, 2}, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		var res3 []*testcase.Diamond
		from3, err3 := cc.MGet(ctx, []int64{1, 2, 3}, &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromLoaderPartially, from3)
	})

	t.Run("SsSourceFirst", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsSourceFirst).BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				diamondList, err := testcase.GetDiamondPtrList(ctx, 1, []int64{missedItem.(int64)})
				if err != nil {
					return nil, err
				}

				return diamondList[0], nil
			})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(ctx, []int64{1, 2}, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromLoader, from2)

		var res3 []*testcase.Diamond
		from3, err3 := cc.MGet(context.WithValue(ctx, "bad_source", 1), []int64{1, 2}, &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromCache, from3)

		assert.Equal(t, res2, res3)

		// 回源报错，缓存不全，应该把回源的错误暴露出来
		var res4 []*testcase.Diamond
		from4, err4 := cc.MGet(context.WithValue(ctx, "bad_source", 1), []int64{1, 2, 3}, &res4)
		assert.Error(t, err4)
		assert.Equal(t, "bad_source", err4.Error())
		assert.Equal(t, FromCache, from4)
	})

	t.Run("SsOnlyCache", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsOnlyCache).BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				diamondList, err := testcase.GetDiamondPtrList(ctx, 1, []int64{missedItem.(int64)})
				if err != nil {
					return nil, err
				}

				return diamondList[0], nil
			})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromCache, from1)
		assert.Zero(t, res1[0])
		assert.Zero(t, res1[1])
	})

	t.Run("SsOnlySource", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsOnlySource).BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				diamondList, err := testcase.GetDiamondPtrList(ctx, 1, []int64{missedItem.(int64)})
				if err != nil {
					return nil, err
				}

				return diamondList[0], nil
			})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		// 确保上一步没有写缓存
		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(ctx, []int64{1, 2}, &res2, WithSourceStrategy(SsCacheFirst))
		assert.NoError(t, err2)
		assert.Equal(t, FromLoader, from2)

		var res3 []*testcase.Diamond
		from3, err3 := cc.MGet(ctx, []int64{1, 2}, &res3, WithSourceStrategy(SsCacheFirst))
		assert.NoError(t, err3)
		assert.Equal(t, FromCache, from3)
	})

	t.Run("SsExpiredDataBackup", func(t *testing.T) {
		ttl := time.Second
		cc := NewDefault().WithSourceStrategy(SsExpiredDataBackup).WithTTL(ttl, 2*ttl).
			BuildBatchFetcherByLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("1-%d", item)
				},
				func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
					diamondList, err := testcase.GetDiamondPtrList(ctx, 1, []int64{missedItem.(int64)})
					if err != nil {
						return nil, err
					}

					return diamondList[0], nil
				})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		// 缓存中有数据，并且没有过期
		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(ctx, []int64{1, 2}, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)
		assert.Equal(t, res1, res2)

		time.Sleep(ttl)

		// 缓存已过期，回源失败，使用过期缓存兜底
		var res3 []*testcase.Diamond
		from3, err3 := cc.MGet(context.WithValue(ctx, "bad_source", 1), []int64{1, 2}, &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromExpiredCache, from3)

		time.Sleep(ttl)

		// 缓存硬删除，回源失败，报错
		var res4 []*testcase.Diamond
		from4, err4 := cc.MGet(context.WithValue(ctx, "bad_source", 1), []int64{1, 2}, &res4)
		assert.Error(t, err4)
		assert.Equal(t, FromLoader, from4)
	})

	t.Run("SsExpiredDataAndAsyncSource", func(t *testing.T) {
		ttl := time.Second
		cc := NewDefault().WithSourceStrategy(SsExpiredDataAndAsyncSource).WithTTL(ttl, 2*ttl).
			BuildBatchFetcherByLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("%d-%d", item, extraParam)
				},
				func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
					diamondList, err := testcase.GetDiamondPtrList(ctx, extraParam.(int64), []int64{missedItem.(int64)})
					if err != nil {
						return nil, err
					}

					return diamondList[0], nil
				})

		var liveId int64 = 1
		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1, WithExtraParam(liveId))
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		// 缓存中有数据，并且没有过期
		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(ctx, []int64{1, 2}, &res2, WithExtraParam(liveId))
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)
		assert.Equal(t, res1, res2)

		time.Sleep(ttl)

		// 缓存已过期，返回过期数据 1，异步回源
		var res3 []*testcase.Diamond
		from3, err3 := cc.MGet(ctx, []int64{1, 2}, &res3, WithExtraParam(liveId))
		assert.NoError(t, err3)
		assert.Equal(t, FromExpiredCache, from3)

		// 等待异步刷新
		time.Sleep(time.Millisecond)

		// 回源成功
		var res4 []*testcase.Diamond
		from4, err4 := cc.MGet(ctx, []int64{1, 2}, &res4, WithExtraParam(liveId))
		assert.NoError(t, err4)
		assert.Equal(t, FromCache, from4)
	})
}

func TestAnyCache_WithSourceStrategy(t *testing.T) {
	ctx := context.Background()

	t.Run("SsCacheFirst", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsCacheFirst).BuildFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("type:%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
			},
		)

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cc.Get(ctx, int64(1), &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)
	})

	t.Run("SsSourceFirst", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsSourceFirst).BuildFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("type:%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
			})

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cc.Get(ctx, int64(1), &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromLoader, from2)

		var res3 []testcase.Diamond
		from3, err3 := cc.Get(context.WithValue(ctx, "bad_source", 1), int64(1), &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromCache, from3)
	})

	t.Run("SsOnlyCache", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsOnlyCache).BuildFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("type:%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
			})

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.Error(t, err1)
		assert.Equal(t, FromCache, from1)
	})

	t.Run("SsOnlySource", func(t *testing.T) {
		cc := NewDefault().WithSourceStrategy(SsOnlySource).BuildFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("type:%d", item)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
			})

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		// 确保上一步没有写缓存
		var res2 []testcase.Diamond
		from2, err2 := cc.Get(ctx, int64(1), &res2, WithSourceStrategy(SsCacheFirst))
		assert.NoError(t, err2)
		assert.Equal(t, FromLoader, from2)

		var res3 []testcase.Diamond
		from3, err3 := cc.Get(ctx, int64(1), &res3, WithSourceStrategy(SsCacheFirst))
		assert.NoError(t, err3)
		assert.Equal(t, FromCache, from3)
	})

	t.Run("SsExpiredDataBackup", func(t *testing.T) {
		ttl := time.Second
		cc := NewDefault().WithSourceStrategy(SsExpiredDataBackup).WithTTL(ttl, 2*ttl).
			BuildFetcherByLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("type:%d", item)
				},
				func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
					return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
				})

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		// 缓存中有数据，并且没有过期
		var res2 []testcase.Diamond
		from2, err2 := cc.Get(ctx, int64(1), &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		time.Sleep(ttl)

		// 缓存数据已过期，回源失败，使用过期数据兜底
		var res3 []testcase.Diamond
		from3, err3 := cc.Get(context.WithValue(ctx, "bad_source", 1), int64(1), &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromExpiredCache, from3)

		time.Sleep(ttl)

		// 缓存硬删除，回源失败，报错
		var res4 []testcase.Diamond
		from4, err4 := cc.Get(context.WithValue(ctx, "bad_source", 1), int64(1), &res4)
		assert.Error(t, err4)
		assert.Equal(t, FromLoader, from4)
	})

	t.Run("SsExpiredDataAndAsyncSource", func(t *testing.T) {
		ttl := time.Second
		cc := NewDefault().WithSourceStrategy(SsExpiredDataAndAsyncSource).WithTTL(ttl, 2*ttl).
			BuildFetcherByLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("type:%d", item)
				},
				func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
					return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
				})

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		// 缓存中有数据，并且没有过期
		var res2 []testcase.Diamond
		from2, err2 := cc.Get(ctx, int64(1), &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		time.Sleep(ttl)

		// 缓存数据已过期，返回过期数据，异步回源
		var res3 []testcase.Diamond
		from3, err3 := cc.Get(ctx, int64(1), &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromExpiredCache, from3)

		// 等待异步回源完成
		time.Sleep(100 * time.Millisecond)

		// 缓存数据已刷新
		var res4 []testcase.Diamond
		from4, err4 := cc.Get(ctx, int64(1), &res4)
		assert.NoError(t, err4)
		assert.Equal(t, FromCache, from4)
	})
}

func TestAnyCache_NameSpace(t *testing.T) {
	ctx := context.Background()

	cc := NewDefault().WithNameSpace("default").BuildFetcherByLoader(
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return fmt.Sprintf("type:%d", item)
		},
		func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
		})

	var res1 []testcase.Diamond
	from1, err1 := cc.Get(ctx, int64(1), &res1)
	assert.NoError(t, err1)
	assert.Equal(t, FromLoader, from1)

	var res2 []testcase.Diamond
	from2, err2 := cc.Get(ctx, int64(1), &res2)
	assert.NoError(t, err2)
	assert.Equal(t, FromCache, from2)

	var res3 []testcase.Diamond
	from3, err3 := cc.Get(ctx, int64(1), &res3, withoutNamespace())
	assert.NoError(t, err3)
	assert.Equal(t, FromLoader, from3)

	var res4 []testcase.Diamond
	from4, err4 := cc.Get(ctx, int64(1), &res4, withoutNamespace())
	assert.NoError(t, err4)
	assert.Equal(t, FromCache, from4)
}

func TestAnyCache_WithCacheSampling(t *testing.T) {
	ctx := context.Background()

	cc := NewDefault().WithCacheSampling(0).BuildFetcherByLoader(
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return fmt.Sprintf("type:%d", item)
		},
		func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
		})

	var res1 []testcase.Diamond
	from1, err1 := cc.Get(ctx, int64(1), &res1)
	assert.NoError(t, err1)
	assert.Equal(t, FromLoader, from1)

	var res2 []testcase.Diamond
	from2, err2 := cc.Get(ctx, int64(1), &res2)
	assert.NoError(t, err2)
	assert.Equal(t, FromLoader, from2)
}

func TestAnyCache_WithoutCacheHeader(t *testing.T) {
	storage := cache.MustNewLocalBytesCacheLRU(1)
	cc := New(storage, codec.NewJson(codec.JsonImplStd)).WithCacheNil(false).WithoutCacheHeader().
		BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("1-%d", item)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				v := item.(*testcase.Diamond)
				return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
			})

	ctx := context.TODO()

	var diamondList []*testcase.Diamond
	_, err1 := cc.MGet(ctx, []int64{1, 2, 3}, &diamondList)
	assert.NoError(t, err1)

	e, err := storage.Get(cache.WithoutHeader(ctx), "1-1")
	assert.NoError(t, err)
	data, err := codec.NewJson(codec.JsonImplStd).Marshal(diamondList[0])
	assert.NoError(t, err)
	assert.Equal(t, e.GetVal(), data)
}

func TestAnyCache_WithCacheNilDefaultValue(t *testing.T) {
	testFn := func(s string) {
		storage := cache.MustNewLocalBytesCacheLRU(1)
		ctx := context.TODO()
		cc := New(storage, codec.NewJson(codec.JsonImplStd)).WithCacheNilDefaultValue([]byte(s)).WithoutCacheHeader().
			BuildBatchFetcherBySliceBatchLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("1-%d", item)
				},
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					v := item.(*testcase.Diamond)
					return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
				},
				func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
					return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
				})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{100}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)
		assert.Nil(t, res1[0])

		var res2 []*testcase.Diamond
		from2, err2 := cc.MGet(ctx, []int64{100}, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)
		assert.Nil(t, res2[0])

		e, err := storage.Get(cache.WithoutHeader(ctx), "1-100")
		assert.NoError(t, err)
		assert.Equal(t, []byte(s), e.GetVal())
	}

	testFn("nil-cache")

	assert.Panics(t, func() {
		testFn("")
	})
}

func TestAnyCache_WithExtraParam(t *testing.T) {
	// 调用次数，回源时+1，命中缓存时不+1

	t.Run("fetcher", func(t *testing.T) {
		var simpleFuncCallCount int64

		storage := cache.MustNewLocalBytesCacheLRU(1)

		cache := New(storage, codec.NewJson(codec.JsonImplStd)).WithCacheNil(false).WithoutCacheHeader().
			BuildFetcherByLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("1-%d", item)
				},
				func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
					dbID := 0
					if extraParam != nil {
						dbID = extraParam.(int)
					}
					atomic.AddInt64(&simpleFuncCallCount, 1)
					return "hello " + strconv.Itoa(dbID), nil
				})

		var r1, r2, r3 string

		// 第一次调用，会直接回源
		from1, err1 := cache.Get(context.Background(), "anycache", &r1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)
		assert.Equal(t, simpleFuncCallCount, int64(1))

		// 第二次调用，会命中缓存
		from2, err2 := cache.Get(context.Background(), "anycache", &r2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		//  第三次调用，刷新缓存，进行回源
		err3 := cache.Refresh(context.Background(), "anycache", WithExtraParam(1))
		assert.NoError(t, err3)
		assert.Equal(t, simpleFuncCallCount, int64(2))

		// 再次调用发现缓存被刷新
		from4, err4 := cache.Get(context.Background(), "anycache", &r3)
		assert.NoError(t, err4)
		assert.Equal(t, FromCache, from4)
		assert.Equal(t, simpleFuncCallCount, int64(2))
		assert.Equal(t, r3, "hello 1")

	})

	t.Run("batchfetcher", func(t *testing.T) {
		var batchLoaderCallCount int64
		storage := cache.MustNewLocalBytesCacheLRU(1)
		cache := New(storage, codec.NewJson(codec.JsonImplStd)).WithCacheNil(false).WithoutCacheHeader().BuildBatchFetcherBySliceBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				dbID := 0
				if extraParam != nil {
					dbID = extraParam.(int)
				}
				return fmt.Sprintf("id:%d, db_id:%d", item, dbID)
			},
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprint(item)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				ids := missedItems.([]int)

				dbID := 0
				if extraParam != nil {
					dbID = extraParam.(int)
				}
				atomic.AddInt64(&batchLoaderCallCount, 1)
				var ss []string
				for i := range ids {
					ss = append(ss, fmt.Sprintf("id:%d, db_id:%d", ids[i], dbID))
				}

				return ss, nil
			},
		)

		var r1, r2, r3 []string

		// 第一次调用，会直接回源
		from1, err1 := cache.MGet(context.Background(), []int{1, 2, 3}, &r1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)
		assert.Equal(t, batchLoaderCallCount, int64(1))

		// 第二次调用，会直接读缓存，不回源
		from2, err2 := cache.MGet(context.Background(), []int{1, 2, 3}, &r2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)
		assert.Equal(t, batchLoaderCallCount, int64(1))

		err3 := cache.MRefresh(context.Background(), []int{1, 2, 3}, WithExtraParam(1))
		assert.NoError(t, err3)
		assert.Equal(t, batchLoaderCallCount, int64(2))

		// 再次调用发现缓存被刷新
		from4, err4 := cache.MGet(context.Background(), []int{1, 2, 3}, &r3, WithExtraParam(1))
		assert.NoError(t, err4)
		assert.Equal(t, FromCache, from4)
		assert.Equal(t, batchLoaderCallCount, int64(2))
		assert.Equal(t, r3[0], "id:1, db_id:1")
	})

}

type BadGetCache struct{ cache.Cacher }

func (b *BadGetCache) MGet(ctx context.Context, keys ...string) ([]cache.IEntry, error) {
	return nil, fmt.Errorf("bad mget")
}

func (b *BadGetCache) Get(ctx context.Context, key string) (cache.IEntry, error) {
	return nil, fmt.Errorf("bad get")
}

func TestAnyCache_WithReturnFastWhenCacheErr(t *testing.T) {
	type returnFastCtxKey struct{}
	t.Run("MGet_TestAnyCache_WithReturnFastWhenCacheErr", func(t *testing.T) {
		storage := &BadGetCache{cache.MustNewLocalBytesCacheLRU(1)}
		ctx := context.TODO()
		cc := New(storage, codec.NewJson(codec.JsonImplStd)).
			WithReturnFastWhenCacheErr(func(ctx context.Context, err error) bool {
				v := ctx.Value(returnFastCtxKey{})
				returnFast, _ := strconv.ParseBool(fmt.Sprint(v))
				return returnFast
			}).
			WithSourceStrategy(SsCacheFirst).
			BuildBatchFetcherBySliceBatchLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("1-%d", item)
				},
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					v := item.(*testcase.Diamond)
					return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
				},
				func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
					return testcase.GetDiamondPtrList(ctx, 1, missedItems.([]int64))
				})

		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{100}, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []*testcase.Diamond
		returnFastCtx := context.WithValue(ctx, returnFastCtxKey{}, "true")
		from2, err2 := cc.MGet(returnFastCtx, []int64{100}, &res2)
		assert.NotNil(t, err2)
		assert.Equal(t, FromCache, from2)
	})
	t.Run("Get_TestAnyCache_WithReturnFastWhenCacheErr", func(t *testing.T) {
		storage := &BadGetCache{cache.MustNewLocalBytesCacheLRU(1)}
		ctx := context.TODO()
		cc := New(storage, codec.NewJson(codec.JsonImplStd)).
			WithReturnFastWhenCacheErr(func(ctx context.Context, err error) bool {
				v := ctx.Value(returnFastCtxKey{})
				returnFast, _ := strconv.ParseBool(fmt.Sprint(v))
				return returnFast
			}).
			WithSourceStrategy(SsCacheFirst).
			BuildFetcherByLoader(
				func(ctx context.Context, item interface{}, extraParam interface{}) string {
					return fmt.Sprintf("type:%d", item)
				},
				func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
					return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
				})

		var res1 []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		returnFastCtx := context.WithValue(ctx, returnFastCtxKey{}, "true")
		from2, err2 := cc.Get(returnFastCtx, int64(1), &res2)
		assert.NotNil(t, err2)
		assert.Equal(t, FromCache, from2)
	})

}

func TestAnyCache_WithTTL(t *testing.T) {
	cc := NewDefault().WithTTL(time.Second, time.Second).WithoutLog().BuildFetcherByLoader(
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return fmt.Sprintf("type:%d", item)
		},
		func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			return testcase.GetDiamondByDiamondType(ctx, 1, missedItem.(int64))
		},
	)

	ctx := context.Background()

	t.Run("defaultTTL", func(t *testing.T) {
		var res []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(2), &res)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		time.Sleep(2 * time.Second)

		from2, err2 := cc.Get(ctx, int64(2), &res)
		assert.NoError(t, err2)
		assert.Equal(t, FromLoader, from2)
	})

	t.Run("dynamicTTL", func(t *testing.T) {
		var res []testcase.Diamond
		from1, err1 := cc.Get(ctx, int64(1), &res, WithTTL(3*time.Second, 0))
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		time.Sleep(2 * time.Second)

		from2, err2 := cc.Get(ctx, int64(1), &res)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)
	})
}

func TestAnyCache_WithDeleteNil(t *testing.T) {
	// 先把 key 塞到缓存里
	lc := cache.MustNewLocalBytesCacheLRU(10)
	ctx := context.Background()

	err := lc.Set(ctx, cache.NewEntry("type:2", []byte("[{}]"), 0, 0))
	assert.NoError(t, err)

	_, err = lc.Get(ctx, "type:2")
	assert.NoError(t, err)

	// 不缓存 nil
	cc := New(lc, codec.NewJson(codec.JsonImplIteratorDefault)).WithTTL(time.Second, time.Second).WithoutLog().WithCacheNil(false).BuildFetcherByLoader(
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return fmt.Sprintf("type:%d", item)
		},
		func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			return nil, nil
		},
	)

	// 确认一下从缓存的读到数据
	var res []testcase.Diamond
	from1, err1 := cc.Get(ctx, int64(2), &res)
	assert.NoError(t, err1)
	assert.Equal(t, FromCache, from1)

	// 刷新一下，delete nil cache
	err2 := cc.Refresh(ctx, int64(2), withDeleteNil(true))
	assert.NoError(t, err2)

	// 缓存中的数据不存在了
	_, err = lc.Get(ctx, "type:2")
	assert.Equal(t, cache.ErrNotFound, err)
}
