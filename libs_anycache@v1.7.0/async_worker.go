package anycache

import (
	"context"
	"strings"

	"code.byted.org/webcast/libs_anycache/internal/ctxvalues"
	"code.byted.org/webcast/logw"
)

type asyncRefreshParam struct {
	asyncCtx   context.Context
	ac         *AnyCache
	item       interface{}
	extraParam interface{}
}

func asyncContext(ctx context.Context, ctxKeys []string) context.Context {
	keys := []string{ctxvalues.CtxKeyEnv, ctxvalues.CtxKeyLogID}
	keys = append(keys, ctxKeys...)

	return ctxvalues.CopyContext(ctx, keys...)
}

func convertFromAsyncRefreshParam(v interface{}) (ctx context.Context, ac *AnyCache, item, extraParam interface{}) {
	p := v.(*asyncRefreshParam)
	return p.asyncCtx, p.ac, p.item, p.extraParam
}
func convertToAsyncRefreshParam(ctx context.Context, ac *AnyCache, item, extraParam interface{}) interface{} {
	param := &asyncRefreshParam{
		ac:         ac,
		item:       item,
		extraParam: extraParam,
	}
	if ac.option.ctxCloner == nil {
		param.asyncCtx = asyncContext(ctx, ac.option.ctxKeys)
	} else {
		param.asyncCtx = ac.option.ctxCloner(ctx)
	}
	return param
}

func asyncBatchRefreshWithRet(v interface{}) int {
	asyncCtx, ac, item, extraParam := convertFromAsyncRefreshParam(v)
	ctx, _ := ac.initCtx(asyncCtx, "")
	items := item.([]interface{})

	packedKeys := make([]string, len(items))
	keys := make([]string, len(items))
	for i := range items {
		packedKeys[i] = ac.packKey(ctx, ac.option.batchLoader.genKeyFromParams(ctx, items[i], extraParam))
		keys[i] = ac.option.batchLoader.genKeyFromParams(ctx, items[i], extraParam)
	}
	entries, err := ac.cache.MGet(ctx, packedKeys...)
	if err == nil {
		tmpItems := make([]interface{}, 0, len(items))
		for i := range entries {
			if entries[i] == nil {
				tmpItems = append(tmpItems, items[i])
			}
		}
		if len(tmpItems) == 0 {
			logw.CtxDebug(ctx, "cache is all valid")
			return 0
		}
		items = tmpItems
	}

	resp, err, _ := ac.g.Do("async-batch:"+ac.packKey(asyncCtx, strings.Join(keys, ":")), func() (interface{}, error) {
		val, err := batchMultiFetch(ctx, items, extraParam, ac.option)
		if err != nil && !ac.option.partiallyCache {
			return -1, err
		}
		var logicError error
		if err != nil {
			logicError = err
		}
		if err = ac.mSet(ctx, val); err != nil {
			return -1, err
		}

		return len(val), logicError
	})

	if err != nil {
		logw.CtxWarnKVs(ctx, "batchMultiFetch failed", "err", err)
		return -1
	}

	return resp.(int)
}

func asyncRefreshWithRet(v interface{}) int {
	asyncCtx, ac, item, extraParam := convertFromAsyncRefreshParam(v)
	ctx, _ := ac.initCtx(asyncCtx, "")
	key := ac.option.loader.genKeyFromParams(ctx, item, extraParam)

	_, err := ac.cache.Get(ctx, ac.packKey(ctx, key))
	if err == nil {
		logw.CtxDebugKVs(ctx, "cache is valid", "key", key)
		return 0
	}

	_, err, _ = ac.g.Do("async:"+ac.packKey(ctx, key), func() (interface{}, error) {
		val, err := ac.option.loader.load(ctx, item, extraParam)
		if err != nil {
			return 0, err
		}
		if err := ac.set(ctx, key, val); err != nil {
			return 0, err
		}

		return 1, nil
	})

	if err != nil {
		logw.CtxWarnKVs(ctx, "refresh failed", "err", err)
		return -1
	}

	return 1
}

func (ac *AnyCache) asyncBatchRefresh(ctx context.Context, v interface{}) {
	// 入参ctx无法获得主线程业务ctx，将业务ctx打包进v.(asyncRefreshParam)中
	asyncBatchRefreshWithRet(v)
}

func (ac *AnyCache) asyncRefresh(ctx context.Context, v interface{}) {
	// 入参ctx无法获得主线程业务ctx，将业务ctx打包进v.(asyncRefreshParam)中
	asyncRefreshWithRet(v)
}
