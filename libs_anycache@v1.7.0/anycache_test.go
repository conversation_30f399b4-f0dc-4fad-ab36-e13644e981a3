package anycache

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"sync/atomic"
	"testing"
	"time"
	"unsafe"

	"github.com/stretchr/testify/assert"

	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"code.byted.org/webcast/libs_anycache/testcase"
)

// issue: https://code.byted.org/webcast/libs_anycache/issues/7
func Test_MGet_make_slice_panic(t *testing.T) {
	cacheIns := NewDefault().BuildBatchFetcherBySliceBatchLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondId := item.(int64)
		liveId := extraParam.(int64)
		return fmt.Sprintf("%d-%d", liveId, diamondId)
	}, func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(testcase.Diamond)
		return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
	}, func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
		diamondIdList := missedItems.([]int64)
		liveId := extraParam.(int64)

		return testcase.GetDiamondList(ctx, liveId, diamondIdList)
	})

	sliceData := func(s []testcase.Diamond) uintptr {
		sh := (*reflect.SliceHeader)(unsafe.Pointer(&s))
		return sh.Data
	}

	ctx := context.Background()
	assert.NotPanics(t, func() {
		items := make([]testcase.Diamond, 0, 3)
		oldDataPtr := sliceData(items)
		_, err := cacheIns.MGet(ctx, []int64{1, 2, 3}, &items, WithExtraParam(int64(1)))
		assert.NoError(t, err)
		assert.Equal(t, 3, len(items))

		// 没有做内存分配
		newDataPtr := sliceData(items)
		assert.Equal(t, oldDataPtr, newDataPtr)
	})
	assert.NotPanics(t, func() {
		items := make([]testcase.Diamond, 0, 2)
		oldDataPtr := sliceData(items)
		_, err := cacheIns.MGet(ctx, []int64{1, 2, 3}, &items, WithExtraParam(int64(1)))
		assert.NoError(t, err)
		assert.Equal(t, 3, len(items))

		// 重新分配
		newDataPtr := sliceData(items)
		assert.NotEqual(t, oldDataPtr, newDataPtr)
	})
	assert.NotPanics(t, func() {
		items := make([]testcase.Diamond, 3)
		oldDataPtr := sliceData(items)
		_, err := cacheIns.MGet(ctx, []int64{1, 2, 3}, &items, WithExtraParam(int64(1)))
		assert.NoError(t, err)
		assert.Equal(t, 3, len(items))

		// 没有做内存分配
		newDataPtr := sliceData(items)
		assert.Equal(t, oldDataPtr, newDataPtr)
	})
}

// issue: singlefight 共享结果，导致不同goroutine之间共享对象，有并发问题
func Test_BatchLoader_SingleFlight(t *testing.T) {
	var callCount int64

	cacheIns := NewDefault().BuildBatchFetcherBySliceBatchLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondId := item.(int64)
		liveId := extraParam.(int64)
		return fmt.Sprintf("%d-%d", liveId, diamondId)
	}, func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(testcase.Diamond)
		return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
	}, func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
		atomic.AddInt64(&callCount, 1)

		diamondIdList := missedItems.([]int64)
		liveId := extraParam.(int64)

		return testcase.GetDiamondList(ctx, liveId, diamondIdList)
	})

	ctx := context.Background()
	ctx = context.WithValue(ctx, "latency", time.Second)

	resList := make([][]testcase.Diamond, 10)
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			var items []testcase.Diamond
			_, err := cacheIns.MGet(ctx, []int64{1, 2, 3}, &items, WithExtraParam(int64(1)))
			assert.NoError(t, err)

			resList[i] = items
		}(i)
	}

	wg.Wait()
	assert.Equal(t, int64(1), callCount)

	// 保证地址不同
	for i := range resList {
		j := (i + 1) % len(resList)
		itemsA, itemsB := resList[i], resList[j]
		assert.Equal(t, itemsA, itemsB)
		assert.Equal(t, true, &itemsA != &itemsB)
		for k := range itemsA {
			assert.Equal(t, itemsA[k], itemsB[k])
			assert.Equal(t, true, &itemsA[k] != &itemsB[k])
		}
	}
}

func Test_Loader_SingleFlight(t *testing.T) {
	var callCount int64

	cacheIns := NewDefault().BuildFetcherByLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondType := item.(int64)
		liveID := extraParam.(int64)
		return fmt.Sprintf("%d-%d", liveID, diamondType)
	}, func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
		atomic.AddInt64(&callCount, 1)

		diamondType := missedItem.(int64)
		liveID := extraParam.(int64)
		return testcase.GetDiamondByDiamondType(ctx, liveID, diamondType)
	})

	ctx := context.Background()
	ctx = context.WithValue(ctx, "latency", time.Second)

	resList := make([][]testcase.Diamond, 10)
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()

			var items []testcase.Diamond
			_, err := cacheIns.Get(ctx, int64(1), &items, WithExtraParam(int64(1)))
			assert.NoError(t, err)

			resList[i] = items
		}(i)
	}

	wg.Wait()
	assert.Equal(t, int64(1), callCount)

	// 保证地址不同
	for i := range resList {
		j := (i + 1) % len(resList)
		itemsA, itemsB := resList[i], resList[j]
		assert.Equal(t, itemsA, itemsB)
		assert.Equal(t, true, &itemsA != &itemsB)
		for k := range itemsA {
			assert.Equal(t, itemsA[k], itemsB[k])
			assert.Equal(t, true, &itemsA[k] != &itemsB[k])
		}
	}
}

func Test_BatchLoader_SingleFlight_panic(t *testing.T) {
	var callCount int64

	cacheIns := NewDefault().BuildBatchFetcherBySliceBatchLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondId := item.(int64)
		liveId := extraParam.(int64)
		return fmt.Sprintf("%d-%d", liveId, diamondId)
	}, func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(testcase.Diamond)
		return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
	}, func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
		atomic.AddInt64(&callCount, 1)

		time.Sleep(time.Second)
		panic("panic ...")
	})

	ctx := context.Background()

	resList := make([][]testcase.Diamond, 10)
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(i int) {
			defer func() {
				wg.Done()
				if e := recover(); e != nil {
					t.Log(e)
				}
			}()
			var items []testcase.Diamond
			_, err := cacheIns.MGet(ctx, []int64{1, 2, 3}, &items, WithExtraParam(int64(1)))
			assert.Equal(t, err, ErrPanic)

			resList[i] = items
		}(i)
	}

	assert.False(t, waitTimeout(&wg, 3*time.Second))
	assert.Equal(t, int64(1), callCount)
}

func Test_Loader_SingleFlight_panic(t *testing.T) {
	var callCount int64

	cacheIns := NewDefault().BuildFetcherByLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondType := item.(int64)
		liveID := extraParam.(int64)
		return fmt.Sprintf("%d-%d", liveID, diamondType)
	}, func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
		atomic.AddInt64(&callCount, 1)
		time.Sleep(time.Second)
		panic("panic ...")
	})

	ctx := context.Background()

	resList := make([][]testcase.Diamond, 10)
	var wg sync.WaitGroup
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(i int) {
			defer func() {
				wg.Done()
				if e := recover(); e != nil {
					t.Log(e)
				}
			}()

			var items []testcase.Diamond
			_, err := cacheIns.Get(ctx, int64(1), &items, WithExtraParam(int64(1)))
			assert.Equal(t, err, ErrPanic)

			resList[i] = items
		}(i)
	}

	assert.False(t, waitTimeout(&wg, 3*time.Second))
	assert.Equal(t, int64(1), callCount)
}

func waitTimeout(wg *sync.WaitGroup, timeout time.Duration) bool {
	c := make(chan struct{})
	go func() {
		defer close(c)
		wg.Wait()
	}()
	select {
	case <-c:
		return false // completed normally
	case <-time.After(timeout):
		return true // timed out
	}
}

func TestAnyCache_BuildFetcherByLoader(t *testing.T) {
	type itemType struct {
		liveId      int64
		diamondType int64
	}

	cacheIns := NewDefault().BuildFetcherByLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(itemType)
		return fmt.Sprintf("%d-%d", v.liveId, v.diamondType)
	}, func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
		v := missedItem.(itemType)
		return testcase.GetDiamondByDiamondType(ctx, v.liveId, v.diamondType)
	})

	ctx := context.Background()
	t.Run("Get", func(t *testing.T) {
		item := itemType{
			liveId:      1,
			diamondType: 1,
		}

		result1 := new([]testcase.Diamond)
		from1, err1 := cacheIns.Get(ctx, item, result1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		result2 := new([]testcase.Diamond)
		from2, err2 := cacheIns.Get(ctx, item, result2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, result1, result2)
	})

	t.Run("Set", func(t *testing.T) {
		item := itemType{
			liveId:      1,
			diamondType: 2,
		}

		err := cacheIns.Set(ctx, item, nil)
		assert.NoError(t, err)

		result1 := new([]testcase.Diamond)
		from1, err1 := cacheIns.Get(ctx, item, result1)
		assert.Error(t, err1, ErrNil)
		assert.Equal(t, FromCache, from1)
	})

	t.Run("Del", func(t *testing.T) {
		item := itemType{
			liveId:      1,
			diamondType: 3,
		}

		result1 := new([]testcase.Diamond)
		from1, err1 := cacheIns.Get(ctx, item, result1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		err := cacheIns.Del(ctx, item)
		assert.NoError(t, err)

		result2 := new([]testcase.Diamond)
		from2, err2 := cacheIns.Get(ctx, item, result2)
		assert.NoError(t, err2)
		assert.Equal(t, FromLoader, from2)
	})

	t.Run("Refresh", func(t *testing.T) {
		item := itemType{
			liveId:      1,
			diamondType: 4,
		}

		result1 := new([]testcase.Diamond)
		from1, err1 := cacheIns.Get(ctx, item, result1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		result2 := new([]testcase.Diamond)
		from2, err2 := cacheIns.Get(ctx, item, result2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, result1, result2)

		err := cacheIns.Refresh(ctx, item)
		assert.NoError(t, err)

		result3 := new([]testcase.Diamond)
		from3, err3 := cacheIns.Get(ctx, item, result3)
		assert.NoError(t, err3)
		assert.Equal(t, FromCache, from3)
		assert.NotEqual(t, result2, result3)

		result4, err4 := cacheIns.Refresh2(ctx, item)
		assert.NoError(t, err4)
		assert.NotEqual(t, result4, result3)
	})
}

func TestAnyCache_BuildBatchFetcherByBatchLoader(t *testing.T) {
	type itemType struct {
		liveId    int64
		diamondId int64
	}

	cacheIns := NewDefault().BuildBatchFetcherByBatchLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(itemType)
		return fmt.Sprintf("%d-%d", v.liveId, v.diamondId)
	}, func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
		v := missedItems.([]itemType)
		var diamondIdList []int64
		for _, item := range v {
			diamondIdList = append(diamondIdList, item.diamondId)
		}
		diamondList, err := testcase.GetDiamondList(ctx, v[0].liveId, diamondIdList)
		if err != nil {
			return nil, err
		}

		ret := make(map[string]testcase.Diamond, len(diamondList))
		for i, d := range diamondList {
			key := fmt.Sprintf("%d-%d", d.LiveId, d.Id)
			ret[key] = diamondList[i]
		}
		return ret, nil
	})

	ctx := context.Background()
	t.Run("MGet", func(t *testing.T) {
		inItems := []itemType{
			{1, 1},
			{1, 2},
		}

		var res1 []testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cacheIns.MGet(ctx, inItems, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, res1, res2)

		var res3 []testcase.Diamond
		from3, err3 := cacheIns.MGet(ctx, append(inItems, itemType{1, 3}), &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromLoaderPartially, from3)
	})

	t.Run("MSet", func(t *testing.T) {
		inItems := []itemType{
			{2, 1},
			{2, 2},
		}

		err1 := cacheIns.MSet(ctx, inItems, []*testcase.Diamond{nil, nil})
		assert.NoError(t, err1)

		var res1 []*testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromCache, from1)
		assert.Zero(t, res1[0])
		assert.Zero(t, res1[1])
	})

	t.Run("MDel", func(t *testing.T) {
		inItems := []itemType{
			{3, 1},
			{3, 2},
		}
		inItems3 := append(inItems, itemType{3, 3})

		var res1 []testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems3, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cacheIns.MGet(ctx, inItems3, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, res1, res2)

		err3 := cacheIns.MDel(ctx, inItems)
		assert.NoError(t, err3)

		var res4 []testcase.Diamond
		from4, err4 := cacheIns.MGet(ctx, inItems3, &res4)
		assert.NoError(t, err4)
		assert.Equal(t, FromLoaderPartially, from4)
	})

	t.Run("MRefresh", func(t *testing.T) {
		inItems := []itemType{
			{4, 1},
			{4, 2},
		}

		var res1 []testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cacheIns.MGet(ctx, inItems, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, res1, res2)

		err3 := cacheIns.MRefresh(ctx, inItems)
		assert.NoError(t, err3)

		var res4 []testcase.Diamond
		from4, err4 := cacheIns.MGet(ctx, inItems, &res4)
		assert.NoError(t, err4)
		assert.Equal(t, FromCache, from4)

		assert.NotEqual(t, res2, res4)
	})
}

func TestAnyCache_BuildBatchFetcherByLoader(t *testing.T) {
	type itemType struct {
		liveId    int64
		diamondId int64
	}

	cacheIns := NewDefault().BuildBatchFetcherByLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(itemType)
		return fmt.Sprintf("%d-%d", v.liveId, v.diamondId)
	}, func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
		v := missedItem.(itemType)
		diamondList, err := testcase.GetDiamondList(ctx, v.liveId, []int64{v.diamondId})
		if err != nil {
			return nil, err
		}
		return diamondList[0], nil
	})

	ctx := context.Background()
	t.Run("MGet", func(t *testing.T) {
		inItems := []itemType{
			{1, 1},
			{1, 2},
		}

		var res1 []testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cacheIns.MGet(ctx, inItems, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, res1, res2)

		var res3 []testcase.Diamond
		from3, err3 := cacheIns.MGet(ctx, append(inItems, itemType{1, 3}), &res3)
		assert.NoError(t, err3)
		assert.Equal(t, FromLoaderPartially, from3)
	})

	t.Run("MSet", func(t *testing.T) {
		inItems := []itemType{
			{2, 1},
			{2, 2},
		}

		err1 := cacheIns.MSet(ctx, inItems, []*testcase.Diamond{nil, nil})
		assert.NoError(t, err1)

		var res1 []*testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromCache, from1)
		assert.Zero(t, res1[0])
		assert.Zero(t, res1[1])
	})

	t.Run("MDel", func(t *testing.T) {
		inItems := []itemType{
			{3, 1},
			{3, 2},
		}
		inItems3 := append(inItems, itemType{3, 3})

		var res1 []testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems3, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cacheIns.MGet(ctx, inItems3, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, res1, res2)

		err3 := cacheIns.MDel(ctx, inItems)
		assert.NoError(t, err3)

		var res4 []testcase.Diamond
		from4, err4 := cacheIns.MGet(ctx, inItems3, &res4)
		assert.NoError(t, err4)
		assert.Equal(t, FromLoaderPartially, from4)
	})

	t.Run("MRefresh", func(t *testing.T) {
		inItems := []itemType{
			{4, 1},
			{4, 2},
		}

		var res1 []testcase.Diamond
		from1, err1 := cacheIns.MGet(ctx, inItems, &res1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		var res2 []testcase.Diamond
		from2, err2 := cacheIns.MGet(ctx, inItems, &res2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, res1, res2)

		err3 := cacheIns.MRefresh(ctx, inItems)
		assert.NoError(t, err3)

		var res4 []testcase.Diamond
		from4, err4 := cacheIns.MGet(ctx, inItems, &res4)
		assert.NoError(t, err4)
		assert.Equal(t, FromCache, from4)

		assert.NotEqual(t, res2, res4)
	})
}

func Test_BatchLoader_Msgp(t *testing.T) {
	cacheIns := New(cache.MustNewLocalBytesCacheLRU(100), codec.NewMsgpack1()).BuildBatchFetcherByBatchLoader(
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return fmt.Sprintf("key-%d", item)
		},
		func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			m := make(map[string]*testcase.DataStruct)

			for _, item := range missedItem.([]int) {
				key := fmt.Sprintf("key-%d", item)
				m[key] = testcase.DataInStruct
			}

			return m, nil
		},
	)

	ctx := context.Background()

	{
		var v1, v2 []testcase.DataStruct
		from1, err1 := cacheIns.MGet(ctx, []int{1, 2, 3}, &v1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		from2, err2 := cacheIns.MGet(ctx, []int{1, 2, 3}, &v2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, v1, v2)

		err := cacheIns.MDel(ctx, []int{1, 2, 3})
		assert.NoError(t, err)
	}

	{
		var v1, v2 []*testcase.DataStruct
		from1, err1 := cacheIns.MGet(ctx, []int{1, 2, 3}, &v1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		from2, err2 := cacheIns.MGet(ctx, []int{1, 2, 3}, &v2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, v1, v2)

		err := cacheIns.MDel(ctx, []int{1, 2, 3})
		assert.NoError(t, err)
	}

	{
		var v1, v2 []**testcase.DataStruct
		from1, err1 := cacheIns.MGet(ctx, []int{1, 2, 3}, &v1)
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)

		from2, err2 := cacheIns.MGet(ctx, []int{1, 2, 3}, &v2)
		assert.NoError(t, err2)
		assert.Equal(t, FromCache, from2)

		assert.Equal(t, v1, v2)

		err := cacheIns.MDel(ctx, []int{1, 2, 3})
		assert.NoError(t, err)
	}
}

// 暂时不对外提供此功能

// func TestAnyCache_buildForWarmUpFunc(t *testing.T) {
//	type itemType struct {
//		liveId, diamondId int64
//	}
//	cache, err := NewDefault().buildForWarmUpFuncRetSlice(
//		func(ctx context.Context, item interface{}, extraParam interface{}) string {
//			v := item.(itemType)
//			return fmt.Sprintf("%d-%d", v.liveId, v.diamondId)
//		},
//		func(ctx context.Context, item interface{}, extraParam interface{}) string {
//			v := item.(testcase.Diamond)
//			return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
//		},
//		func(ctx context.Context) pageWarmUpLoad {
//			return func(ctx context.Context) (interface{}, bool, error) {
//				diamondList, err := testcase.GetDiamondList(ctx, 1, []int64{1, 2, 3})
//				if err != nil {
//					return nil, false, err
//				}
//				return diamondList, false, nil
//			}
//		}, "all_diamond_of_aweme", time.Second)
//
//	assert.NoError(t, err)
//
//	var res1 []testcase.Diamond
//	from1, err1 := cache.MGet(context.Background(), []itemType{{1, 1}}, &res1)
//	assert.NoError(t, err1)
//	assert.Equal(t, FromCache, from1)
//	assert.NotZero(t, res1[0])
//
//	// 等待 warmUp 自动刷新
//	time.Sleep(2 * time.Second)
//
//	var res2 []testcase.Diamond
//	from2, err2 := cache.MGet(context.Background(), []itemType{{1, 1}}, &res2)
//	assert.NoError(t, err2)
//	assert.Equal(t, FromCache, from2)
//	assert.NotZero(t, res2[0])
//
//	assert.NotEqual(t, res1[0], res2[0])
// }

func Test_SsExpiredDataAndAsyncSource_DynamicOption(t *testing.T) {
	loadNum := 0
	listLocalCache := NewDefault().
		WithTTL(time.Second, 2*time.Second).
		WithSourceStrategy(SsExpiredDataAndAsyncSource).
		BuildFetcherByLoader(
			// parse cache key by request
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return "test_anycache"
			},
			// load data from source
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				loadNum++
				return "cache data", nil
			},
		)
	ctx := context.TODO()
	err := listLocalCache.Set(ctx, "test", "cache data")
	assert.NoError(t, err)
	assert.Equal(t, 0, loadNum)
	time.Sleep(time.Second)
	var resp string
	res, err := listLocalCache.Get(ctx, "test", &resp, WithTTL(2*time.Second, 5*time.Second))
	assert.NoError(t, err)
	assert.Equal(t, FromExpiredCache, res)
	time.Sleep(100 * time.Millisecond)
	assert.Equal(t, 1, loadNum)
	time.Sleep(3 * time.Second)
	res, err = listLocalCache.Get(ctx, "test", &resp, WithTTL(2*time.Second, 5*time.Second))
	assert.NoError(t, err)
	assert.Equal(t, FromExpiredCache, res)
	time.Sleep(500 * time.Millisecond)
	assert.Equal(t, 2, loadNum)
	res, err = listLocalCache.Get(ctx, "test", &resp, WithTTL(2*time.Second, 5*time.Second))
	assert.NoError(t, err)
	assert.Equal(t, 2, loadNum)
	assert.Equal(t, FromCache, res)
	time.Sleep(5 * time.Second)
	res, err = listLocalCache.Get(ctx, "test", &resp, WithTTL(2*time.Second, 5*time.Second))
	assert.NoError(t, err)
	assert.Equal(t, 3, loadNum)
	assert.Equal(t, FromLoader, res)
}
