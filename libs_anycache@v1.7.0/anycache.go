// Package anycache 提供了一种方便的缓存使用方式或者最佳实践。
package anycache

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"

	"golang.org/x/sync/singleflight"

	"code.byted.org/webcast/libs_sync/batch"
	"code.byted.org/webcast/logw"

	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"code.byted.org/webcast/libs_anycache/stat"

	"code.byted.org/webcast/libs_anycache/plugin/cache/objectcache"
)

// AnyCache 由缓存组件、序列化组件、异步组件等组成，支持 DB 缓存、函数缓存
type AnyCache struct {
	cache cache.Cacher
	codec codec.Codecer
	g     *singleflight.Group

	asyncSaveCacheChan chan asyncSaveCacheChanElem
	asyncsaverStopCh   chan struct{}
	asyncSaverLock     sync.Mutex

	option *option
}

const (
	chanLen        = 100
	goroutineCount = 10
	batchConsume   = 10

	asyncWorkChanLen = 20
)

type asyncSaveCacheChanElem struct {
	entries []cache.IEntry
	ctx     context.Context
}

// Deprecated: Use New instead（当使用WithAsyncSaveCache启用异步保存缓存时，会自动开启异步协程任务）
func NewSyncSave(cacheIns cache.Cacher, codecIns codec.Codecer) *AnyCache {
	any := &AnyCache{
		cache:  cacheIns,
		codec:  codecIns,
		g:      &singleflight.Group{},
		option: getDefaultOption(),
	}

	return any
}

// New 返回 AnyCache 实例
func New(cacheIns cache.Cacher, codecIns codec.Codecer) *AnyCache {
	any := &AnyCache{
		cache:  cacheIns,
		codec:  codecIns,
		g:      &singleflight.Group{},
		option: getDefaultOption(),
	}

	return any
}

// Deprecated: use NewDefaultBytesCache instead
func NewDefault() *AnyCache {
	return New(cache.MustNewLocalBytesCacheLRU(256), codec.NewJson(codec.JsonImplIteratorDefault))
}

func NewDefaultBytesCache() *AnyCache {
	return New(cache.MustNewLocalBytesCacheLRU(256), codec.NewJson(codec.JsonImplIteratorDefault))
}

func NewDefaultObjectCache() *AnyCache {
	return New(objectcache.MustNewLocalObjectCacheLRU(10240), codec.NewBad()).WithObjectCache()
}

// Clear 清理所有缓存(仅可用于 LocalBytesCache)
func (ac *AnyCache) Clear() error {
	if lc, ok := ac.cache.(*cache.LocalBytesCache); ok {
		return lc.Clear()
	}
	logw.Errorf(fmt.Sprintf("cache type %T does not support Clear()", ac.cache))
	return nil
}

func (ac *AnyCache) stopAsyncSavers() {
	ac.asyncSaverLock.Lock()
	defer ac.asyncSaverLock.Unlock()

	if ac.asyncSaveCacheChan == nil {
		logw.Info("anycache: ignore stop async saver calls, asyncSaveCacheChan is nil")
		return
	}

	if ac.asyncsaverStopCh == nil {
		logw.Info("anycache: ignore stop async saver calls, asyncsaverStopCh is nil")
		ac.asyncSaveCacheChan = nil
		return
	}
	close(ac.asyncsaverStopCh)

	ac.asyncSaveCacheChan = nil
	ac.asyncsaverStopCh = nil
}

func (ac *AnyCache) startAsyncSavers() {
	ac.asyncSaverLock.Lock()
	defer ac.asyncSaverLock.Unlock()

	if ac.asyncSaveCacheChan != nil {
		logw.Info("anycache: ignore start async saver calls")
		return
	}

	ac.asyncSaveCacheChan = make(chan asyncSaveCacheChanElem, chanLen)
	ac.asyncsaverStopCh = make(chan struct{})

	for i := 0; i < goroutineCount; i++ {
		go func() {
			ticket := time.NewTicker(time.Second)
			defer ticket.Stop()
			for {
				ctx := context.TODO()
				entries := make([]cache.IEntry, 0, batchConsume)
				// after := time.NewTimer(time.Second)

			loop:
				for {
					select {
					case <-ac.asyncsaverStopCh:
						return

					case elem := <-ac.asyncSaveCacheChan:
						ctx = elem.ctx
						entries = append(entries, elem.entries...)
						if len(entries) >= batchConsume {
							break loop
						}
					// case <-after.C:
					// 	break loop
					case <-ticket.C:
						break loop
					}
				}
				// NOTICE
				// after.Stop()

				if len(entries) > 0 {
					// fmt.Println("asynccache", len(entries))
					// FIXME ctx 乱了
					err := ac.cache.MSet(ctx, entries...)
					if err != nil {
						logw.Warn("anycache: async save cachee failed, err: %v", err)
					}
				}
			}
		}()
	}
}

func (ac *AnyCache) clone() *AnyCache {
	c := AnyCache{
		cache:              ac.cache,
		codec:              ac.codec,
		g:                  ac.g,
		asyncSaveCacheChan: ac.asyncSaveCacheChan,
		asyncsaverStopCh:   ac.asyncsaverStopCh,
		option:             ac.option.clone(),
	}
	return &c
}

// -- build start ---

type Fetcher interface {
	Get(ctx context.Context, inItem, v interface{}, options ...DynamicOption) (ResultSource, error)
	Set(ctx context.Context, inItem, val interface{}, options ...DynamicOption) error
	Del(ctx context.Context, inItem interface{}, options ...DynamicOption) error
	Refresh(ctx context.Context, inItem interface{}, options ...DynamicOption) error
	Refresh2(ctx context.Context, inItem interface{}, options ...DynamicOption) (interface{}, error)
}

type BatchFetcher interface {
	MGet(ctx context.Context, inItems, v interface{}, options ...DynamicOption) (ResultSource, error)
	MSet(ctx context.Context, inItems, valList interface{}, options ...DynamicOption) error
	MDel(ctx context.Context, inItems interface{}, options ...DynamicOption) error
	MRefresh(ctx context.Context, inItems interface{}, options ...DynamicOption) error
}

// type warmUpFetcher interface {
// 	warmUp(name string, asyncRefreshRate time.Duration) error
// 	MGet(ctx context.Context, inItems, v interface{}, options ...DynamicOption) (ResultSource, error)
// }

// func (ac *AnyCache) buildForWarmUpFetcher(warmUpLoader warmUpLoader) warmUpFetcher {
// 	c := ac.clone()
// 	c.option.warmUpLoader = warmUpLoader
// 	c.option.batchLoader = warmUpLoader
// 	c.option.sourceStrategy = SsOnlyCache
//
// 	return c
// }

// func (ac *AnyCache) buildFetcherByLoader(genKeyFromParamsFn genKeyFunc, simpleFn loaderFunc) Fetcher {
// 	loader := loaderFuncWrap(genKeyFromParamsFn, simpleFn)
// 	c := ac.clone()
// 	c.option.loader = loader
// 	return c
// }

func (ac *AnyCache) BuildFetcherByLoader(genKeyFromParamsFn genKeyFunc, loaderFn loaderFunc) Fetcher {
	loader := loaderFuncWrap(genKeyFromParamsFn, loaderFn)
	c := ac.clone()
	c.option.loader = loader
	c.afterBuild()
	return c
}

func (ac *AnyCache) BuildBatchFetcherByLoader(genKeyFromParamsFn genKeyFunc, loaderFn loaderFunc) BatchFetcher {
	batchLoader := loaderFuncBatchWrap(genKeyFromParamsFn, loaderFn)
	c := ac.clone()
	c.option.batchLoader = batchLoader
	c.afterBuild()
	return c
}

func (ac *AnyCache) BuildBatchFetcherBySliceBatchLoader(genKeyFromParamsFn, GenKeyFromResultFn genKeyFunc, multiFn batchLoaderFunc) BatchFetcher {
	return ac.buildBatchFetcherByBatchLoader(genKeyFromParamsFn, GenKeyFromResultFn, multiFn)
}

func (ac *AnyCache) BuildBatchFetcherByBatchLoader(genKeyFromParamsFn genKeyFunc, multiFn batchLoaderFunc) BatchFetcher {
	return ac.buildBatchFetcherByBatchLoader(genKeyFromParamsFn, nil, multiFn)
}

func (ac *AnyCache) buildBatchFetcherByBatchLoader(genKeyFromParamsFn, GenKeyFromResultFn genKeyFunc, multiFn batchLoaderFunc) BatchFetcher {
	batchLoader := batchLoaderFuncWrap(genKeyFromParamsFn, GenKeyFromResultFn, multiFn)
	c := ac.clone()
	c.option.batchLoader = batchLoader
	c.afterBuild()
	return c
}

func (ac *AnyCache) afterBuild() {
	// if ac.option.smartKeyOption != nil {
	// 	keySyncIns := key_sync.New(ac.option.smartKeyOption.group)
	//
	// 	hotKeyWorker := batch.NewAsyncWorker(context.TODO(), asyncWorkChanLen, ac.asyncHotKeyHandler)
	//
	// 	expireKeyWorker := batch.NewAsyncWorker(context.TODO(), asyncWorkChanLen, ac.asyncExpiredKeyHandler)
	//
	// 	go func() {
	// 		for k := range keySyncIns.HotKeyChan() {
	// 			hotKeyWorker.Add(k)
	// 		}
	// 	}()
	//
	// 	go func() {
	// 		for k := range keySyncIns.ExpireKeyChan() {
	// 			expireKeyWorker.Add(k)
	// 		}
	// 	}()
	//
	// 	ac.option.smartKeySync = keySyncIns
	// }

	if ac.option.sourceStrategy == SsExpiredDataAndAsyncSource {
		if ac.option.batchLoader != nil {
			batchRefreshWorker := batch.NewAsyncWorker(context.TODO(), asyncWorkChanLen, ac.asyncBatchRefresh)

			ac.option.batchRefreshWorker = batchRefreshWorker
		}

		if ac.option.loader != nil {
			refreshWorker := batch.NewAsyncWorker(context.TODO(), asyncWorkChanLen, ac.asyncRefresh)

			ac.option.refreshWorker = refreshWorker
		}
	}
}

// -- build end ---

// func (ac *AnyCache) buildForWarmUpFuncRetSlice(genKeyFromParamsFn, GenKeyFromResultFn genKeyFunc, warnUpFn warmUpFunc, name string, asyncRefreshRate time.Duration) (warmUpFetcher, error) {
// 	return ac.buildForWarmUpFunc(genKeyFromParamsFn, GenKeyFromResultFn, warnUpFn, name, asyncRefreshRate)
// }

// func (ac *AnyCache) buildForWarmUpFuncRetMap(genKeyFromParamsFn genKeyFunc, warnUpFn warmUpFunc, name string, asyncRefreshRate time.Duration) (warmUpFetcher, error) {
// 	return ac.buildForWarmUpFunc(genKeyFromParamsFn, nil, warnUpFn, name, asyncRefreshRate)
// }

// func (ac *AnyCache) buildForWarmUpFunc(genKeyFromParamsFn, GenKeyFromResultFn genKeyFunc, warnUpFn warmUpFunc, name string, asyncRefreshRate time.Duration) (warmUpFetcher, error) {
// 	warmUpFetcher := warmUpFuncWrap(genKeyFromParamsFn, GenKeyFromResultFn, warnUpFn)
//
// 	derivedWarmUpLoader := ac.buildForWarmUpFetcher(warmUpFetcher)
// 	err := derivedWarmUpLoader.warmUp(name, asyncRefreshRate)
// 	if err != nil {
// 		return nil, err
// 	}
//
// 	return derivedWarmUpLoader, nil
// }

// -- WarmUpLoader start ---
// func (ac *AnyCache) warmUp(name string, asyncRefreshRate time.Duration) error {
// 	ctx, fn := ac.initCtx(context.Background(), "WarmUp")
// 	defer fn()
//
// 	refresh := func() error {
// 		pageWarmUpLoad := ac.option.warmUpLoader.warmUpLoad(ctx)
// 		var (
// 			res  interface{}
// 			more = true
// 			err  error
// 		)
// 		for more {
// 			res, more, err = pageWarmUpLoad(ctx)
// 			if err != nil {
// 				return err
// 			}
//
// 			// FIXME 不能缓存 nil
// 			resMap := make(map[string]interface{})
// 			lock := &sync.Mutex{}
// 			fillRes(ctx, lock, res, resMap, ac.option.warmUpLoader.genKeyFromResults, ac.option.extraParam)
//
// 			var entries []cache.IEntry
// 			for k, v := range resMap {
// 				entry, err := ac.newCacheEntry(k, v)
// 				if err != nil {
// 					return err
// 				}
// 				entries = append(entries, entry)
// 			}
// 			if err := ac.cache.MSet(ctx, entries...); err != nil {
// 				return err
// 			}
// 		}
//
// 		return nil
// 	}
//
// 	if err := refresh(); err != nil {
// 		return err
// 	}
//
// 	// 异步刷新
// 	if asyncRefreshRate > 0 {
// 		return ac.worker.Submit(name, task.NewTask(asyncRefreshRate, func() {
// 			_ = refresh()
// 		}))
// 	}
//
// 	return nil
// }

// -- WarmUpLoader end ---

// -- Fetcher start ---
func (ac *AnyCache) Get(ctx context.Context, inItem, v interface{}, options ...DynamicOption) (ResultSource, error) {
	acc := ac.applyDynamicOption(options...)

	ctx, fn := acc.initCtx(ctx, "Get")
	defer fn()

	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return 0, errors.New("v must be a valid pointer")
	}
	from, err := acc.get(ctx, inItem, rv)
	return from, err
}

func (ac *AnyCache) Set(ctx context.Context, inItem, val interface{}, options ...DynamicOption) error {
	acc := ac.applyDynamicOption(options...)

	ctx, fn := acc.initCtx(ctx, "Set")
	defer fn()

	key := ac.option.loader.genKeyFromParams(ctx, inItem, acc.option.extraParam)
	// if ac.option.smartKeySync != nil {
	// 	ac.option.smartKeySync.Expire(ac.packKey(ctx, key))
	// }
	return acc.set(ctx, key, val)
}

func (ac *AnyCache) Del(ctx context.Context, inItem interface{}, options ...DynamicOption) error {
	acc := ac.applyDynamicOption(options...)

	ctx, fn := acc.initCtx(ctx, "Del")
	defer fn()

	key := acc.option.loader.genKeyFromParams(ctx, inItem, acc.option.extraParam)
	stat.WithInfo(ctx, stat.InfoKey, key)

	packedKey := ac.packKey(ctx, key)
	// if ac.option.smartKeySync != nil {
	// 	ac.option.smartKeySync.Expire(packedKey)
	// }
	return acc.cache.MDel(ctx, packedKey)
}

func (ac *AnyCache) Refresh(ctx context.Context, inItem interface{}, options ...DynamicOption) error {
	acc := ac.applyDynamicOption(options...)
	ctx, fn := acc.initCtx(ctx, "Refresh")
	defer fn()

	// if ac.option.smartKeySync != nil {
	// 	key := acc.option.loader.genKeyFromParams(ctx, inItem, acc.option.extraParam)
	// 	ac.option.smartKeySync.Expire(ac.packKey(ctx, key))
	// }

	_, err := acc.refresh(ctx, inItem)
	return err
}

func (ac *AnyCache) Refresh2(ctx context.Context, inItem interface{}, options ...DynamicOption) (interface{}, error) {
	acc := ac.applyDynamicOption(options...)
	ctx, fn := acc.initCtx(ctx, "Refresh")
	defer fn()

	// if ac.option.smartKeySync != nil {
	// 	key := acc.option.loader.genKeyFromParams(ctx, inItem, acc.option.extraParam)
	// 	ac.option.smartKeySync.Expire(ac.packKey(ctx, key))
	// }

	return acc.refresh(ctx, inItem)
}

// -- Fetcher end ---

// -- BatchLoader start ---
func (ac *AnyCache) MGet(ctx context.Context, inItems, v interface{}, options ...DynamicOption) (ResultSource, error) {
	acc := ac.applyDynamicOption(options...)

	ctx, fn := acc.initCtx(ctx, "MGet")
	defer fn()

	// 检测 target 是否满足要求
	rv := reflect.ValueOf(v) // *slice
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return 0, errors.New("v must be a valid pointer")
	}
	// slice
	if rv.Elem().Kind() != reflect.Slice {
		return 0, errors.New("v should be a slice")
	}
	from, err := acc.mGet(ctx, inItems, rv)
	return from, err
}

func (ac *AnyCache) MSet(ctx context.Context, inItems, v interface{}, options ...DynamicOption) error {
	acc := ac.applyDynamicOption(options...)

	ctx, fn := acc.initCtx(ctx, "MSet")
	defer fn()

	itemList := interface2SliceReflect(inItems)
	if len(itemList) == 0 {
		return nil
	}
	valList := interface2SliceReflect(v)
	if len(itemList) != len(valList) {
		panic("kv count not equal")
	}

	keyValMap := make(map[string]interface{})
	for i, item := range itemList {
		key := acc.option.batchLoader.genKeyFromParams(ctx, item, acc.option.extraParam)
		keyValMap[key] = valList[i]
	}

	// if ac.option.smartKeySync != nil {
	// 	for key := range keyValMap {
	// 		ac.option.smartKeySync.Expire(ac.packKey(ctx, key))
	// 	}
	// }

	return acc.mSet(ctx, keyValMap)
}

func (ac *AnyCache) MDel(ctx context.Context, inItems interface{}, options ...DynamicOption) error {
	acc := ac.applyDynamicOption(options...)

	ctx, fn := acc.initCtx(ctx, "MDel")
	defer fn()

	itemList := interface2SliceReflect(inItems)
	keys := make([]string, len(itemList))
	var packedKeys []string
	for i, item := range itemList {
		key := acc.packKey(ctx, acc.option.batchLoader.genKeyFromParams(ctx, item, acc.option.extraParam))
		packedKeys = append(packedKeys, key)
		keys[i] = key
	}

	stat.WithInfo(ctx, stat.InfoKey, strings.Join(keys, ", "))

	// if ac.option.smartKeySync != nil {
	// 	for _, key := range packedKeys {
	// 		ac.option.smartKeySync.Expire(key)
	// 	}
	// }

	return acc.cache.MDel(ctx, packedKeys...)
}

func (ac *AnyCache) MRefresh(ctx context.Context, inItems interface{}, options ...DynamicOption) error {
	acc := ac.applyDynamicOption(options...)
	ctx, fn := acc.initCtx(ctx, "MRefresh")
	defer fn()

	itemList := interface2SliceReflect(inItems)
	if len(itemList) == 0 {
		return nil
	}

	// if ac.option.smartKeySync != nil {
	// 	var packedKeys []string
	// 	for _, item := range itemList {
	// 		packedKeys = append(packedKeys, acc.packKey(ctx, acc.option.batchLoader.genKeyFromParams(ctx, item, acc.option.extraParam)))
	// 	}
	//
	// 	for _, key := range packedKeys {
	// 		ac.option.smartKeySync.Expire(key)
	// 	}
	// }

	return acc.mRefresh(ctx, itemList)
}

// -- BatchLoader end ---

func (ac *AnyCache) initCtx(ctx context.Context, methodName string) (context.Context, func()) {
	// 校验 option
	if ac.option.noCacheOverhead && ac.option.sourceStrategy == SsExpiredDataBackup {
		panic("SsExpiredDataBackup requires cache overhead")
	}

	if ac.option.validNil && ac.option.noCacheOverhead && len(ac.option.nilCacheDefaultValue) == 0 {
		panic("if you want to cache nil while not allow add cache value overhead, cache default value must be not empty")
	}

	ctx, fn := stat.InitStats(ctx, methodName, ac.option.needLog, ac.option.metricsV1, ac.cache.Name(), ac.codec.Name())

	stat.WithMetrics(ctx, stat.TagNS, ac.option.ns)

	if ac.option.noCacheOverhead {
		ctx = cache.WithoutHeader(ctx)
	}
	ctx = cache.WithRuntimeTTL(ctx, ac.option.ttl, ac.option.delTTL, ac.option.ttlStep)
	if ac.option.customTTLFunc != nil {
		ctx = cache.WithCustomTTL(ctx)
	}
	return ctx, fn
}

func (ac *AnyCache) applyDynamicOption(options ...DynamicOption) *AnyCache {
	acc := ac
	if len(options) > 0 {
		acc = ac.clone()
		acc.withDynamicOption(options...)
	}

	return acc
}
