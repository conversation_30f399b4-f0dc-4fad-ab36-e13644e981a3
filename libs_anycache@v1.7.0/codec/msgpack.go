package codec

import (
	"github.com/tinylib/msgp/msgp"
	"github.com/vmihailenco/msgpack/v4"
)

type Msgpack struct {
}

func (m Msgpack) Name() string {
	return "msgpack_codec"
}

func (m Msgpack) Marshal(v interface{}) ([]byte, error) {
	if vv, ok := v.(msgp.Marshaler); ok {
		return vv.MarshalMsg(nil)
	}

	return msgpack.Marshal(v)
}

func (m Msgpack) Unmarshal(data []byte, v interface{}) error {
	if vv, ok := v.(msgp.Unmarshaler); ok {
		_, err := vv.UnmarshalMsg(data)
		return err
	}

	return msgpack.Unmarshal(data, v)
}

func NewMsgpack() Codecer {
	return &Msgpack{}
}
