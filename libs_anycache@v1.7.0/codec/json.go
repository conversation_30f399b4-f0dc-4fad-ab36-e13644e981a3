package codec

import (
	jsoniter "github.com/json-iterator/go"
)

type Json struct {
	Codecer
}

func (j Json) Name() string {
	return "json_codec"
}

func (j <PERSON>son) Marshal(v interface{}) ([]byte, error) {
	return j.Codecer.Marshal(v)
}

func (j <PERSON>son) Unmarshal(data []byte, v interface{}) error {
	return j.Codecer.Unmarshal(data, v)
}

type jsoniterDefault struct {
}

func (j jsoniterDefault) Name() string {
	return "jsoniter_default_codec"
}

func (j jsoniterDefault) Marshal(v interface{}) ([]byte, error) {
	return jsoniter.ConfigDefault.Marshal(v)
}

func (j jsoniterDefault) Unmarshal(data []byte, v interface{}) error {
	return jsoniter.ConfigDefault.Unmarshal(data, v)
}

type jsoniterFast struct {
}

func (j jsoniterFast) Name() string {
	return "jsoniter_fast_codec"
}

func (j jsoniterFast) Marshal(v interface{}) ([]byte, error) {
	return jsoniter.ConfigFastest.Marshal(v)
}

func (j jsoniterFast) Unmarshal(data []byte, v interface{}) error {
	return jsoniter.ConfigFastest.Unmarshal(data, v)
}

type JsonImplType int

const (
	JsonImplStd JsonImplType = iota
	JsonImplIteratorDefault
	JsonImplIteratorFast
	JsonImplRawMessage
)

func NewJson(impType JsonImplType) Codecer {
	imp := &Json{}

	switch impType {
	case JsonImplIteratorDefault:
		imp.Codecer = &jsoniterDefault{}
	case JsonImplIteratorFast:
		imp.Codecer = &jsoniterFast{}
	case JsonImplRawMessage:
		imp.Codecer = newJsonRawMsg()
	default:
		imp.Codecer = newJsonStd()
	}

	return imp
}
