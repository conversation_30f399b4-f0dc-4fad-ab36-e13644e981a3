package codec

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/webcast/libs_anycache/testcase"
)

var (
	codecList = []struct {
		name  string
		codec Codecer
	}{
		{"stdJson", <PERSON><PERSON><PERSON>(JsonImplStd)},
		{"json-iterator-default", <PERSON><PERSON><PERSON>(JsonImplIteratorDefault)},
		{"json-iterator-fast", <PERSON><PERSON>son(JsonImplIteratorFast)},
		{"msgpack", NewMsgpack()},
		{"msgpack1", NewMsgpack1()},
		{"msgpack2", newMsgpack2()},
		{"stdJsonWithGzip", NewCompress(NewJson(JsonImplStd))},
		{"msgpackWithGzip", NewCompress(NewMsgpack())},
	}
)

func Benchmark_Marshal(b *testing.B) {
	for _, item := range codecList {
		b.ResetTimer()

		b.<PERSON>(item.name, func(b *testing.B) {
			b.Report<PERSON>llocs()

			for i := 0; i < b.N; i++ {
				data, err := item.codec.Marshal(testcase.DataInStruct)
				assert.NoError(b, err)
				b.ReportMetric(float64(len(data)), "B/total_mem")
			}
		})
	}
}

func Benchmark_Unmarshal(b *testing.B) {
	for _, item := range codecList {
		data, err := item.codec.Marshal(testcase.DataInStruct)
		assert.NoError(b, err)

		b.ResetTimer()

		b.Run(item.name, func(b *testing.B) {
			v := new(testcase.DataStruct)

			b.ReportAllocs()

			for i := 0; i < b.N; i++ {
				err := item.codec.Unmarshal(data, v)
				assert.NoError(b, err)
			}
		})
	}
}
