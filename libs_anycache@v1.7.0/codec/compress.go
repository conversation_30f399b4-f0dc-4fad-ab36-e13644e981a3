package codec

import (
	"bytes"
	"compress/gzip"
	"io/ioutil"
)

type Compress struct {
	Codecer
}

func (c Compress) Name() string {
	return "compress_codec"
}

func (c Compress) Marshal(v interface{}) ([]byte, error) {
	data, err := c.Codecer.Marshal(v)
	if err != nil {
		return data, err
	}

	var bf bytes.Buffer
	gw := gzip.NewWriter(&bf)

	if _, err := gw.Write(data); err != nil {
		return nil, err
	}

	if err := gw.Flush(); err != nil {
		return nil, err
	}

	if err := gw.Close(); err != nil {
		return nil, err
	}

	return bf.Bytes(), nil
}

func (c Compress) Unmarshal(data []byte, v interface{}) error {
	gr, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return err
	}

	unCompressData, err := ioutil.ReadAll(gr)
	if err != nil {
		return err
	}

	if err := gr.Close(); err != nil {
		return err
	}

	return c.Codecer.Unmarshal(unCompressData, v)
}

func NewCompress(codecer Codecer) Codecer {
	return &Compress{codecer}
}
