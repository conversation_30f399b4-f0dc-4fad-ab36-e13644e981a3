package codec

import (
	"github.com/tinylib/msgp/msgp"
)

type Msgpack1 struct {
}

func (m Msgpack1) Name() string {
	return "msgpack_1_codec"
}

func (m Msgpack1) Marshal(v interface{}) ([]byte, error) {
	if vv, ok := v.(msgp.Marshaler); ok {
		return vv.MarshalMsg(nil)
	}

	panic("must implement msgp.Marshaler")
}

func (m Msgpack1) Unmarshal(data []byte, v interface{}) error {
	if vv, ok := v.(msgp.Unmarshaler); ok {
		_, err := vv.UnmarshalMsg(data)
		return err
	}

	panic("must implement msgp.Unmarshaler")
}

func NewMsgpack1() Codecer {
	return &Msgpack1{}
}
