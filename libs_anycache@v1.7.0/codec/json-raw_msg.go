package codec

import (
	"encoding/json"
	"fmt"
)

type jsonRawMsg struct {
}

func (j jsonRawMsg) Name() string {
	return "json_raw_codec"
}

func (j jsonRawMsg) Marshal(v interface{}) ([]byte, error) {
	return json.RawMessage(mustString(v)), nil
}

func (j jsonRawMsg) Unmarshal(data []byte, v interface{}) error {
	if jrm, ok := v.(*json.RawMessage); ok {
		return jrm.UnmarshalJSON(data)
	}
	return fmt.Errorf("v's type must be a pointer of json.RawMessage ")
}

func newJsonRawMsg() Codecer {
	return &jsonRawMsg{}
}

func mustString(any interface{}) string {
	if any == nil {
		return ""
	}
	switch val := any.(type) {
	case int, uint, int64, uint64, uint32, int32, uint8, int8, int16, uint16:
		return fmt.Sprintf("%d", val)
	case string:
		return val
	case []byte:
		return string(val)
	default:
		return fmt.Sprintf("%v", val)
	}

}
