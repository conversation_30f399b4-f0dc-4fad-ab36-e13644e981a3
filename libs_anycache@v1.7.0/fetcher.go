package anycache

import "context"

// Loader 是单个回源的接口，如果想要实现自己的复杂的单个回源逻辑，需要实现这个接口
//
// - Load 表示回源函数
//
// - GenKey 返回缓存的 key
type Loader interface {
	load(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error)
	genKeyFromParams(ctx context.Context, item interface{}, extraParam interface{}) string
}

// BatchLoader 是批量回源的接口，如果想要实现自己的复杂的批量回源逻辑，需要实现这个接口
//
// - MultiFetch 表示批量回源函数，missedItems 表示需要回源的 item，返回值的 map 的 key 是缓存的 key，value 是回源结果。注意某些 item 回源失败的情况下，不要加入 map
//
// - genKeyFromParams 返回 item 对应的缓存 key
//
// - GetInItems 返回需要处理的所有 item
type BatchLoader interface {
	batchLoad(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error)

	genKeyFromParams(ctx context.Context, item interface{}, extraParam interface{}) string
	genKeyFromResults(ctx context.Context, outItem interface{}, extraParam interface{}) string
}

// WarmUpLoader 是预热缓存的接口
//
// - WarmUpLoad 用来获取热加载的数据
//
// - GenOutItemKey 获取返回值中的数据的 key，作为缓存的 key
type warmUpLoader interface {
	BatchLoader
	warmUpLoad(ctx context.Context) pageWarmUpLoad
}
