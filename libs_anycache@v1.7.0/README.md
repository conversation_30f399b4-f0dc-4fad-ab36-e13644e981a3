<!-- markdown-toc start - Don't edit this section. Run M-x markdown-toc-refresh-toc -->
- [介绍](#介绍)
    - [功能](#功能)
    - [install](#install)
    - [Quick Start](#quick-start)
    - [推荐阅读](#推荐阅读)
    - [友链](#友链)

<!-- markdown-toc end -->
# 介绍

**AnyCache** 是业务开发中缓存使用的最佳实践，帮助开发者安全的使用缓存。

## 功能
- 函数缓存，支持更丰富的函数类型，以更优雅的方式缓存函数（包括 rpc）结果
- 可替换的缓存组件，提供了全局缓存组件、本地缓存组件、多层缓存组件，按需选择，零成本替换
- 可替换的序列化组件，提供了 json、msgpack 等序列化组件，零成本替换
- 灵活的缓存策略配置，如缓存优先、回源优先、只读缓存等，更贴近业务使用场景

## install

``` shell
go get -u code.byted.org/webcast/libs_anycache
```

## Quick Start

``` go
package main

import (
	"context"
	"fmt"

	anycache "code.byted.org/webcast/libs_anycache"
)

type UserInfo struct {
	Uid int64
}

// 批量回源函数
func MultiGetUserInfo(uids []int64) ([]UserInfo, error) {
	// implement
}

var cc anycache.BatchFetcher

func init() {
	// 新建一个 BatchFetcher 用来封装缓存和回源函数
	cc = anycache.NewDefault().BuildBatchFetcherBySliceBatchLoader(
		// 告诉框架如何从传入的参数获取缓存的 key
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return fmt.Sprintf("uid:%d", item)
		},
		// 告诉框架如何从回源函数返回值中解析缓存的 key
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			userInfo := item.(UserInfo)

			return fmt.Sprintf("uid:%d", userInfo.Uid)
		},
		// 声明回源函数
		func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
			uids := missedItems.([]int64)

			return MultiGetUserInfo(uids)
		},
	)
}

func main() {
	var userInfoList []UserInfo
	// 批量获取 uid=[1, 2, 3] 的用户信息
	_, err := cc.MGet(context.Background(), []int64{1, 2, 3}, &userInfoList)
	if err != nil {
		// handler error
	}

	// biz code ...
}
```


## 推荐阅读

[使用说明](https://code.byted.org/webcast/libs_anycache/blob/master/docs/document.md)

[最佳实践](https://code.byted.org/webcast/libs_anycache/blob/master/docs/best-practice.md)

[常见问题](https://code.byted.org/webcast/libs_anycache/blob/master/docs/faq.md)

[缓存相关事故](https://code.byted.org/webcast/libs_anycache/blob/master/docs/fatal.md)

[GoDoc](http://godoc.byted.org/pkg/code.byted.org/webcast/libs_anycache/)

[如何为 anycache 贡献代码](https://code.byted.org/webcast/libs_anycache/blob/master/docs/how-to-contribute.md)

## 联系我们

[请先尝试使用 DeepWiki 找到问题答案](https://deepwiki.byted.org/webcast/libs_anycache/)

[提交issue](https://code.byted.org/webcast/libs_anycache/issues)

## 友链

[一种专用于Recall场景的本地缓存-LinqSource](https://tech.bytedance.net/articles/6930902712942854152)

[Golang 训练营 - <业务缓存anycache实践> by 唐志伟](https://tech.bytedance.net/articles/6963991364547641352)
