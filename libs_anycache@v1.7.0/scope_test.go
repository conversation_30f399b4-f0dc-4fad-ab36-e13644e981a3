package anycache

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"code.byted.org/webcast/logw"
	"github.com/alicebob/miniredis/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"code.byted.org/kv/goredis"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/codec"
	"code.byted.org/webcast/libs_anycache/testcase"
)

var (
	cli       *goredis.Client
	onceRedis sync.Once

	RedisFakePSM = "toutiao.redis.webcast_libs"
)

func GetRedisCli() *goredis.Client {
	onceRedis.Do(func() {
		cli = NewRedisCli(RedisFakePSM)
	})

	return cli
}

func NewRedisCli(psm string) *goredis.Client {
	redisSrv, err := miniredis.Run()
	if err == nil {
		if redisCli, err := goredis.NewClientWithServers("", []string{redisSrv.Addr()}, goredis.NewOption()); err == nil {
			return redisCli
		} else {
			logw.Errorf(fmt.Sprintf("get mock redis client NewClientWithServers failed, psm=%v", psm))
		}
	} else {
		logw.Errorf(fmt.Sprintf("get mock redis client miniredis.Run() failed, psm=%v", psm))
	}
	return nil
}

func Test_saveMGetFromLoaderCache(t *testing.T) {
	background := context.Background()
	cacheIns := New(cache.NewRedisWrap(GetRedisCli()), codec.NewJson(codec.JsonImplIteratorDefault)).BuildBatchFetcherByLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondType := item.(int)
		return fmt.Sprintf("%d", diamondType)
	}, func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
		diamondType := missedItem.(int)
		return strconv.Itoa(diamondType), nil
	})
	anyCache := cacheIns.(*AnyCache)
	entries := make([]cache.IEntry, 0)
	inItems := make([]int, 0)
	for i := 0; i < 3000; i++ {
		inItems = append(inItems, i)
		entry, err := anyCache.newCacheEntry(background, strconv.Itoa(i), strconv.Itoa(i))
		assert.NoError(t, err)
		entries = append(entries, entry)
	}
	result := make([]string, 0)
	_, err := cacheIns.MGet(background, inItems, &result)
	assert.NoError(t, err)
	assert.Equal(t, 3000, len(result))
}

func Test_getFromLoader(t *testing.T) {
	var opt int64
	cacheIns := NewDefault().BuildBatchFetcherBySliceBatchLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
		diamondId := item.(int64)
		liveId := extraParam.(int64)
		return fmt.Sprintf("%d-%d", liveId, diamondId)
	}, func(ctx context.Context, item interface{}, extraParam interface{}) string {
		v := item.(testcase.Diamond)
		return fmt.Sprintf("%d-%d", v.LiveId, v.Id)
	}, func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
		if opt == 0 {
			atomic.AddInt64(&opt, 1)
			return "test", nil
		} else {
			return []string{}, nil
		}
	})
	var res1 []string
	assert.Panics(t, func() {
		_, _ = cacheIns.MGet(context.Background(), []int64{1}, &res1, WithExtraParam(int64(1)))
	})
	t1 := time.Now()
	_, _ = cacheIns.MGet(context.Background(), []int64{1}, &res1, WithExtraParam(int64(1)))
	elapsed := time.Since(t1)
	assert.Less(t, elapsed, time.Millisecond)
}

func TestGetPartiallyFromLoader(t *testing.T) {
	// go test -timeout 30s -run ^TestGetPartiallyFromLoader$ code.byted.org/webcast/libs_anycache -v --count=1
	ctx := context.Background()
	namespace := "test"
	defaultTTL := time.Minute
	defaultDelTTL := time.Minute
	ac := NewDefaultObjectCache().
		WithNameSpace(namespace).
		WithTTL(defaultTTL, defaultDelTTL).
		WithSourceStrategy(SsExpiredDataBackup).
		WithCacheNil(false).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return item.(string)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				fmt.Printf("missitems: %v", missedItems)
				return map[string]string{
					"1": "1",
				}, nil
			},
		)
	ac.MRefresh(ctx, []string{"1", "2"})
	res := make([]string, 0)
	typ, err := ac.MGet(ctx, []string{"1", "2", "3"}, &res)
	fmt.Printf("typ: %v, err: %v", typ, err)
	require.Equal(t, FromLoaderPartially, typ)
}

func TestGetFromLoader(t *testing.T) {
	// go test -timeout 30s -run ^TestGetFromLoader$ code.byted.org/webcast/libs_anycache
	ctx := context.Background()
	namespace := "test"
	defaultTTL := time.Minute
	defaultDelTTL := time.Minute
	ac := NewDefaultObjectCache().
		WithNameSpace(namespace).
		WithTTL(defaultTTL, defaultDelTTL).
		WithSourceStrategy(SsExpiredDataBackup).
		WithCacheNil(false).
		BuildBatchFetcherByBatchLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return item.(string)
			},
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				fmt.Printf("missitems: %v", missedItems)
				return map[string]string{
					"1": "1",
					"2": "2",
				}, nil
			},
		)
	res := make([]string, 0)
	typ, err := ac.MGet(ctx, []string{"1", "2"}, &res)
	fmt.Printf("typ: %v, err: %v", typ, err)
	require.Equal(t, FromLoader, typ)
}

func TestGetFromExpiredDataAndAsyncSource(t *testing.T) {
	// go test -timeout 30s -run ^TestGetFromExpiredDataAndAsyncSource$ code.byted.org/webcast/libs_anycache -v --count=1
	ctx := context.Background()
	namespace := "test"
	defaultTTL := time.Second
	defaultDelTTL := 3 * time.Second

	val := "value1"
	ac := NewDefaultBytesCache().
		WithNameSpace(namespace).
		WithTTL(defaultTTL, defaultDelTTL).
		WithSourceStrategy(SsExpiredDataAndAsyncSource).
		WithCacheNil(false).
		BuildFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return item.(string)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				logw.CtxInfo(ctx, "missitem: %v", missedItem)
				key := missedItem.(string)
				if key == "1" {
					return val, nil
				}
				return nil, ErrNil
			},
		)
	res := ""
	typ, err := ac.Get(ctx, "1", &res)
	require.NoError(t, err)
	require.Equal(t, FromLoader, typ)
	require.Equal(t, val, res)

	typ, err = ac.Get(ctx, "2", &res)
	require.ErrorIs(t, err, ErrNil)
	require.Equal(t, FromLoader, typ)

	time.Sleep(defaultTTL + time.Second) // 等待过期

	typ, err = ac.Get(ctx, "1", &res)
	require.NoError(t, err)
	require.Equal(t, FromExpiredCache, typ)

	time.Sleep(defaultTTL + 3*time.Second)
	typ, err = ac.Get(ctx, "1", &res)
	require.NoError(t, err)
	require.Equal(t, FromLoader, typ)
}
