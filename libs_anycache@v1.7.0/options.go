package anycache

import (
	"context"
	"time"

	// "code.byted.org/ies/smart_cache_pkg/key_sync"
	"code.byted.org/webcast/libs_sync/batch"
)

type option struct {
	// asyncRefreshRate time.Duration // 异步刷新
	ns                       string                     // 命名空间
	ttl                      time.Duration              // 逻辑过期，但不会物理删除
	delTTL                   time.Duration              // 物理删除
	ttlStep                  *float64                   // ttl分级
	maxFetchItemsCount       int                        // 批量回源函数最多允许一次请求多少个 item
	sourceStrategy           SourceStrategy             // 回源策略
	validNil                 bool                       // 是否缓存 nil
	nilTTL                   *time.Duration             // 缓存空值的单独过期时间
	deleteNil                bool                       // 回源结果为 nil 时，是否删除缓存中的数据；只有不缓存 nil 时才生效
	sampleFn                 sampleFunc                 // 采样函数，结果为 true 表示需要缓存
	filterNil                bool                       // 是否过滤 nil
	asyncSaveCache           bool                       // 是否启用异步保存缓存
	needLog                  bool                       // 是否需要打日志
	noCacheOverhead          bool                       // cache 不加 overhead
	objectCache              bool                       // 使用对象缓存
	nilCacheDefaultValue     []byte                     // 回源结果为nil时，默认的cache value
	returnFastWhenCacheErrFn returnFastWhenCacheErrFunc // 当缓存获取失败时，直接返回
	metricsV1                bool                       // 采集新版 metrics 指标，详见： https://bytedance.feishu.cn/docx/doxcnfPsx35J0lxN1KuHjfSb10f
	ctxKeys                  []string                   // 异步流程希望继承的 ctx key, 目前只支持 string 类型， anycache 会自动继承 K_ENV 和 K_LOGID, 所以这两个不需要手动指定
	ctxCloner                ctxClonerFunc              // 用于克隆 ctx 以在异步流程中设置需要继承的 ctx key 等；如果设置了ctxCloner，K_ENV、K_LOGID 等 key 需要手动复制
	partiallyCache           bool                       // 批量回源时，是否允许部分成功写缓存

	batchLoader  BatchLoader
	loader       Loader
	warmUpLoader warmUpLoader

	refreshWorker      batch.AsyncWorker
	batchRefreshWorker batch.AsyncWorker
	customTTLFunc      func(ctx context.Context, item interface{}) (ttl time.Duration, delTTL time.Duration)

	extraParam interface{}
}

func getDefaultOption() *option {
	opt := option{
		ns:                 "",
		ttl:                time.Minute,
		delTTL:             time.Minute,
		maxFetchItemsCount: 50,
		sourceStrategy:     SsCacheFirst,
		validNil:           true,
		nilTTL:             nil,
		deleteNil:          false,
		sampleFn: func(ctx context.Context, resp interface{}) bool {
			return true
		},
		filterNil:            false,
		asyncSaveCache:       false,
		needLog:              true,
		noCacheOverhead:      false,
		objectCache:          false,
		nilCacheDefaultValue: nil,
		returnFastWhenCacheErrFn: func(ctx context.Context, err error) bool {
			return false
		},
		metricsV1: true,
	}

	return &opt
}

func (opt *option) clone() *option {
	c := option{
		ns:                       opt.ns,
		ttl:                      opt.ttl,
		delTTL:                   opt.delTTL,
		maxFetchItemsCount:       opt.maxFetchItemsCount,
		sourceStrategy:           opt.sourceStrategy,
		validNil:                 opt.validNil,
		nilTTL:                   opt.nilTTL,
		deleteNil:                opt.deleteNil,
		sampleFn:                 opt.sampleFn,
		filterNil:                opt.filterNil,
		asyncSaveCache:           opt.asyncSaveCache,
		needLog:                  opt.needLog,
		noCacheOverhead:          opt.noCacheOverhead,
		objectCache:              opt.objectCache,
		nilCacheDefaultValue:     opt.nilCacheDefaultValue,
		returnFastWhenCacheErrFn: opt.returnFastWhenCacheErrFn,
		metricsV1:                opt.metricsV1,
		ctxKeys:                  opt.ctxKeys,
		ctxCloner:                opt.ctxCloner,

		batchLoader:  opt.batchLoader,
		loader:       opt.loader,
		warmUpLoader: opt.warmUpLoader,

		// smartKeySync:       opt.smartKeySync,
		refreshWorker:      opt.refreshWorker,
		batchRefreshWorker: opt.batchRefreshWorker,
	}

	return &c
}

type sampleFunc func(ctx context.Context, resp interface{}) bool

type returnFastWhenCacheErrFunc func(ctx context.Context, err error) bool

type ctxClonerFunc func(ctx context.Context) context.Context

const samplePrecision = 1_000_000

// -- option start ---
func (ac *AnyCache) WithNameSpace(ns string) *AnyCache {
	acc := ac.clone()
	acc.option.ns = ns
	return acc
}

func (ac *AnyCache) WithTTL(ttl, delTTL time.Duration) *AnyCache {
	// 逻辑删除时间必须小于等于物理删除时间
	if ttl > delTTL {
		delTTL = ttl
	}

	acc := ac.clone()
	acc.option.ttl = ttl
	acc.option.delTTL = delTTL

	return acc
}

// FIXME rename
func (ac *AnyCache) WithBatchSize(max int) *AnyCache {
	acc := ac.clone()
	acc.option.maxFetchItemsCount = max

	return acc
}

// FIXME rename
func (ac *AnyCache) WithCacheNil(b bool) *AnyCache {
	acc := ac.clone()
	acc.option.validNil = b

	return acc
}

func (ac *AnyCache) WithNilTTL(ttl time.Duration) *AnyCache {
	acc := ac.clone()
	acc.option.nilTTL = &ttl

	return acc
}

type SourceStrategy int

const (
	SsCacheFirst                = iota // 优先缓存，缓存失败回源
	SsSourceFirst                      // 优先回源，回源失败走缓存
	SsOnlyCache                        // 不回源，只读缓存
	SsOnlySource                       // 只回源，不读缓存
	SsExpiredDataBackup                // 优先缓存；miss（过期或不存在） 后回源；回源失败，如果缓存是过期导致 miss，使用过期缓存兜底
	SsExpiredDataAndAsyncSource        // 优先缓存，如果缓存过期返回过期的缓存，同时异步回源；如果缓存 miss，同步回源
)

func (ac *AnyCache) WithSourceStrategy(ss SourceStrategy) *AnyCache {
	acc := ac.clone()
	acc.option.sourceStrategy = ss

	return acc
}

func (ac *AnyCache) WithCacheSampling(pct int) *AnyCache {
	acc := ac.clone()
	acc.option.sampleFn = func(ctx context.Context, resp interface{}) bool {
		pct = pct % samplePrecision

		random := time.Now().Nanosecond()
		return random%samplePrecision < pct
	}

	return acc
}

func (ac *AnyCache) WithCustomCacheSampling(fn func(ctx context.Context, resp interface{}) bool) *AnyCache {
	acc := ac.clone()
	acc.option.sampleFn = fn

	return acc
}

func (ac *AnyCache) WithFilterNilInSliceRet(b bool) *AnyCache {
	acc := ac.clone()
	acc.option.filterNil = b

	return acc
}

// WithReturnFastWhenCacheErr return directly when an error occurs in the cache
func (ac *AnyCache) WithReturnFastWhenCacheErr(fn func(ctx context.Context, err error) bool) *AnyCache {
	acc := ac.clone()
	acc.option.returnFastWhenCacheErrFn = fn

	return acc
}

func (ac *AnyCache) WithAsyncSaveCache(b bool) *AnyCache {
	acc := ac.clone()
	acc.option.asyncSaveCache = b

	if b {
		acc.startAsyncSavers()
	} else {
		acc.stopAsyncSavers()
	}

	return acc
}

func (ac *AnyCache) WithoutLog() *AnyCache {
	acc := ac.clone()
	acc.option.needLog = false

	return acc
}

func (ac *AnyCache) WithoutCacheHeader() *AnyCache {
	acc := ac.clone()
	acc.option.noCacheOverhead = true

	return acc
}

func (ac *AnyCache) WithObjectCache() *AnyCache {
	acc := ac.clone()
	acc.option.objectCache = true

	return acc
}

func (ac *AnyCache) WithCacheNilDefaultValue(b []byte) *AnyCache {
	acc := ac.clone()
	acc.option.nilCacheDefaultValue = b

	return acc
}

func (ac *AnyCache) WithDeleteNil(b bool) *AnyCache {
	acc := ac.clone()
	acc.option.deleteNil = b

	return acc
}

func (ac *AnyCache) WithMetricsV1(t bool) *AnyCache {
	acc := ac.clone()
	acc.option.metricsV1 = t

	return acc
}

func (ac *AnyCache) WithCtxKeys(keys ...string) *AnyCache {
	acc := ac.clone()
	acc.option.ctxKeys = keys

	return acc
}

func (ac *AnyCache) WithCtxCloner(cloner func(ctx context.Context) context.Context) *AnyCache {
	acc := ac.clone()
	acc.option.ctxCloner = cloner

	return acc
}

func (ac *AnyCache) WithLinterCheckType(value interface{}) *AnyCache {
	_ = value

	return ac
}

// -- option end ---

// -- dynamic option start ---
type DynamicOption func(op *option)

func (ac *AnyCache) withDynamicOption(options ...DynamicOption) {
	for _, op := range options {
		op(ac.option)
	}
}

func WithSourceStrategy(ss SourceStrategy) DynamicOption {
	return func(op *option) {
		op.sourceStrategy = ss
	}
}

func withoutNamespace() DynamicOption {
	return func(op *option) {
		op.ns = ""
	}
}

func WithExtraParam(v interface{}) DynamicOption {
	return func(op *option) {
		op.extraParam = v
	}
}

func WithTTL(ttl, delTTL time.Duration) DynamicOption {
	// 逻辑删除时间必须小于等于物理删除时间
	if ttl > delTTL {
		delTTL = ttl
	}
	return func(op *option) {
		op.ttl = ttl
		op.delTTL = delTTL
	}
}

func WithTTLStep(ttlStep float64) DynamicOption {
	if ttlStep <= 0.0 {
		return func(op *option) {}
	}

	return func(op *option) {
		op.ttlStep = &ttlStep
	}
}

func withDeleteNil(b bool) DynamicOption {
	return func(op *option) {
		op.deleteNil = b
	}
}

func WithFilterNilInSliceRet(b bool) DynamicOption {
	return func(op *option) {
		op.filterNil = b

	}
}

func WithCtxKeys(keys ...string) DynamicOption {
	return func(op *option) {
		op.ctxKeys = keys
	}
}

func WithCustomTTL(ttlFunc func(ctx context.Context, item interface{}) (ttl time.Duration, delTTL time.Duration)) DynamicOption {
	return func(op *option) {
		op.customTTLFunc = ttlFunc
	}
}

func WithPartiallyCache(b bool) DynamicOption {
	return func(op *option) {
		op.partiallyCache = b
	}
}

// -- dynamic option end ---
