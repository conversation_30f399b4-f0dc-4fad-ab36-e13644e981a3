package anycache

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

//func Test_slice2Interface(t *testing.T) {
//	var iList []interface{}
//	for i := 0; i < 100; i++ {
//		iList = append(iList, i)
//	}
//	t.Logf("%#v", slice2Interface(iList))
//
//	var i64List []interface{}
//	for i := 0; i < 100; i++ {
//		i64List = append(i64List, int64(i))
//	}
//	t.Logf("%#v", slice2Interface(i64List))
//
//	var i32List []interface{}
//	for i := 0; i < 100; i++ {
//		i32List = append(i32List, int32(i))
//	}
//	t.Logf("%#v", slice2Interface(i32List))
//
//	var i16List []interface{}
//	for i := 0; i < 100; i++ {
//		i16List = append(i16List, int16(i))
//	}
//	t.Logf("%#v", slice2Interface(i16List))
//
//	var i8List []interface{}
//	for i := 0; i < 100; i++ {
//		i8List = append(i8List, int8(i))
//	}
//	t.Logf("%#v", slice2Interface(i8List))
//
//	var byteList []interface{}
//	for i := 0; i < 100; i++ {
//		byteList = append(byteList, byte(i))
//	}
//	t.Logf("%#v", slice2Interface(byteList))
//}

//func Test_slice2InterfacePtr(t *testing.T) {
//	var iList []interface{}
//	for i := 0; i < 100; i++ {
//		j := i
//		iList = append(iList, &j)
//	}
//	i := slice2Interface(iList)
//	t.Log(i)
//}

func Test_slice2InterfaceReflect(t *testing.T) {
	var iList []interface{}
	for i := 0; i < 100; i++ {
		iList = append(iList, i)
	}
	i := slice2InterfaceReflect(iList)
	t.Log(i)
}

//func Test_interface2Slice(t *testing.T) {
//	var iList []int
//	for i := 0; i < 100; i++ {
//		iList = append(iList, i)
//	}
//
//	i := interface2Slice(iList, reflect.TypeOf(1), convert2Eface(1))
//	t.Logf("%#v", i)
//}

func Test_interface2SliceReflect(t *testing.T) {
	var iList []int
	for i := 0; i < 100; i++ {
		iList = append(iList, i)
	}

	i := interface2SliceReflect(iList)
	t.Logf("%#v", i)
}

func Test_ResultSource_String(t *testing.T) {
	assert.Equal(t, "FromCache", fmt.Sprint(FromCache))
	assert.Equal(t, "FromLoader", fmt.Sprint(FromLoader))
	assert.Equal(t, "FromLoaderPartially", fmt.Sprint(FromLoaderPartially))
}
