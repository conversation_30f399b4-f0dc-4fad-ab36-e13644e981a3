package async

import (
	"sync/atomic"
	"testing"
	"time"

	"code.byted.org/webcast/libs_anycache/async/task"
	"github.com/stretchr/testify/assert"
)

func TestSimpleTask(t *testing.T) {
	t.<PERSON>()

	s := NewLocal()
	var count int64
	unit := task.NewTask(2*time.Second, func() {
		atomic.AddInt64(&count, 1)
	})
	err := s.Submit("test", unit)
	assert.NoError(t, err)
	time.Sleep(10 * time.Second)
	assert.Equal(t, true, count > 2)
}

func TestSimpleMultiTaskMerge(t *testing.T) {
	t.<PERSON>llel()

	s := NewLocal()
	var err error
	var count, count1, count2 int64

	mt := task.NewMultiTask(2*time.Second, func(missedItems []interface{}) {
		atomic.AddInt64(&count, 1)
	}, []interface{}{1, 2, 3})
	err = s.Submit("multi:test", mt)
	assert.NoError(t, err)

	time.Sleep(6 * time.Second)
	assert.Equal(t, []interface{}{1, 2, 3}, mt.Items())

	mt1 := task.NewMultiTask(2*time.Second, func(missedItems []interface{}) {
		atomic.AddInt64(&count1, 1)
	}, []interface{}{1, 2, 3})
	err = s.Submit("multi:test", mt1)
	assert.NoError(t, err)

	time.Sleep(6 * time.Second)
	// mt1 的数据被加到了 mt 上
	assert.Equal(t, []interface{}{1, 2, 3, 1, 2, 3}, mt.Items())
	assert.Equal(t, []interface{}{1, 2, 3}, mt1.Items())

	mt2 := task.NewMultiTask(2*time.Second, func(missedItems []interface{}) {
		atomic.AddInt64(&count2, 1)
	}, []interface{}{1, 2, 3})
	err = s.Submit("multi:test2", mt2)
	assert.NoError(t, err)

	time.Sleep(8 * time.Second)
	// mt2 的数据自己保存了，没有给 mt 和 mt1
	assert.Equal(t, []interface{}{1, 2, 3, 1, 2, 3}, mt.Items())
	assert.Equal(t, []interface{}{1, 2, 3}, mt1.Items())
	assert.Equal(t, []interface{}{1, 2, 3}, mt2.Items())

	assert.Equal(t, true, count >= 7)
	assert.Equal(t, int64(0), count1)
	assert.Equal(t, true, count2 >= 3)
}

func TestSchedule(t *testing.T) {
	t.Parallel()

	s := NewLocal()
	var count int64
	unit := task.NewTask(3*time.Second, func() {
		atomic.AddInt64(&count, 1)
	})
	err := s.Submit("test", unit)
	assert.NoError(t, err)
	time.Sleep(10 * time.Second)
	assert.Equal(t, int64(3), count)
}

func TestPanic(t *testing.T) {
	t.Parallel()

	s := NewLocal()
	var err error
	unit := task.NewTask(3*time.Second, func() {
	})
	err = s.Submit("test", unit)
	assert.NoError(t, err)

	mt := task.NewMultiTask(time.Second, func(missedItems []interface{}) {
	}, []interface{}{1, 2, 3})

	assert.Panics(t, func() {
		err = s.Submit("test", mt)
		assert.NoError(t, err)
	})
}
