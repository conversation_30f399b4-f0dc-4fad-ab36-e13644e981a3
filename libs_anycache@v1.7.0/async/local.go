package async

import (
	"sync"
	"sync/atomic"
	"time"

	"code.byted.org/webcast/libs_anycache/async/task"
)

type local struct {
	tasks sync.Map
}

type entry struct {
	t       task.Tasker
	lastRun time.Time
	running int32
}

func (e *entry) lock() bool {
	return atomic.CompareAndSwapInt32(&e.running, 0, 1)
}

func (e *entry) unlock() bool {
	return atomic.CompareAndSwapInt32(&e.running, 1, 0)
}

func newEntry(t task.Tasker) *entry {
	return &entry{
		t: t,
	}
}

func NewLocal() Worker {
	s := &local{}
	go s.run()

	return s
}

// 如果是单任务，直接加入就可以；存在即覆盖
// 如果是批量任务，如果不存在就加入；存在的话，加入批量任务的 tasker
func (s *local) Submit(key string, t task.Tasker) error {
	mt, ok := t.(task.MultiTasker)

	// 单任务直接覆盖
	if !ok {
		s.tasks.Store(key, newEntry(t))
		return nil
	}

	// 多任务
	actual, loaded := s.tasks.LoadOrStore(key, newEntry(t))
	// 不存在直接加入即可
	if !loaded {
		return nil
	}

	// 可能 panic?
	actual.(*entry).t.(task.MultiTasker).Append(mt.Items())
	return nil
}

func (s *local) run() {
	for range time.Tick(time.Second) {
		now := time.Now()
		s.tasks.Range(func(key, value interface{}) bool {
			e := value.(*entry)

			// 最短 1s 运行一次
			d := e.t.Duration()
			if d < time.Second {
				d = time.Second
			}

			if now.Sub(e.lastRun) >= d {
				if e.lock() {
					go func() {
						e.t.Run()
						e.unlock()
					}()
					e.lastRun = now
				}
			}

			return true
		})
	}
}
