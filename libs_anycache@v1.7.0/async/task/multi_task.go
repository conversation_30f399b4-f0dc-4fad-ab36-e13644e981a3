package task

import (
	"sync"
	"time"
)

type MultiTasker interface {
	Tasker
	Append(missedItems []interface{})
	Items() []interface{}
}

type MultiTask struct {
	d     time.Duration
	fn    func(missedItems []interface{})
	items []interface{}
	lock sync.RWMutex
}

func (m *MultiTask) Duration() time.Duration {
	return m.d
}

func (m *MultiTask) Items() []interface{} {
	return m.items
}

func (m *MultiTask) Run() {
	// TODO 这里加锁合适吗？
	m.lock.RLock()
	missedItems := m.items
	m.fn(missedItems)
	m.lock.RUnlock()
}

func (m *MultiTask) Append(missedItems []interface{}) {
	// TODO 如果有重复的item怎么办？
	m.lock.Lock()
	m.items = append(m.items, missedItems...)
	m.lock.Unlock()
}

func NewMultiTask(d time.Duration, fn func(missedItems []interface{}), items []interface{}) MultiTasker {
	return &MultiTask{
		d:     d,
		fn:    fn,
		items: items,
	}
}
