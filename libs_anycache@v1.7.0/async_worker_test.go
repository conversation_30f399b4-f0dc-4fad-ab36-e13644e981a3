package anycache

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/davecgh/go-spew/spew"
	"github.com/stretchr/testify/assert"

	"code.byted.org/webcast/libs_anycache/testcase"
)

func TestAnyCache_asyncBatchRefresh(t *testing.T) {
	cc := NewDefault().WithSourceStrategy(SsExpiredDataAndAsyncSource).
		BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("%d-%d", item, extraParam)
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				diamondList, err := testcase.GetDiamondPtrList(ctx, extraParam.(int64), []int64{missedItem.(int64)})
				if err != nil {
					return nil, err
				}

				return diamondList[0], nil
			})

	var (
		ctx             = context.TODO()
		diamondTypeList = []interface{}{int64(1), int64(2)}
		liveId          = int64(1)
	)

	{
		var res1 []*testcase.Diamond
		from1, err1 := cc.MGet(ctx, []int64{1}, &res1, WithExtraParam(liveId))
		assert.NoError(t, err1)
		assert.Equal(t, FromLoader, from1)
		t.Log(spew.Sprint(res1))
	}

	assert.Equal(t, 1, asyncBatchRefreshWithRet(convertToAsyncRefreshParam(ctx, cc.(*AnyCache), diamondTypeList, liveId)))
	assert.Equal(t, 0, asyncBatchRefreshWithRet(convertToAsyncRefreshParam(ctx, cc.(*AnyCache), diamondTypeList, liveId)))

	// 测试动态参数是否生效
	time.Sleep(2 * time.Second)
	assert.Equal(t, 0, asyncBatchRefreshWithRet(convertToAsyncRefreshParam(ctx, cc.(*AnyCache), diamondTypeList, liveId)))
	assert.Equal(t, 1, asyncBatchRefreshWithRet(convertToAsyncRefreshParam(ctx, cc.(*AnyCache).applyDynamicOption(WithTTL(time.Second, time.Second)), []interface{}{int64(3)}, liveId)))
	assert.Equal(t, 0, asyncBatchRefreshWithRet(convertToAsyncRefreshParam(ctx, cc.(*AnyCache).applyDynamicOption(WithTTL(time.Second, time.Second)), []interface{}{int64(3)}, liveId)))
	time.Sleep(2 * time.Second)
	assert.Equal(t, 1, asyncBatchRefreshWithRet(convertToAsyncRefreshParam(ctx, cc.(*AnyCache).applyDynamicOption(WithTTL(time.Second, time.Second)), []interface{}{int64(3)}, liveId)))
	var res1 []*testcase.Diamond
	from1, err1 := cc.MGet(ctx, []int64{1, 2}, &res1, WithExtraParam(liveId))
	assert.NoError(t, err1)
	assert.Equal(t, FromCache, from1)
	t.Log(spew.Sprint(res1))
}

func TestAnyCache_asyncRefresh(t *testing.T) {
	cacheIns := NewDefault().WithSourceStrategy(SsExpiredDataAndAsyncSource).
		BuildFetcherByLoader(func(ctx context.Context, item interface{}, extraParam interface{}) string {
			liveId, diamondType := item.(int64), extraParam.(int64)
			return fmt.Sprintf("%d-%d", liveId, diamondType)
		}, func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			liveId, diamondType := missedItem.(int64), extraParam.(int64)
			return testcase.GetDiamondByDiamondType(ctx, liveId, diamondType)
		})

	var (
		ctx         = context.TODO()
		diamondType = int64(1)
		liveId      = int64(1)
	)

	assert.Equal(t, 1, asyncRefreshWithRet(convertToAsyncRefreshParam(ctx, cacheIns.(*AnyCache), diamondType, liveId)))
	assert.Equal(t, 0, asyncRefreshWithRet(convertToAsyncRefreshParam(ctx, cacheIns.(*AnyCache), diamondType, liveId)))

	// 测试动态参数是否生效
	time.Sleep(2 * time.Second)
	assert.Equal(t, 0, asyncRefreshWithRet(convertToAsyncRefreshParam(ctx, cacheIns.(*AnyCache), diamondType, liveId)))
	assert.Equal(t, 1, asyncRefreshWithRet(convertToAsyncRefreshParam(ctx, cacheIns.(*AnyCache).applyDynamicOption(WithTTL(time.Second, time.Second)), int64(3), liveId)))
	assert.Equal(t, 0, asyncRefreshWithRet(convertToAsyncRefreshParam(ctx, cacheIns.(*AnyCache).applyDynamicOption(WithTTL(time.Second, time.Second)), int64(3), liveId)))
	time.Sleep(2 * time.Second)
	assert.Equal(t, 1, asyncRefreshWithRet(convertToAsyncRefreshParam(ctx, cacheIns.(*AnyCache).applyDynamicOption(WithTTL(time.Second, time.Second)), int64(3), liveId)))

	var res1 []testcase.Diamond
	from1, err1 := cacheIns.Get(ctx, diamondType, &res1, WithExtraParam(liveId))
	assert.NoError(t, err1)
	assert.Equal(t, FromCache, from1)
	t.Log(spew.Sprint(res1))
}

func TestAnyCache_asyncCtxOptions(t *testing.T) {
	const (
		CtxKey1 = "K_TEST_1"
		CtxKey2 = "K_TEST_2"
		CtxKey3 = "K_TEST_3"
	)

	checkCtx := func(t *testing.T, client *AnyCache, check func(context.Context)) {
		ctx := context.WithValue(context.WithValue(context.Background(), CtxKey1, "test1"), CtxKey2, "test2")
		actx, _, _, _ := convertFromAsyncRefreshParam(convertToAsyncRefreshParam(ctx, client, nil, nil))
		check(actx)
	}
	t.Run("WithCtxKeys", func(t *testing.T) {
		checkCtx(t, NewDefault().WithCtxKeys(CtxKey1), func(ctx context.Context) {
			assert.Equal(t, "test1", ctx.Value(CtxKey1))
			assert.Equal(t, nil, ctx.Value(CtxKey2))
		})
	})
	t.Run("WithCtxCloner", func(t *testing.T) {
		checkCtx(t, NewDefault().WithCtxCloner(func(ctx context.Context) context.Context {
			return context.WithValue(ctx, CtxKey3, "test3")
		}), func(ctx context.Context) {
			assert.Equal(t, "test1", ctx.Value(CtxKey1))
			assert.Equal(t, "test2", ctx.Value(CtxKey2))
			assert.Equal(t, "test3", ctx.Value(CtxKey3))
		})
	})
}
