<!-- markdown-toc start - Don't edit this section. Run M-x markdown-toc-refresh-toc -->
**Table of Contents**

- [常见问题](#常见问题)
    - [NewRedisWrap 和 NewRedisWithRetry 应该怎么选？](#newrediswrap-和-newrediswithretry-应该怎么选)
        - [如果 redis 出现 io timeout 错误，应该如何处理？](#如果-redis-出现-io-timeout-错误应该如何处理)
        - [如果要增加重试，在 redis 的全局配置中增加重试，还是一些关键的节点手动重试？](#如果要增加重试在-redis-的全局配置中增加重试还是一些关键的节点手动重试)
    - [缓存策略的应用场景是什么？](#缓存策略的应用场景是什么)
        - [缓存优先](#缓存优先)
        - [回源优先](#回源优先)
        - [只读缓存](#只读缓存)
        - [只回源](#只回源)
        - [过期数据做 backup](#过期数据做-backup)
    - [在请求量比较高的服务，一旦缓存失效，很大的瞬时回源流量可能会给源造成很大压力，怎么办？](#在请求量比较高的服务一旦缓存失效很大的瞬时回源流量可能会给源造成很大压力怎么办)

<!-- markdown-toc end -->
# 常见问题


## NewRedisWrap 和 NewRedisWithRetry 应该怎么选？

讨论这个问题前，我们先讨论另外两个问题



### 如果 redis 出现 io timeout 错误，应该如何处理？

1.  调长超时时间，尽量减少超时错误
2.  超时时间设置为 p99,增加有限次的重试
3.  （有其他方案麻烦联系下我）

1 实际上并不能解决问题。redis client 的延时通常在 50ms 以内，如果在默认 50ms 超时的情况下还是出现超时错误，很可能是长尾错误。 这一类的错误通过调长超时时间并不能解决问题，反而长时间的占用了 redis 链接，给服务增加了负担

微服务场景下，服务间的调用应该快速失败+有限次重拾+指数回退，来解决超时问题。因此建议采用方案 2



### 如果要增加重试，在 redis 的全局配置中增加重试，还是一些关键的节点手动重试？

两种方式都可以解决问题。

方式一：

优点是，代码简单，全局生效

缺点是，可能是会增加一部分重试流量（存疑，有限次重试基本不会成为压垮 redis 的原因）

方式二：

优点是：精打细算，节省 redis 资源

缺点是，增加代码复杂度；可能会埋坑：如果同时给 redis 全局配置了重试，最终的重试次数=全局重试次数×局部重试次数

如果你喜欢方式一，请选用 NewRedisWrap；如果你喜欢方案二，请选用 NewRedisWithRetry。推荐 NewRedisWrap



## 缓存策略的应用场景是什么？



### 缓存优先

默认策略，大部分情况下都应该使用这个策略。优先读缓存，缓存未命中再回源。



### 回源优先

目前有一种场景适合这个策略：做 backup 数据

回源没有问题的时候，优先回源；回源失败后，用缓存数据做兜底。牺牲实时性、准确性，换取稳定性。

有愿意尝试的小伙伴联系我～



### 只读缓存

这种策略通常和主动更新缓存配合使用。缓存永不过期，由异步组件来定期更新缓存。



### 只回源

通过修改缓存策略为只回源，使所有的缓存都失效，所有的请求都回源。可能的应用场景是，加缓存初期担心出问题，做一个 ab 放量。



### 过期数据做 backup

算是 `缓存优先` 的加强版，将源失败的影响降低到最小。



## 在请求量比较高的服务，一旦缓存失效，很大的瞬时回源流量可能会给源造成很大压力，怎么办？

anycache 在单实例上采取了 singleflight 的处理方式。单个实例上统一时间只会对一个 key 回源一次，其他请求会等待回源的结果。 具体可以看下 singleflight 的实现。

不能完全杜绝瞬时回源流量，能一定程度的缓解。

有计划做一个全局的 singleflight，目前还在调研阶段
