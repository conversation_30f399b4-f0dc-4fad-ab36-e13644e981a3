<!-- markdown-toc start - Don't edit this section. Run M-x markdown-toc-refresh-toc -->
**Table of Contents**

- [缓存相关事故记录](#缓存相关事故记录)
    - [榜单数据不更新](#榜单数据不更新)
        - [事故描述](#事故描述)
        - [原因分析](#原因分析)
        - [改进](#改进)
    - [音乐中台本地缓存失效](#音乐中台本地缓存失效)
        - [原因分析](#原因分析-1)
        - [改进](#改进-1)

<!-- markdown-toc end -->
# 缓存相关事故记录

## 榜单数据不更新

### 事故描述

4 月 1 日晚老罗直播间，20:17 后音浪数据不更新

### 原因分析

音浪数据默认使用"只回源"策略，没有设置过期时间；高热直播间使用"过期数据兜底"策略，设置了过期时间。
通过 tcc 配置动态设置走哪个策略。

代码如下：

```go

// 默认没有设置 ttl
options := []anycache.Option{
	anycache.WithSourceStrategy(anycache.SsOnlySource),
	anycache.WithPrefix(fmt.Sprintf("%s_%s_%s", liveId, rankName, method)),
}

// 动态设置回源策略和 ttl
if useCache, short, _ := dao.IsNeedLocalCache(bigkey); useCache {
	options = append(options, anycache.WithSourceStrategy(anycache.SsExpiredDataBackup), anycache.WithTTL(short))
}

anycache.WithOptions(options).Get(...)

```

20:17 前，使用“只回源”策略，没有设置过期时间，每次回源都会写缓存，并且缓存永不过期；20:17 后，使
用“过期数据兜底”策略，但由于之前的缓存不会过期，所以永远不会回源，数据不会更新。

### 改进

1. anycache 的“只回源”策略，修改为不写缓存
2. anycache 的默认过期时间设置为 1 分钟，避免没有设置 ttl 导致数据不过期的情况；如果需要数据永不
过期，需要显式的设置 ttl=0

## 音乐中台本地缓存失效

[fatal](https://bytedance.feishu.cn/docs/doccnsJkPCIuuQeOiTnpF7wGrXb)

### 原因分析

当前版本 smartcache 和 anycache 的集成方案存在问题：如果业务同时使用了 smartcache 和 anycache+smartcache，那么不是通过 anycache 的请求不会命中 smartcache 的本地缓存，导致全部回源到 redis。

集成方案参考：https://bytedance.feishu.cn/wiki/wikcnMwQJLCD71uhvjxCnppYDJf


### 改进

1. 暂时不推荐使用 anycache+smartcache 的组合方式，smartcache 标记为 deprecated。
2. 探索更合理的集成方案。
