# Design and implement

## 缓存组件

### 逻辑过期时间

anycache 的逻辑过期不依赖缓存的具体实现。

anycache 将每条记录定义为一个 entry

```go
type Entry struct {
	key string
	val []byte

	createAt int64
	ttl      time.Duration
}
```

除了 key、val 两个必须的字段外，还包含了创建时间的时间戳和有效期两个字段。写入缓存时，缓存的 key 不变，缓存的 value 变成了 Entry 序列化之后的结果，具体的做法是：

2 个字节的版本信息 + 4 个字节的创建时间 + 8 个字节的有效期 + 本身的 val

也就是说，每个 entry 在存储时，会有 14 个字节的 overhead。



### 物理过期时间

物理删除依赖缓存的具体实现



### 如何实现的缓存 nil

anycache 也借助 entry 实现了缓存 nil 的功能。nil 时，entry.val 为空，但 entry 的其他信息都是存在的。借此区分开了什么是有缓存但是是 nil 和什么是没有缓存。



### 多级缓存

anycache 的缓存组件包含了一个特殊的缓存实现&#x2013;多级缓存。

多级缓存组件实现了多级缓存情况下，如何正确的获取、设置、回源缓存：

获取：从上到下，依次读取缓存。

设置：从下往上，依次设置缓存。

回源：命中缓存后，回写到上层缓存中。下次再获取时，可以直接从上层缓存中读取到数据。

1.  最佳实践

    多级缓存的最佳实践是两层缓存：本地缓存 + 全局缓存。更多级的缓存效果不明显，反而增加了复杂度

2.  多级缓存下的过期时间设置

    多级缓存下的过期时间设置，如果设置成一样的，容易出现多层缓存的数据同时过期，大量回源的情况。
    
    为了解决这个问题，我们给过期时间设置了一个系数，期望能够把上下层缓存的过期时间打散。这里用户需要注意，不要依赖这个不精确的过期时间。
    
    目前的系数是，上层缓存是下层缓存的 0.7 倍，下层是上层的 1.3 倍。



## 异步组件

异步组件目前主要是提供主动刷新的功能。 `这个功能还不成熟，慎用`

异步组件需要实现三个接口： Worker、Tasker 和 MultiTasker。Worker 用于提交任务，定时去运行任务。Tasker 是一般任务，MultiTasker 是批量任务，在添加环节有区别。

Tasker 任务会直接添加到 Worker 的待处理任务中；而 MultiTasker 会首先查一下有没有相同的任务已经添加到 Worker 中了，如果没有添加也会直接添加到 Worker 的待处理任务中。 如果已经添加了，会把自身的 items 添加到之前的任务中，自身不加入到 Worker 的待处理任务中。

这样处理的目的是，针对批量回源的场景，一个 missedItem 只回源一次，减轻源的压力。



### 异步组件的调度

异步组件的调度不是精确调度，最高精度到秒，延迟不超过 1 秒。

具体的实现是：

1.  每个任务会记录自身上次运行时间。第一次运行时，上次运行时间是 0
2.  借助 go 的定时器，每秒遍历一次待处理任务列表
3.  如果 当前时间-上次运行时间 >= 任务的运行时间间隔，就异步运行，否则跳过
4.  任务运行时会在自身加锁，保证一个任务同一时间只会被运行一次



## 序列化组件

序列化组件没有特殊的地方，只要能够正确的序列化、反序列化即可。

anycache 自带的序列化组件只是包装了一下标准库自带的 json 解析。

后续可能会提供更高效的序列化组件。



## 统计组件

统计组件以来 context 来统计数据。

用户调用 anycache 的方法来操作数据时，会自动在 context 里嵌入一个统计相关的结构体。在 anycache 运行过程中，关键节点会在 context 中加入埋点数据。最后在返回用户代码前， 将 context 中统计数据，记录到 metrics 和日志中。

注意：埋点数据并没有使用 context 的 withValue，埋点数据的增加不会有性能问题。

目前支持的 metrics 指标有：

1.  method，anycache 的方法名
2.  sp<sub>hit</sub>，是否命中缓存
3.  sp<sub>part</sub><sub>hit</sub>，是否部分命中缓存
4.  sp<sub>source</sub>，是否回源
5.  cache<sub>level</sub>，代表读取缓存的层数。一层缓存的情况下，始终等于 0

举例说明：

sp<sub>hit</sub>=1,cache<sub>level</sub>=0：代表在第 0 层缓存命中缓存 sp<sub>hit</sub>=1,cache<sub>level</sub>=1：代表在第 1 层缓存命中缓存 sp<sub>part</sub><sub>hit</sub>=1,sp<sub>source</sub>=1：代表缓存中只有部分数据，没有命中的数据进行了回源操作。只有批量场景下才有这种情况

更多例子： [anycache 大盘](https://grafana.byted.org/d/NUKUt18Zz/anycache-da-pan?orgId=1)

日志：

```text
Notice 2020-02-11 16:00:00,736 v1(6) stats.go:49 10.8.63.143 live.room.config 202002111559560100140490351B595943 default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=622] method:MGet], elapsed: 74
Notice 2020-02-11 16:00:00,834 v1(6) stats.go:49 10.8.63.143 live.room.config 20200211160000010010023097051B4276 default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=703] method:MGet], elapsed: 51
Notice 2020-02-11 16:00:00,848 v1(6) stats.go:49 10.8.63.143 live.room.config 202002111600000100140440771978932E default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=20] method:MGet], elapsed: 54
Notice 2020-02-11 16:00:00,850 v1(6) stats.go:49 10.8.63.143 live.room.config 202002111600000100110611061E1B4DAC default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=696] method:MGet], elapsed: 26
```

日志中包含了 metrics 中的全部数据，以及额外的耗时、key 等信息。有助于排查问题



## 核心模块

核心模块包含两个接口， `Fetcher` 和 `BatchFetcher`

通过部分实现或全部实现了上述两个接口进行缓存。

如果用户有其他的对象需要缓存，实现上面两个接口，也可以很方便的嵌入到 anycache 中

## 路线图

-   [X] 基于 Fetcher、BatchFetcher 接口，实现核心逻辑
-   [X] 基于 BatchLoader 接口提供批量回源功能
-   [X] 提供函数缓存语法糖
-   [X] 通过 option 的方式支持 只读缓存、优先回源，回源失败读缓存、默认优先读缓存，缓存失败回源
-   [X] 缓存监控
-   [X] 支持设置是否缓存 nil
-   [X] 单机回源支持 single flight
-   [X] 提供 LFU 缓存组件
-   [ ] 全局 single flight
-   [X] 批量的情况下，用户如果不关心 nil，提供 filter nil 的 option

### 对比

>需求：
>
>1. 按主键 id 在 db 中索引数据
>2. db 抗不住全部流量，需要加缓存

*可能是如下的写法 ：*

``` go
type User struct {
	Id int `...`
	...
}
// 直接访问 DB，通过 id 查询记录
func SelectIn(ids ...int) ([]*User, error) {
	var result []*User
	db, _ := dbH.GetConnection()

	err := db.Debug().Where("id in (?)", ids).Find(&result).Error
	return result, err
}

// 带缓存的访问方式，优先读缓存，缓存没有命中再读 DB
func GetUserByIds(ids []int) []*User {
	itemList := make([]*User, len(ids))

	redisCli := GetRedisCli()
	pipe := redisCli.Pipeline()
	defer pipe.Close()

	// 批量去 redis 拿数据
	resList := make([]*redis.StringCmd, len(ids))
	for i, id := range ids {
		key := formatKey(id)
		resList[i] = pipe.Get(key)
	}

	// 如果执行成功，key 存在并且 value 解析成功，就加入到 itemList 中；否则把 id 加入到 missedIds 中
	var missedIds []int
	_, err := pipe.Exec()
	if err == nil {
		for i, res := range resList {
			if data, err := res.Bytes(); err == nil {
				item := new(User)
				if err := json.Unmarshal(data, &item); err == nil {
					// 这里为了简化，使用了 append，丢失了 ids 和 itemList 的映射关系，itemList 变成了乱序
					itemList = append(itemList, item)
				} else {
					missedIds = append(missedIds, ids[i])
				}
			} else {
				missedIds = append(missedIds, ids[i])
			}
		}
	} else {
		missedIds = ids
	}

	// 如果有未命中缓存的 id，就去 db 读取数据。读取成功后，回写到 redis 中
	if len(missedIds) > 0 {
		if items, err := SelectIn(missedIds...); err == nil {
			itemList = append(itemList, items...)

			pipe1 := redisCli.Pipeline()
			defer pipe1.Close()

			for _, item := range items {
				key := formatKey(item.Id)
				if data, err := json.Marshal(item); err == nil {
					pipe1.Set(key, data, 10*time.Second)
				}
				if pipe1.CmdNum() > 0 {
					pipe1.Exec()
				}
			}
		}
	}

	return itemList
}

// 格式化缓存的 key
func formatKey(id int) string {
	return fmt.Sprintf("user:%d", id)
}

func main() {
	res := GetUserByIds(1, 2, 3)
}
```

有一点繁琐，但是还在可控范围。各位新老司机写起来还是比较稳当的

--- 

*看看 anycache 的处理方式：*

``` go
func main() {
	cache:=anycache.NewDefault().WithoutLog().BuildBatchFetcherBySliceBatchLoader(func(ctx context.Context, item interface{}) string {
		// 根据入参获取缓存 key
		return fmt.Sprintf("user:%d", item)
	}, func(ctx context.Context, item interface{}) string {
		// 根据结果获取缓存 key
		return fmt.Sprintf("user:%d", item.(*User).Id)
	}, func(ctx context.Context, missedItems interface{}) (interface{}, error) {
		// miss 时回源
		ids := missedItems.([]int)
		
		// 请求源数据
		return SelectIn(ids)
	})

	var res []*User
	// 这里内部实现了先查缓存再回源的逻辑，最终把数据通过 res 返回给用户
	from, err := cache.MGet(context.Background(), []int{1, 2, 3}, &res)
	if err != nil {
		// handler error
	}
}
```

看起来简单了一些，但是优势还不明显～

---

>随着代码迭代，又有了新增需求：
>
>3. 一层缓存不够，要使用 localcache+redis 的两层缓存
>4. 想看看缓存的命中率怎么样
>5. 其他的数据表也想使用这样的缓存方式

手动实现就变得比较复杂了。。这个例子有点复杂我就不写了。。大家脑补下，觉得复杂的老铁把复杂打在公屏上

*再看看 anycache 的实现方式：*

``` go
// 无痛替换缓存为双层缓存
// cache = anycache.New(cache.MustNewLocalByteCacheLRU(1), codec.NewJson(codec.JsonImpStd)).BuildXXX(...)
cache = anycache.New(cache.NewMultiLevelCache(MustNewLocalByteCacheLRU(1), NewRedisWrap(testcase.GetRedisCli())), codec.NewJson(codec.JsonImpStd)).BuildXXX(...)

// 如果要替换序列化方式，也是简单的很
```

anycache 自带命中率统计；本身是基于反射实现的，其他库表如果要用，先注册后使用即可。

通过 anycache 调用还有一个特性：返回值和参数是**一一对应**的、**有序**的，用起来更方便。
