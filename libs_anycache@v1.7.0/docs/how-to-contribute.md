<!-- markdown-toc start - Don't edit this section. Run M-x markdown-toc-refresh-toc -->
**Table of Contents**

- [作为开发者](#作为开发者)
- [作为使用者](#作为使用者)
    - [规范](#规范)

<!-- markdown-toc end -->
# 作为开发者

TODO

# 作为使用者

第一次使用一个库，大家可能会写一些 demo 来验证功能。写完的 demo 一般不会进入正式的代码仓库，每个人写一遍 demo 存在脑力浪费的问题，所以希望大家能把 自己写的 demo 保存下来，作为实例供后续使用的同学参考。

## 规范

1.  位于项目根目录 examples 文件夹中
2.  命名格式为：example<sub>foo</sub><sub>bar</sub><sub>test.go</sub>，其中 foo<sub>bar</sub> 替换为自己想要的名字，建议与示例内容相关
3.  example<sub>foo</sub><sub>bar</sub><sub>test.go</sub> 内没有 main 方法，可执行方法的命名方式为：func ExampleFooBar()，goland 可以直接运行
4.  期望输出写在 ExampleFooBar 的函数体最下方。参考 [go-examples](https://blog.golang.org/examples)，不想看的话直接参考已有的 example 也可以
