<!-- markdown-toc start - Don't edit this section. Run M-x markdown-toc-refresh-toc -->
**Table of Contents**

- [Document](#document)
    - [缓存类型](#缓存类型)
        - [函数缓存](#函数缓存)
            - [简单函数-BuildFetcherByLoader](#简单函数-BuildFetcherByLoader)
            - [批量函数-BuildBatchFetcherByBatchLoader](#批量函数-BuildBatchFetcherByBatchLoader)
            - [批量请求单个函数-BuildBatchFetcherByLoader](#批量请求单个函数-BuildBatchFetcherByLoader)
    - [初始化](#初始化)
        - [缓存组件](#缓存组件)
        - [序列化组件](#序列化组件)
        - [缓存选项](#缓存选项)
            - [ttl 和 delTTL](#ttl-和-delttl)
            - [maxFetchitemsCount](#maxfetchitemscount)
            - [sourceStragety](#sourcestragety)
    - [日志和监控](#日志和监控)
        - [日志](#日志)
        - [grafana 大盘](#grafana-大盘)

<!-- markdown-toc end -->
# Document


### 函数缓存

#### 简单函数-BuildFetcherByLoader

> 定义：不对函数返回的结果做处理，整体缓存

例子：获取全部配置信息（函数签名`func() Config`)， 整体缓存配置信息

[example_fetch_loader_test.go](https://code.byted.org/webcast/libs_anycache/blob/master/examples/example_fetch_loader_test.go)

#### 批量函数-BatchFetcherBathceLoader

> 定义：有批量请求的需求，并且源函数支持批量请求

例子：批量获取用户信息（函数签名`func(uidList) []UserInfo`），将 uid 和对应的 userInfo 一一对应缓存

优势是，当开发者请求 uidList=[1, 2, 3]的用户信息，如果 1 和 2 在缓存中存在但是 3 不存在，那么只会回源 uid=3 获取用户信息。有利于提升缓存命中率，减少回源

[example_batchfetcher_batchloader_test.go](https://code.byted.org/webcast/libs_anycache/blob/master/examples/example_batchfetcher_batchloader_test.go)

#### 批量请求单个函数-BatchFetcherLoader

> 定义：有批量请求的需求，但是源函数不支持批量请求

例子：批量获取用户信息（函数签名`func(uid) UserInfo`），将 uid 和对应的 userInfo 一一对应缓存

优势是，当开发者请求 uidList=[1, 2, 3]的用户信息，直接支持批量并发请求；同时也能提升缓存命中率，减少回源

[example_batchfetcher_loader_test.go](https://code.byted.org/webcast/libs_anycache/blob/master/examples/example_batchfetcher_loader_test.go)


## 初始化

`func New(cacheIns cache.Cacher, codecIns codec.Codecer) *AnyCache`

由缓存组件和序列化组件组成 anycache

### 缓存组件

提供字节缓存的能力

自带的缓存实现：
- [ies_bytescache](https://code.byted.org/gopkg/localcache)本地缓存
- redis 和支持重试的 redis 实现
- [smart-cache](https://bytedance.feishu.cn/wiki/wikcnILO7B57OOv0vLBdVryGUlF?new_source=message)

### 序列化组件

提供序列化和反序列化的能力

自带的序列化实现：
- json，包括标准库实现、json-iterator default-mode、json-iterator fast-mode
- msgpack，包括[msgp](https://github.com/tinylib/msgp)和[msgpack](github.com/vmihailenco/msgpack/v4)

### 缓存选项

```go
ns                 string         // 命名空间，默认为 default
ttl                time.Duration  // 逻辑过期，但不会物理删除，默认 1 分钟
delTTL             time.Duration  // 物理删除，默认 1 分钟
maxFetchItemsCount int            // 批量回源函数最多允许一次请求多少个 item，默认 50
sourceStrategy     sourceStrategy // 回源策略，默认优先缓存
validNil           bool           // 是否缓存 nil，默认缓存
sampleFn           sampleFunc     // 采样函数，结果为 true 表示需要缓存，默认不采样全部缓存
filterNil          bool           // 是否过滤 nil，默认不过滤
asyncSaveCache     bool           // 是否启用异步保存缓存，默认不启用
needLog            bool           // 是否需要打日志，默认打印日志
noCacheOverhead    bool           // cache 不加 overhead，默认加
```

上述选项都提供了对应的 WithXXXX 选项

#### ttl 和 delTTL
ttl 表示逻辑过期时间，delTTL 表示物理过期时间。如果 ttl 过期，缓存组件内部可能还是能读到数据，但是数据已经过期，此时也会触发回源逻辑；delTTL 表示物理过期，缓存组件不会再读到数据。

不管是物理过期还是逻辑过期，对开发者而言都是过期，只是 anycache 内部实现上有区分。

#### maxFetchitemsCount

最大回源数量，例如某个源函数支持批量请求，但每次最多允许请求 10 个数据，设置这个选项后，anycache 内部会自动将请求分组，每组最多 10 个数据，并发回源。

#### sourceStragety

缓存策略，默认优先缓存

```go
const (
	SsCacheFirst                = iota // 优先缓存，缓存失败回源
	SsSourceFirst                      // 优先回源，回源失败走缓存
	SsOnlyCache                        // 不回源，只读缓存
	SsOnlySource                       // 只回源，不读缓存
	SsExpiredDataBackup                // 优先缓存；miss（过期或不存在） 后回源；回源失败，如果缓存是过期导致 miss，使用过期缓存兜底
	SsExpiredDataAndAsyncSource        // 优先缓存，如果缓存过期返回过期的缓存，同时异步回源；如果缓存 miss，同步回源
)
```



## 日志和监控

### 日志

```text
Notice 2020-02-11 16:00:00,736 v1(6) stats.go:49 10.8.63.143 live.room.config 202002111559560100140490351B595943 default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=622] method:MGet], elapsed: 74
Notice 2020-02-11 16:00:00,834 v1(6) stats.go:49 10.8.63.143 live.room.config 20200211160000010010023097051B4276 default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=703] method:MGet], elapsed: 51
Notice 2020-02-11 16:00:00,848 v1(6) stats.go:49 10.8.63.143 live.room.config 202002111600000100140440771978932E default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=20] method:MGet], elapsed: 54
Notice 2020-02-11 16:00:00,850 v1(6) stats.go:49 10.8.63.143 live.room.config 202002111600000100110611061E1B4DAC default canary anycache stat path: 1, info: map[cache_level:0 keys:[default:shield_type:id=696] method:MGet], elapsed: 26
```

日志中包含了 metrics 中的全部数据，以及额外的耗时、key 等信息。有助于排查问题


### grafana 大盘

[anycache 大盘](https://grafana.byted.org/d/NUKUt18Zz/anycache-da-pan?orgId=1)

目前支持的 metrics 指标有：

1.  `method`，anycache 的方法名
2.  `sp_hit`，是否命中缓存
3.  `sp_part_hit`，是否部分命中缓存
4.  `sp_source`，是否回源
5.  `cache_level`，代表读取缓存的层数。一层缓存的情况下，始终等于 0
