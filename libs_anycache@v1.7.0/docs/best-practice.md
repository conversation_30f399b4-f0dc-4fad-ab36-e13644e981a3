<!-- markdown-toc start - Don't edit this section. Run M-x markdown-toc-refresh-toc -->
**Table of Contents**

- [Best practice](#best-practice)
    - [白名单服务使用 anycache 缓存白名单元信息](#白名单服务使用-anycache-缓存白名单元信息)
    - [用户服务使用 anycache 提高容灾能力](#用户服务使用-anycache-提高容灾能力)
    - [榜单服务使用 anycache 缓存高热数据，提高服务稳定性](#榜单服务使用-anycache-缓存高热数据提高服务稳定性)
    - [webcast.user.pack 误用对象缓存，影响服务稳定性](#webcastuserpack-误用对象缓存影响服务稳定性)

<!-- markdown-toc end -->
# Best practice

## 白名单服务使用 anycache 缓存白名单元信息

白名单服务代表的场景是：请求量很大，每次回源抗不住；允许一定延迟

使用了 anycache 的 DB 类缓存，使用 redis+lc 的缓存组件组合，采用缓存优先的缓存策略。

接入收益：

1.  代码更加精简，业务无关的代码更少，更加专注业务逻辑

[接入前](https://code.byted.org/live/config/blob/master/model/sheildTypeCache.go) [接入后](https://code.byted.org/live/config/blob/master/model/newSheildTypeCache.go)

1.  提供缓存监控 [grafana](https://grafana.byted.org/d/NUKUt18Zz/anycache-da-pan?orgId=1&var-psm=live.room.config&var-bosunsource=bosun&var-datasource=bytetsd&var-cache_level=All&var-sp_hit=All&var-sp_part_hit=All&var-sp_source=All)，实时了解缓存运行状况。后续可添加报警监控



## 用户服务使用 anycache 提高容灾能力

用户服务是典型的 proxy 类型的服务，即本身不存储数据，数据来源是下游服务。proxy 类型服务的稳定性通常受下游影响很大，下游稳定性是上游稳定性的上限

使用了 anycache 的函数缓存，使用 redis 缓存组件，采用回源优先的缓存策略。

接入收益：

1.  下游故障期间，读接口可以使用缓存数据，减少下游稳定性对本服务稳定性的影响

2.  [接入代码](https://code.byted.org/webcast/user/blob/master/demotion/user_info.go) 代码精简，接入成本低

3.  后期如果发现热数据明显，可以很方便的将 redis 组件替换为 redis+lc 的组件



## 榜单服务使用 anycache 缓存高热数据，提高服务稳定性

榜单的热数据明显，并且服务稳定性要求很高

使用了 anycache 的函数缓存，使用 lc 缓存组件，采用过期数据做 backup 的缓存策略。



## webcast.user.pack 误用对象缓存，影响服务稳定性

由于对象缓存省去了序列化反序列化，保留了对象原来的类型信息，可以直接通过断言得到原对象，因此使用起来更简单。

webcast.user.pack 直接使用了 gcache 缓存 RPC 结果。但是由于 gcache 是对象缓存，当缓存的数据非常多时， 会对 gc 造成很大的压力。表现为接口时延有毛刺、晚高峰接口时延上升、CPU 监控抖动严重等，晚高峰服务稳定性下 降到两个 9 。

使用 anycache（freecache + json-iterator）改造后，基本解决了 gc 问题，晚高峰服务稳定性也能维持在四个 9 以上，接口时延稳定，CPU 使用率稳定。

同时，代码也更加清晰。
