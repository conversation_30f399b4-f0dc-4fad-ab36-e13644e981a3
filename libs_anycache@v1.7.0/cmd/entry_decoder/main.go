package main

import (
	"bufio"
	"context"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"code.byted.org/webcast/libs_anycache/plugin/cache/base"
)

func main() {
	scanner := bufio.NewScanner(os.Stdin)
	scanner.Scan()

	text := strings.TrimPrefix(scanner.Text(), "0x")
	data, err := hex.DecodeString(text)
	if err != nil {
		fmt.Fprintf(os.Stderr, "decode string faied: err: %v\n", err)

		os.Exit(1)
	}

	entry, err := base.DeserializeEntryNew(context.TODO(), "fake_key", data)
	if err != nil {
		fmt.Fprintf(os.Stderr, "failed to decode entry, err: %v", err)

		os.Exit(1)
	}

	s, err := prettyPrintJSON(string(entry.GetVal()))
	if err != nil {
		os.Exit(1)
	}

	fmt.Println(s)
}

func prettyPrintJSON(s string) (string, error) {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(s), &data)
	if err != nil {
		return "", err
	}

	b, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return "", err
	}

	return string(b), nil
}
