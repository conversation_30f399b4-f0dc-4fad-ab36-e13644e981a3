## Changelog

## [v1.5.4](https://code.byted.org/webcast/libs_anycache/tags/v1.5.4)

* [issue_14](https://code.byted.org/webcast/libs_anycache/issues/14)

* [issue_15](https://code.byted.org/webcast/libs_anycache/issues/15)

* upgrade goredis 

## [v1.5.3](https://code.byted.org/webcast/libs_anycache/tags/v1.5.3)

### Fix

* 未设置 namespace 时，metrics 默认使用 none，确保大盘显示正常

## [v1.5.2](https://code.byted.org/webcast/libs_anycache/tags/v1.5.2)

### Fix

* 升级 webcast/libs_sync，解决并发调用时 BatchProcess 会 panic 的问题

## [v1.5.1](https://code.byted.org/webcast/libs_anycache/tags/v1.5.1)

### Fix

* 多层缓存会不断刷新上层缓存的数据，是本地缓存 TTL 比预期长，极端情况下本地缓存不过期


## [v1.5.0](https://code.byted.org/webcast/libs_anycache/tags/v1.5.0)

### Added

* 新增回源策略：优先缓存，如果缓存逻辑过期就返回过期的缓存，同时异步回源；如果缓存 miss 或物理过期，同步回源。适合对延迟更敏感的场景

* 新增缓存组件：[bigkey_redis](https://code.byted.org/webcast/libs_anycache/tags/plugin%2Fcache%2Fbigkey_redis%2Fv0.0.1)， 对 redis 大 key 进行分片存储

### Removed

* 移除 smartcache；如有需求可以使用原生的 smartcache SDK

## [v1.4.10](https://code.byted.org/webcast/libs_anycache/tags/v1.4.10)

### Changed

* 回源优先策略，如果回源失败并且本地数据不全，需要把回源错误返回给用户 [issue_11](https://code.byted.org/webcast/libs_anycache/issues/11)

### Added

* 支持根据回源结果自定义采样缓存

## [v1.4.9](https://code.byted.org/webcast/libs_anycache/tags/v1.4.9)

### Fix

* [issue_9](https://code.byted.org/webcast/libs_anycache/issues/9)


## [v1.4.8](https://code.byted.org/webcast/libs_anycache/tags/v1.4.8)

### Fix

* [issue_7](https://code.byted.org/webcast/libs_anycache/issues/7)


## [v1.4.7](https://code.byted.org/webcast/libs_anycache/tags/v1.4.7)

### Changed

* 升级 redis 依赖，避免 redis tag 删除导致编译不过


## [v1.4.6](https://code.byted.org/webcast/libs_anycache/tags/v1.4.6)

### Fix

* 多层缓存，localcache key 不过期

## [v1.4.5](https://code.byted.org/webcast/libs_anycache/tags/v1.4.5)

### Changed

* wrap 回源函数，避免回源函数 panic 导致 goroutine hang 死

## [v1.4.4](https://code.byted.org/webcast/libs_anycache/tags/v1.4.4)

### Changed

* upgrade gopkg/localcache

## [v1.4.3](https://code.byted.org/webcast/libs_anycache/tags/v1.4.3)

### Changed

* support json raw message
* set/delete support dynamicOption
* support stress flow
* refactor cache 

### Fix

* mGetFromLoader: assign wrong value to result

## [v1.4.2](https://code.byted.org/webcast/libs_anycache/tags/v1.4.2)

### Changed

* use gopkg/localcache instead of iespkg/localcache

## [v1.4.1](https://code.byted.org/webcast/libs_anycache/tags/v1.4.1)

### Deprecated

* deprecated smartcache


## [v1.4.0](https://code.byted.org/webcast/libs_anycache/tags/v1.4.0)

### Added

* WithReturnFastWhenCacheErr：fast return in case of cache error

## [v1.3.0](https://code.byted.org/webcast/libs_anycache/tags/v1.3.0)

### Added

* new cache: [vfastcache](https://code.byted.org/videoarch/vfastcache)
* add info log if internal error happens 

## [v1.2.0](https://code.byted.org/webcast/libs_anycache/tags/v1.2.0)

### Added

* new api: refresh2, return new value

## [v1.1.0](https://code.byted.org/webcast/libs_anycache/tags/v1.1.0)

### Added 

* new api: refresh2, return new value

## [v1.0.0](https://code.byted.org/webcast/libs_anycache/tags/v1.0.0) - 2020-05-24

### Changed

__notice this version is not compatible for early version__

#### modidy api name for user to understand easily

__Interface__： 
 
 * Fetcher  -> Loader 
 * MultiFetcher -> BatchLoader
 * DerivedFetcher -> Fetcher
 * DerivedMulitFetcher -> BatchFetcher
 
 __func__ :

 * func (*AnyCache)BuildForSimpleFunc -> func (*AnyCache)BuildFetcherByLoader
 * func (*AnyCache)BuildForSingleFunc -> func (*AnyCache)BuildBatchFetcherByLoader
 * func (*AnyCache)BuildForMultiFuncRetMap -> func (*AnyCache)BuildBatchFetcherByBatchLoader
 * func (*AnyCache)BuildForMultiFuncRetSlice -> func (*AnyCache)BuildBatchFetcherBySliceBatchLoader
 * func WithNoOverhead -> func WithoutHeader
 * func (ac *AnyCache) WithMaxFetchItemsCount -> func (ac *AnyCache) WithBatchSize
 * func (ac *AnyCache) WithSampleCache -> func (ac *AnyCache) WithCacheSampling
 * func (ac *AnyCache) WithFilterNil -> func (ac *AnyCache) WithFilterNilInSliceRet
 * func (ac *AnyCache) WithNoCacheOverhead -> func (ac *AnyCache) WithoutCacheHeader()
 
 __const__ :
 
 * type FromType int -> type ResultSource int
 * const FromSource -> const FromLoader
 * const FromPartSource -> const FromLoaderPartially
 
 #### remove useless func 
 
 * remove func (*AnyCache) WithLog, now suggest use func (ac *AnyCache) WithoutLog()
 * remove func (*AnyCache) WithoutNameSpace , now suggest use func (ac *AnyCache) WithNameSpace(ns string)
 
 #### modify default namespace 
 
 * default namespace "default" -> "", if you want Keep the namespace same as before， use WithNameSpace("default")
 


## [v0.9.0](https://code.byted.org/webcast/libs_anycache/tags/v0.9.0) - 2020-04-17

### Changed

* new api, easy to use
* add ci and Changelog

## [v0.8.1](https://code.byted.org/webcast/libs_anycache/tags/v0.8.1) - 2020-04-02

### Changed

* ssOnlySource will not cache result
* default ttl is one minute


## [v0.8.0](https://code.byted.org/webcast/libs_anycache/tags/v0.8.0) - 2020-03-27

### Added

* support smart-cache

## [v0.7.0](https://code.byted.org/webcast/libs_anycache/tags/v0.7.0) - 2020-03-26

### Added

* use iespkg/localcache-go as default localcache implementation

### Removed

* delete other localcache implementations

## [v0.6.0](https://code.byted.org/webcast/libs_anycache/tags/v0.6.0) - 2020-03-25

### Added

* support context

---

template

## vx.y.z - date

### Added

### Changed

### Deprecated

### Removed

### Fixed
