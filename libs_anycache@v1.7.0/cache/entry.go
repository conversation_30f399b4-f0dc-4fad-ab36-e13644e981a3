package cache

import (
	"context"
	"time"

	"code.byted.org/webcast/libs_anycache/plugin/cache/base"
)

type IEntry = base.IEntry

func NewEntry(key string, val []byte, ttl, delTTL time.Duration) IEntry {
	return base.NewEntry(key, val, ttl, delTTL)
}

func NewEntryNoOverhead(key string, val []byte, ttl time.Duration) IEntry {
	return base.NewEntryNoOverhead(key, val, ttl)
}

func NewEntryWithObject(key string, value interface{}, ttl, delTTL time.Duration) IEntry {
	return base.NewEntryWithObject(key, value, ttl, delTTL)
}

func deserializeEntryNew(ctx context.Context, key string, data []byte) (IEntry, error) {
	return base.DeserializeEntryNew(ctx, key, data)
}

func NewCustomTTLEntry(key string, val []byte, ttl, delTTL, customTtl, customDelTtl time.Duration) IEntry {
	return base.NewCustomEntry(key, val, ttl, delTTL, customTtl, customDelTtl)
}

func NewCustomTTLEntryWithObject(key string, value interface{}, ttl, delTTL, customTtl, customDelTtl time.Duration) IEntry {
	return base.NewCustomEntryWithObject(key, value, ttl, delTTL, customTtl, customDelTtl)
}
