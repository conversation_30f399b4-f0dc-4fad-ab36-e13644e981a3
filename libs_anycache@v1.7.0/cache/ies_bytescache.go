package cache

import (
	"context"
	"strconv"

	"code.byted.org/gopkg/localcache"
	"code.byted.org/gopkg/localcache/base"
	"code.byted.org/gopkg/localcache/contributes/vfastcache"
	"code.byted.org/gopkg/metrics"
	"code.byted.org/webcast/logw"
)

const anyCacheMetricsPrefix = "byted.anycache.sdk.bytescache"

type LocalBytesCache struct {
	bytesCache   localcache.BytesCache
	maxMemory    int64  // in bytes
	maxMemoryStr string // in bytes in string
	merticsCli   *metrics.MetricsClientV2
	cacheType    string
}

func (l *LocalBytesCache) Name() string {
	return "bytes_cache"
}

func (l *LocalBytesCache) Get(ctx context.Context, key string) (IEntry, error) {
	data, err := l.bytesCache.Get(ctx, key)
	if err != nil {
		if err == localcache.ErrKeyNotFound {
			return nil, ErrNotFound
		}
		return nil, err
	}

	entry, err := deserializeEntryNew(ctx, key, data)
	if err != nil {
		return nil, err
	}

	if entry.IsDecayed() && !needExpiredData(ctx) {
		return nil, ErrNotFound
	}

	return entry, nil
}

func (l *LocalBytesCache) Set(ctx context.Context, entry IEntry) error {
	if entry == nil {
		return nil
	}

	if l.merticsCli != nil {
		var ratio int
		if len(entry.GetVal()) != 0 && l.maxMemory != 0 {
			ratio = int(float64(len(entry.GetVal())) / float64(int(l.maxMemory)) * 1000000.0)
		}
		_ = l.merticsCli.EmitTimer("value_size_ratio", ratio,
			metrics.T{Name: "max_memory", Value: l.maxMemoryStr},
			metrics.T{Name: "cache_type", Value: l.cacheType},
		)
	}

	err := l.bytesCache.Set(ctx, entry.GetKey(), entry.Serialize(), entry.GetDelTTL())
	if err != nil {
		logw.CtxInfoKVs(ctx, "anycache: set kv failed", "key", entry.GetKey(), "err", err)
		l.merticsCli.EmitStore("kvset.failed", 1)
	}

	return err
}

func (l *LocalBytesCache) MGet(ctx context.Context, keys ...string) ([]IEntry, error) {
	entries := make([]IEntry, len(keys))
	for i := range keys {
		if entry, err := l.Get(ctx, keys[i]); err == nil {
			entries[i] = entry
		}
	}

	return entries, nil
}

func (l *LocalBytesCache) MSet(ctx context.Context, entries ...IEntry) error {
	for _, e := range entries {
		if err := l.Set(ctx, e); err != nil {
			return err
		}
	}

	return nil
}

func (l *LocalBytesCache) MDel(ctx context.Context, keys ...string) error {
	l.bytesCache.MDel(ctx, keys)
	return nil
}

func (l *LocalBytesCache) Clear() error {
	l.bytesCache.Close()
	return nil
}

// Deprecated: use MustNewLocalBytesCacheLRU or MustNewLocalBytesCacheLFU instead
func NewLocalBytesCache(bytesCache localcache.BytesCache) Cacher {
	return &LocalBytesCache{
		bytesCache:   bytesCache,
		maxMemoryStr: "-",
		merticsCli:   metrics.NewDefaultMetricsClientV2(anyCacheMetricsPrefix, true),
		cacheType:    "-",
	}
}

func NewLocalBytesCacheWithOpt(maxMemorySizeMB int, opt ...base.BytesCacheOption) Cacher {
	return &LocalBytesCache{
		bytesCache:   localcache.MustNewBytesCache(maxMemorySizeMB, localcache.LRU, opt...),
		maxMemory:    int64(maxMemorySizeMB) * 1024 * 1024,
		maxMemoryStr: strconv.FormatInt(int64(maxMemorySizeMB)*1024*1024, 10),
		merticsCli:   metrics.NewDefaultMetricsClientV2(anyCacheMetricsPrefix, true),
		cacheType:    "lru",
	}
}

// MustNewLocalBytesCacheLRU is a shortcut for LRU bytesCache
func MustNewLocalBytesCacheLRU(maxMemorySizeMB int, opt ...base.BytesCacheOption) Cacher {
	return &LocalBytesCache{
		bytesCache:   localcache.MustNewBytesCache(maxMemorySizeMB, localcache.LRU, opt...),
		maxMemory:    int64(maxMemorySizeMB) * 1024 * 1024,
		maxMemoryStr: strconv.FormatInt(int64(maxMemorySizeMB)*1024*1024, 10),
		merticsCli:   metrics.NewDefaultMetricsClientV2(anyCacheMetricsPrefix, true),
		cacheType:    "lru",
	}
}

// MustNewLocalBytesCacheLRU is a shortcut for LFU bytesCache
func MustNewLocalBytesCacheLFU(maxMemorySizeMB int, opt ...base.BytesCacheOption) Cacher {
	return &LocalBytesCache{
		bytesCache:   localcache.MustNewBytesCache(maxMemorySizeMB, localcache.LFU, opt...),
		maxMemory:    int64(maxMemorySizeMB) * 1024 * 1024,
		maxMemoryStr: strconv.FormatInt(int64(maxMemorySizeMB)*1024*1024, 10),
		merticsCli:   metrics.NewDefaultMetricsClientV2(anyCacheMetricsPrefix, true),
		cacheType:    "lfu",
	}
}

// MustNewLocalBytesCacheAPU is a shortcut for vfastcache
// default: shardAmount=16; blockSize=1024; shardCapacity=maxMemorySizeMB/blockSize/shardAmount
func MustNewLocalBytesCacheARC(maxMemorySizeMB int, blockSize int, opt ...base.BytesCacheOption) Cacher {
	return &LocalBytesCache{
		bytesCache: localcache.MustNewBytesCache(
			maxMemorySizeMB, localcache.Specific, append(opt, vfastcache.WithCustomBytesCache(blockSize))...,
		),
		maxMemory:    int64(maxMemorySizeMB) * 1024 * 1024,
		maxMemoryStr: strconv.FormatInt(int64(maxMemorySizeMB)*1024*1024, 10),
		merticsCli:   metrics.NewDefaultMetricsClientV2(anyCacheMetricsPrefix, true),
		cacheType:    "arc",
	}
}
