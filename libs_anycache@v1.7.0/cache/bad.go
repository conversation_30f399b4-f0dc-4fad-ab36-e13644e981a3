package cache

import (
	"context"
)

type Bad struct {
	bad int
	Cacher
}

const (
	BadGet = 1 << iota
	BadSet
	BadMGet
	BadMSet
	BadMDel

	BadAll  = 0xFF
	BadNone = 0x00
)

func newBad(bad int) Cacher {
	return &Bad{
		bad:    bad,
		Cacher: MustNewLocalBytesCacheLRU(1),
	}
}

func (b *Bad) Name() string {
	return "bad_cache"
}

func (b *Bad) Get(ctx context.Context, key string) (IEntry, error) {
	if b.bad&BadGet != 0 {
		return nil, ErrNotFound
	}
	return b.Cacher.Get(ctx, key)
}

func (b *Bad) Set(ctx context.Context, entry IEntry) error {
	if b.bad&BadSet != 0 {
		return ErrInternalErr
	}
	return b.Cacher.Set(ctx, entry)
}

func (b *Bad) MGet(ctx context.Context, keys ...string) ([]IEntry, error) {
	if b.bad&BadMGet != 0 {
		entries := make([]IEntry, len(keys))
		return entries, nil
	}
	return b.Cacher.MGet(ctx, keys...)
}

func (b *Bad) MSet(ctx context.Context, entries ...IEntry) error {
	if b.bad&BadMSet != 0 {
		return ErrInternalErr
	}
	return b.Cacher.MSet(ctx, entries...)
}

func (b *Bad) MDel(ctx context.Context, keys ...string) error {
	if b.bad&BadMDel != 0 {
		return ErrInternalErr
	}
	return b.Cacher.MDel(ctx, keys...)
}
