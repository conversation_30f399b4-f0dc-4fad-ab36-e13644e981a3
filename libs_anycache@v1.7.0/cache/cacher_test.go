package cache

import (
	"context"
	"testing"
	"time"

	"code.byted.org/gopkg/localcache"
	"code.byted.org/webcast/libs_anycache/testcase"
	"github.com/stretchr/testify/assert"
)

func TestEntry(t *testing.T) {
	var data1, data2 []byte

	{
		e := NewEntry("hello", []byte("world"), time.Second, 0)

		data1 = e.Serialize()
		ee, err := deserializeEntryNew(context.TODO(), e.<PERSON>(), data1)
		assert.NoError(t, err)
		assert.Equal(t, e.<PERSON>(), ee.<PERSON><PERSON><PERSON>())
		assert.Equal(t, e.<PERSON>al(), ee.GetVal())

		assert.Equal(t, false, ee.IsDecayed())
		time.Sleep(time.Second)
		assert.Equal(t, true, ee.IsDecayed())
	}

	{
		e := NewEntryNoOverhead("hello", []byte("world"), time.Second)

		data2 = e.<PERSON>ialize()
		ee, err := deserializeEntryNew(WithoutHeader(context.Background()), e.<PERSON>(), data2)
		assert.NoError(t, err)
		assert.Equal(t, e.<PERSON>(), ee.<PERSON><PERSON>())
		assert.Equal(t, e.GetVal(), ee.GetVal())

		assert.Equal(t, false, ee.IsDecayed())
	}

	assert.NotEqual(t, data1, data2)
	assert.Equal(t, data1[len(data1)-len(data2):], data2)
}

func TestCache(t *testing.T) {
	cacheList := map[string]Cacher{
		"NewRedisWrap":             NewRedisWrap(testcase.GetRedisCli()),
		"NewRedisWithRetry":        NewRedisWithRetry(testcase.GetRedisCli()),
		"IesLocalByteCacheLRU":     NewLocalBytesCache(localcache.MustNewBytesCache(1, localcache.LRU)),
		"IesLocalByteCacheLFU":     NewLocalBytesCache(localcache.MustNewBytesCache(1, localcache.LFU)),
		"MustIesLocalByteCacheLRU": MustNewLocalBytesCacheLRU(1),
		"MustIesLocalByteCacheLFU": MustNewLocalBytesCacheLFU(1),
		"NewIesLocalByteCacheAPC":  MustNewLocalBytesCacheARC(1, 1024),
		// "NewMultiLevelCache":        NewMultiLevelCache(MustNewLocalBytesCacheLRU(1), NewRedisWrap(testcase.GetRedisCli())),
		// "NewMultiLevelCacheWithBad": NewMultiLevelCache(newBad(BadGet|BadMGet), NewRedisWrap(testcase.GetRedisCli())),
	}

	ctx := context.Background()
	ctx = WithRetryOptions(ctx, 3, time.Second)
	for name, ins := range cacheList {
		t.Run(name, func(t *testing.T) {
			testCache(t, ctx, name, ins)
		})
	}
}

func testCache(t *testing.T, ctx context.Context, name string, ins Cacher) {
	k, v := name+"get", []byte(name+"world")
	err := ins.Set(ctx, NewEntry(k, v, time.Second, 0))
	assert.NoError(t, err)
	entry, err := ins.Get(ctx, k)
	if assert.NoError(t, err) {
		assert.Equal(t, v, entry.GetVal())
	}

	mk, mv := name+"mget", []byte(name+"world")
	err = ins.MSet(ctx, NewEntry(mk, mv, 10*time.Second, 0))
	assert.NoError(t, err)
	entries, err := ins.MGet(ctx, mk)
	if assert.NoError(t, err) {
		assert.Equal(t, mv, entries[0].GetVal())
	}

	err = ins.MDel(ctx, mk)
	assert.NoError(t, err)
	entries, err = ins.MGet(ctx, mk)
	if assert.NoError(t, err) {
		assert.Nil(t, entries[0])
	}

	time.Sleep(1500 * time.Millisecond)

	entry, err = ins.Get(ctx, k)
	assert.Equal(t, ErrNotFound, err)
	assert.Nil(t, entries[0])
}

// func TestSmartCache(t *testing.T) {
// 	assert.Panics(t, func() {
// 		smartcache.NewSmartCache(testcase.RedisFakePSM)
// 	})

// 	smartcache.InitSmartCache(testcase.RedisFakePSM)

// 	assert.NotPanics(t, func() {
// 		smartcache.NewSmartCache(testcase.RedisFakePSM)
// 		smartcache.NewSmartCacheWithOption(testcase.RedisFakePSM, goredis.NewOption())
// 	})
// 	ctx := context.Background()
// 	ctx = WithRetryOptions(ctx, 3, time.Second)
// 	name, cli := "smartCache", smartcache.MustNewSmartCache(testcase.RedisFakePSM)
// 	testCache(t, ctx, name, cli)
// }
