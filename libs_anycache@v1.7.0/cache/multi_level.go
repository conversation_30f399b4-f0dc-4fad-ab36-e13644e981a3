package cache

import (
	"context"
	"math"
	"strconv"
	"time"

	"code.byted.org/webcast/libs_anycache/stat"
)

// 多级缓存，也要实现 Cacher 接口
type MultiLevelCache struct {
	mCacher []Cacher
	ttl     time.Duration
	ttlStep float64
}

func NewMultiLevelCache(mc ...Cacher) Cacher {
	return &MultiLevelCache{
		mCacher: mc,
		ttlStep: 1.3,
	}
}

func NewMultiLevelCacheWithTTLStep(ts float64, mc ...Cacher) Cacher {
	return &MultiLevelCache{
		ttlStep: ts,
		mCacher: mc,
	}
}

// 刷新 entry 中的创建时间和ttl，以当前层为基准， 每层之间差 ttlStep 倍
// 这样做的原因是：上层过期后，下层数据还可用，避免一起过期，直接回源
func refreshEntry(ctx context.Context, old IEntry, targetLevel int, ttlStep float64) IEntry {
	if old == nil {
		return old
	}
	ttl, delTTL, ttlStepPtr := runtimeTTL(ctx)
	if HasCustomTTL(ctx) {
		if old.GetCustomTtl() > 0 && old.GetCustomDelTTL() > 0 {
			ttl, delTTL = old.GetCustomTtl(), old.GetCustomDelTTL()
		}
	}
	// FIXME: 不允许缓存不过期
	if ttl == 0 {
		ttl = time.Second
	}
	if delTTL < ttl {
		delTTL = ttl
	}
	// 忽略无效值
	if ttlStepPtr != nil && *ttlStepPtr > 0.0 {
		ttlStep = *ttlStepPtr
	}

	ttl_f, delTTL_f := float64(ttl), float64(delTTL)
	factor := math.Pow(ttlStep, float64(targetLevel))
	ttl_f *= factor
	delTTL_f *= factor

	return old.Refresh(time.Duration(ttl_f), time.Duration(delTTL_f))
}

func mRefreshEntry(ctx context.Context, oldEntries []IEntry, targetLevel int, ttlStep float64) []IEntry {
	newEntries := make([]IEntry, len(oldEntries))
	for i, e := range oldEntries {
		newEntries[i] = refreshEntry(ctx, e, targetLevel, ttlStep)
	}

	return newEntries
}

func (m *MultiLevelCache) Name() string {
	return "multi_level_cache"
}

func (m *MultiLevelCache) Get(ctx context.Context, key string) (entry IEntry, err0 error) {
	var level int
	for i, cache := range m.mCacher {
		e, err := cache.Get(ctx, key)
		// 只要没报错，就先暂存结果，用于下一层缓存失败时做兜底
		if err != nil {
			continue
		}
		level = i
		entry = e

		// 数据没过期
		if !e.IsDecayed() {
			break
		}
	}

	// 缓存存在
	if entry != nil {
		stat.WithMetrics(ctx, stat.TagCacheLevel, strconv.Itoa(level))

		// 并且没有过期，再刷新上层缓存
		if !entry.IsDecayed() && level > 0 {
			for j := 0; j < level; j++ {
				_ = m.mCacher[j].Set(ctx, refreshEntry(ctx, entry, j, m.ttlStep))
			}
		}
		return entry, nil
	}

	return nil, ErrNotFound
}

func (m *MultiLevelCache) Set(ctx context.Context, entry IEntry) error {
	// 倒序更新
	for i := len(m.mCacher) - 1; i >= 0; i-- {
		_ = m.mCacher[i].Set(ctx, refreshEntry(ctx, entry, i, m.ttlStep))
	}

	return nil
}

func (m *MultiLevelCache) MGet(ctx context.Context, keys ...string) ([]IEntry, error) {
	entries := make([]IEntry, len(keys))

	iterKeys := keys
	indexList := make([]int, len(keys))
	for i := range keys {
		indexList[i] = i
	}

	var level int
	levelEntries := map[int][]IEntry{}
	for i, cache := range m.mCacher {
		if len(iterKeys) == 0 {
			break
		}
		level = i

		tmpEntries, err := cache.MGet(ctx, iterKeys...)
		if err != nil {
			continue
		} else {
			var tmpKeys []string
			var tmpIndexList []int
			for j, e := range tmpEntries {
				// 只要数据存在就先存下来
				if e != nil {
					entries[indexList[j]] = e

					// 数据虽然存在，但是过期了，继续往下层找
					if e.IsDecayed() {
						tmpKeys = append(tmpKeys, iterKeys[j])
						tmpIndexList = append(tmpIndexList, indexList[j])
					} else {
						// 数据存在且没过期，才是本层获取到的 entry，用于给上层更新
						levelEntries[i] = append(levelEntries[i], e)
					}
				} else {
					tmpKeys = append(tmpKeys, iterKeys[j])
					tmpIndexList = append(tmpIndexList, indexList[j])
				}
			}
			iterKeys = tmpKeys
			indexList = tmpIndexList
		}
	}
	stat.WithMetrics(ctx, stat.TagCacheLevel, strconv.Itoa(level))

	// 本层需要更新的数据=底层每一层的数据之和
	updateLevelEntries := map[int][]IEntry{}
	for i := 0; i < len(m.mCacher); i++ {
		for j := i + 1; j < len(m.mCacher); j++ {
			// level i 的ttl
			updateLevelEntries[i] = append(updateLevelEntries[i], levelEntries[j]...)
		}
	}

	// 更新上层缓存
	for i, updateEntries := range updateLevelEntries {
		if len(updateEntries) > 0 {
			newEntries := mRefreshEntry(ctx, updateEntries, i, m.ttlStep)
			_ = m.mCacher[i].MSet(ctx, newEntries...)
		}
	}

	return entries, nil
}

func (m *MultiLevelCache) MSet(ctx context.Context, entries ...IEntry) error {
	// 倒序更新
	for i := len(m.mCacher) - 1; i >= 0; i-- {
		newEntries := mRefreshEntry(ctx, entries, i, m.ttlStep)
		_ = m.mCacher[i].MSet(ctx, newEntries...)
	}

	return nil
}

func (m *MultiLevelCache) MDel(ctx context.Context, keys ...string) error {
	// 倒序删除
	for i := len(m.mCacher) - 1; i >= 0; i-- {
		err := m.mCacher[i].MDel(ctx, keys...)
		if err != nil {
			return err
		}
	}

	return nil
}
