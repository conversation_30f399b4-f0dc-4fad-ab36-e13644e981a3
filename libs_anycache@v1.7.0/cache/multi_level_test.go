package cache

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_refreshEntry(t *testing.T) {
	ctx := WithRuntimeTTL(context.TODO(), 10*time.Second, 10*time.Second, nil)

	e1 := NewEntry("test", []byte("test"), 10*time.Second, 10*time.Second)
	e2 := refreshEntry(ctx, e1, 1, 1.3)
	assert.Equal(t, 13*time.Second, e2.GetTtl())

	e3 := refreshEntry(ctx, e1, 2, 2)
	assert.Equal(t, 40*time.Second, e3.GetTtl())

	step := float64(3.0)
	ctx = WithRuntimeTTL(context.TODO(), 10*time.Second, 10*time.Second, &step)
	e4 := refreshEntry(ctx, e1, 1, 1.3)
	assert.Equal(t, 30*time.Second, e4.GetTtl())

	e5 := refreshEntry(ctx, e1, 2, 2)
	assert.Equal(t, 90*time.Second, e5.GetTtl())
}

func Test_mRefreshEntry(t *testing.T) {
	ctx := WithRuntimeTTL(context.TODO(), 10*time.Second, 10*time.Second, nil)

	e1 := NewEntry("test1", []byte("test1"), 10*time.Second, 0)
	e2 := NewEntry("test2", []byte("test2"), 10*time.Second, 0)
	eList := []IEntry{e1, e2}

	el1 := mRefreshEntry(ctx, eList, 1, 1.3)
	assert.Equal(t, 13*time.Second, el1[0].GetTtl())
	assert.Equal(t, 13*time.Second, el1[1].GetTtl())

	el2 := mRefreshEntry(ctx, eList, 2, 2)
	assert.Equal(t, 40*time.Second, el2[0].GetTtl())
	assert.Equal(t, 40*time.Second, el2[1].GetTtl())
}

func Test_multiLevelCache(t *testing.T) {
	cache1 := MustNewLocalBytesCacheLFU(10)
	cache2 := MustNewLocalBytesCacheLFU(10)
	cli := NewMultiLevelCacheWithTTLStep(2, cache1, cache2)

	ctx := WithRuntimeTTL(context.TODO(), 1*time.Second, 2*time.Second, nil)

	// cache1 只有 e1
	e1 := NewEntry("test_1", []byte("hello world1"), 1*time.Second, 2*time.Second)
	assert.NoError(t, cache1.Set(ctx, e1))

	// cache2 有 e2 e3，过期时间是 2 倍
	e2 := NewEntry("test_1", []byte("hello world1"), 2*time.Second, 4*time.Second)
	e3 := NewEntry("test_2", []byte("hello world2"), 2*time.Second, 4*time.Second)
	assert.NoError(t, cache2.Set(ctx, e2))
	assert.NoError(t, cache2.Set(ctx, e3))

	// 访问一次后
	es, err := cli.MGet(ctx, "test_1", "test_2")
	assert.NoError(t, err)
	assert.NotNil(t, es[0])
	assert.NotNil(t, es[1])

	// cache2 被填充了，并且过期时间是 1s
	es, err = cache1.MGet(ctx, "test_1", "test_2")
	assert.NoError(t, err)
	assert.NotNil(t, es[0])
	assert.NotNil(t, es[1])
	assert.Equal(t, 1*time.Second, es[1].GetTtl())

	// 2s 后，cache1 中的数据正确删除
	time.Sleep(2 * time.Second)
	es, err = cache1.MGet(ctx, "test_1", "test_2")
	assert.NoError(t, err)
	assert.Nil(t, es[0])
	assert.Nil(t, es[1])
}

func Test_multiLevelCacheNeedExpiredCache(t *testing.T) {
	cache1 := MustNewLocalBytesCacheLFU(10)
	cache2 := MustNewLocalBytesCacheLFU(10)
	cache3 := newBad(BadGet | BadMGet)
	cli := NewMultiLevelCacheWithTTLStep(2, cache1, cache2, cache3)

	ctx := WithRuntimeTTL(context.TODO(), 1*time.Second, 2*time.Second, nil)
	ctx = WithNeedExpiredData(ctx)

	init := func() {
		// cache1
		e1 := NewEntry("test_1", []byte("hello world1"), 1*time.Second, 2*time.Second)
		assert.NoError(t, cache1.MDel(ctx, "test_1"))
		assert.NoError(t, cache1.Set(ctx, e1))

		// cache2
		e2 := NewEntry("test_1", []byte("hello world1"), 2*time.Second, 4*time.Second)
		assert.NoError(t, cache2.MDel(ctx, "test_1"))
		assert.NoError(t, cache2.Set(ctx, e2))
	}

	t.Run("Get", func(t *testing.T) {
		init()
		// 1s 后，cache1 有数据但是已经过期了，cache2 有数据没有过期，期望返回 cache2 的数据并且更新 cache1
		time.Sleep(time.Second)

		e, err := cache1.Get(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, e.IsDecayed())

		e, err = cli.Get(ctx, "test_1")
		assert.NoError(t, err)
		assert.False(t, e.IsDecayed())

		e, err = cache1.Get(ctx, "test_1")
		assert.NoError(t, err)
		assert.False(t, e.IsDecayed())

		// 再过 1s 后，cache1、cache2 都过期了，要去 cache3 找，但是 cache3 报错了，所以只能用 cache2 的过期数据
		time.Sleep(time.Second)
		e, err = cache1.Get(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, e.IsDecayed())

		e, err = cache2.Get(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, e.IsDecayed())

		e, err = cli.Get(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, e.IsDecayed())
	})

	t.Run("MGet", func(t *testing.T) {
		init()
		// 同样的操作，MGet 也来一次
		time.Sleep(time.Second)

		es, err := cache1.MGet(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, es[0].IsDecayed())

		es, err = cli.MGet(ctx, "test_1")
		assert.NoError(t, err)
		assert.False(t, es[0].IsDecayed())

		es, err = cache1.MGet(ctx, "test_1")
		assert.NoError(t, err)
		assert.False(t, es[0].IsDecayed())

		// 再过 1s 后，cache1、cache2 都过期了，要去 cache3 找，但是 cache3 报错了，所以只能用 cache2 的过期数据
		time.Sleep(time.Second)
		es, err = cache1.MGet(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, es[0].IsDecayed())

		es, err = cache2.MGet(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, es[0].IsDecayed())

		es, err = cli.MGet(ctx, "test_1")
		assert.NoError(t, err)
		assert.True(t, es[0].IsDecayed())
	})
}
