package cache

import (
	"context"
	"time"

	"code.byted.org/gopkg/retry"
	"code.byted.org/kv/goredis"
	"code.byted.org/kv/redis-v6/pkg"
)

const (
	redisWithRetryOptionKey = "cache:redis_with_retry:option"
)

type redisWithRetryOption struct {
	times   int
	timeout time.Duration
}

func WithRetryOptions(ctx context.Context, times int, timeout time.Duration) context.Context {
	ctx = context.WithValue(ctx, redisWithRetryOptionKey, redisWithRetryOption{
		times:   times,
		timeout: timeout,
	})
	return ctx
}

func getRetryOptions(ctx context.Context) (int, time.Duration) {
	if v := ctx.Value(redisWithRetryOptionKey); v != nil {
		opt := v.(redisWithRetryOption)
		return opt.times, opt.timeout
	}

	return 1, time.Second
}

type RedisWithRetry struct {
	Cacher
}

func (r *RedisWithRetry) Name() string {
	return "retry_redis_cache"
}

func (r *RedisWithRetry) Get(ctx context.Context, key string) (e IEntry, err error) {
	times, timeout := getRetryOptions(ctx)
	err = retry.DoCanAbort("anycache-redis-with-retry-get", times, timeout, func() (bool, error) {
		var err1 error
		e, err1 = r.Cacher.Get(ctx, key)
		if err1 != nil {
			if err1 == ErrNotFound || !pkg.IsRetryableError(err1) {
				return true, err1
			}

			return false, err1
		}

		return false, nil
	})

	return e, err
}

func (r *RedisWithRetry) Set(ctx context.Context, entry IEntry) error {
	times, timeout := getRetryOptions(ctx)
	err := retry.DoCanAbort("anycache-redis-with-retry-set", times, timeout, func() (bool, error) {
		err := r.Cacher.Set(ctx, entry)
		if err != nil {
			if !pkg.IsRetryableError(err) {
				return true, err
			}

			return false, err
		}

		return false, nil
	})

	return err
}

func (r *RedisWithRetry) MGet(ctx context.Context, keys ...string) (entries []IEntry, err error) {
	times, timeout := getRetryOptions(ctx)
	err = retry.DoCanAbort("anycache-redis-with-retry-mget", times, timeout, func() (bool, error) {
		var err1 error
		entries, err1 = r.Cacher.MGet(ctx, keys...)
		if err1 != nil {
			if !pkg.IsRetryableError(err1) {
				return true, err1
			}

			return false, err1
		}

		return false, nil
	})

	return entries, err
}

func (r *RedisWithRetry) MSet(ctx context.Context, entries ...IEntry) error {
	times, timeout := getRetryOptions(ctx)
	err := retry.DoCanAbort("anycache-redis-with-retry-mset", times, timeout, func() (bool, error) {
		err := r.Cacher.MSet(ctx, entries...)
		if err != nil {
			if !pkg.IsRetryableError(err) {
				return true, err
			}

			return false, err
		}

		return false, nil
	})

	return err
}

func (r *RedisWithRetry) MDel(ctx context.Context, keys ...string) error {
	times, timeout := getRetryOptions(ctx)
	err := retry.DoCanAbort("anycache-redis-with-retry-mdel", times, timeout, func() (bool, error) {
		err := r.Cacher.MDel(ctx, keys...)
		if err != nil {
			if !pkg.IsRetryableError(err) {
				return true, err
			}

			return false, err
		}

		return false, nil
	})

	return err
}

// NewRedisWrap 底层使用 Redis 作为存储引擎
func NewRedisWithRetry(cli *goredis.Client) Cacher {
	return &RedisWithRetry{
		Cacher: NewRedisWrap(cli),
	}
}
