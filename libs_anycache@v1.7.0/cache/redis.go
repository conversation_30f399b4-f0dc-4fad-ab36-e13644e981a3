package cache

import (
	"context"
	"strings"

	"code.byted.org/kv/goredis"
	"code.byted.org/kv/redis-v6"
)

type RedisWrap struct {
	cli *goredis.Client
}

// NewRedisWrap 底层使用 Redis 作为存储引擎
func NewRedisWrap(cli *goredis.Client) Cacher {
	return &RedisWrap{
		cli: cli,
	}
}

func isRedisNil(err error) bool {
	return strings.Contains(err.Error(), "redis: nil")
}

func (r *RedisWrap) Name() string {
	return "redis_cache"
}

func (r *RedisWrap) Get(ctx context.Context, key string) (IEntry, error) {
	data, err := r.cli.WithContext(ctx).Get(key).Bytes()
	if err != nil {
		if isRedisNil(err) {
			return nil, ErrNotFound
		}
		return nil, err
	}
	if data == nil {
		return nil, ErrNotFound
	}

	entry, err := deserializeEntryNew(ctx, key, data)
	if err != nil {
		return nil, err
	}

	// 当前数据已过期并且上层不需要过期数据
	if entry.IsDecayed() && !needExpiredData(ctx) {
		return nil, ErrNotFound
	}

	return entry, nil
}

func (r *RedisWrap) Set(ctx context.Context, entry IEntry) error {
	if entry == nil {
		return nil
	}
	return r.cli.WithContext(ctx).Set(entry.GetKey(), entry.Serialize(), entry.GetDelTTL()).Err()
}

func (r *RedisWrap) MGet(ctx context.Context, keys ...string) ([]IEntry, error) {
	pipeline := r.cli.WithContext(ctx).Pipeline()
	defer func() {
		_ = pipeline.Close()
	}()

	resList := make([]*redis.StringCmd, len(keys))
	for i := range keys {
		resList[i] = pipeline.Get(keys[i])
	}

	_, err := pipeline.Exec()
	if err != nil {
		return nil, err
	}

	entries := make([]IEntry, len(keys))
	for i := range resList {
		if data, err := resList[i].Bytes(); err == nil {
			if e, err := deserializeEntryNew(ctx, keys[i], data); err == nil {
				if !e.IsDecayed() || needExpiredData(ctx) {
					entries[i] = e
				}
			}
		}
	}

	return entries, nil
}

func (r *RedisWrap) MSet(ctx context.Context, entries ...IEntry) error {
	pipeline := r.cli.WithContext(ctx).Pipeline()
	defer func() {
		_ = pipeline.Close()
	}()

	resList := make([]*redis.StatusCmd, len(entries))
	for i, e := range entries {
		if e == nil {
			continue
		}
		resList[i] = pipeline.Set(e.GetKey(), e.Serialize(), e.GetDelTTL())
	}
	if pipeline.CmdNum() == 0 {
		return nil
	}
	_, err := pipeline.Exec()
	if err != nil {
		return err
	}

	for _, res := range resList {
		if res == nil {
			continue
		}

		if err := res.Err(); err != nil {
			return err
		}
	}

	return nil
}

func (r *RedisWrap) MDel(ctx context.Context, keys ...string) error {
	pipeline := r.cli.WithContext(ctx).Pipeline()
	defer func() {
		_ = pipeline.Close()
	}()

	resList := make([]*redis.IntCmd, len(keys))
	for i, key := range keys {
		resList[i] = pipeline.Del(key)
	}

	_, err := pipeline.Exec()
	if err != nil {
		return err
	}

	for _, res := range resList {
		if err := res.Err(); err != nil {
			return err
		}
	}

	return nil
}
