package cache

import (
	"code.byted.org/webcast/libs_anycache/plugin/cache/base"
)

var (
	ErrNotFound = base.ErrNotFound

	ErrDel = base.ErrDel

	ErrInternalErr = base.ErrInternalErr
)

// Cacher 缓存组件
type Cacher = base.Cacher

var (
	WithNeedExpiredData = base.WithNeedExpiredData
	WithoutHeader       = base.WithoutHeader
	WithRuntimeTTL      = base.WithRuntimeTTL

	needExpiredData = base.NeedExpiredData
	runtimeTTL      = base.RuntimeTTL
	WithCustomTTL   = base.WithCustomTTL
	HasCustomTTL    = base.HasCustomTTL
)
