name: CIPipeline
trigger: [change]
jobs:
  process_ci:
    name: process ci
    image: hub.byted.org/codebase/ci_go_1_18:latest
    runs-on:
      env: boe
    steps:
      - id: prepare_env
        commands:
          - mkdir .codebase_temp
      - id: test # Run Test
        commands:
          - go test -parallel 4 -cpu=4 -coverprofile=.codebase_temp/coverage.out -coverpkg=./... $(go list ./... | grep -vE '/testcase|/examples')
      - id: codecov
        uses: actions/codecov
        inputs:
          file: .codebase_temp/coverage.out
          fail_ci_if_error: true
