package examples

import (
	"context"
	"fmt"
	"sort"
	"sync/atomic"
	"testing"

	anycache "code.byted.org/webcast/libs_anycache"
)

var (
	// 回源次数
	multiFuncMapResultCallCount int64
	// 回源的总个数，和上面的区别是一次回源可能批量请求了多个数据
	multiFuncMapResultItemsCount int64
)

// 回源函数
func MultiFuncMapResult(ids []int) map[string]string {
	atomic.AddInt64(&multiFuncMapResultCallCount, 1)
	atomic.AddInt64(&multiFuncMapResultItemsCount, int64(len(ids)))

	ss := make(map[string]string)
	for i := range ids {
		key := fmt.Sprintf("id:%d", ids[i])
		ss[key] = key + ":value"
	}

	return ss
}

func TestBatchFetcherByBatchLoader(t *testing.T) {
	// 初始化 anycache
	cache := anycache.NewDefault().
		WithNameSpace("example-BatchFetcherByBatchLoader").
		WithoutLog().
		BuildBatchFetcherByBatchLoader(
			// 第一个参数： 从 input item 生成缓存 key
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("id:%d", item)
			},
			// 第二个参数，回源得到 missed item 对应的 key 和 value, 需要以 map 的形式返回，map 的 key 需要和 第一个参数中生成的 key 保持一致
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				ids := missedItems.([]int)

				ss := MultiFuncMapResult(ids)
				return ss, nil
			},
		)

	var r1, r2, r3 []string

	// 第一次调用，会直接回源
	from1, _ := cache.MGet(context.Background(), []int{1, 2, 3}, &r1)
	sort.Strings(r1)
	fmt.Println(r1)
	fmt.Println(multiFuncMapResultCallCount)
	fmt.Println(multiFuncMapResultItemsCount)
	fmt.Println(from1)

	// 第二次调用，会直接读缓存，不回源
	from2, _ := cache.MGet(context.Background(), []int{1, 2, 3}, &r2)
	sort.Strings(r2)
	fmt.Println(r2)
	fmt.Println(multiFuncMapResultCallCount)
	fmt.Println(multiFuncMapResultItemsCount)
	fmt.Println(from2)

	// 第二次调用，获取 1，2，3，4 的数据，由于 1，2，3 已经在缓存里了，4 不在，所以只有 4 回源
	from3, _ := cache.MGet(context.Background(), []int{1, 2, 3, 4}, &r3)
	sort.Strings(r3)
	fmt.Println(r3)
	fmt.Println(multiFuncMapResultCallCount)
	fmt.Println(multiFuncMapResultItemsCount)
	fmt.Println(from3)

	// Output:
	// [id:1 id:2 id:3]
	// 1
	// 3
	// FromLoader
	// [id:1 id:2 id:3]
	// 1
	// 3
	// FromCache
	// [id:1 id:2 id:3 id:4]
	// 2
	// 4
	// FromLoaderPartially
}
