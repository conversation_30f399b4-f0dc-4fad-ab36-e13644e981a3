package examples

import (
	"context"
	"fmt"
	"sync/atomic"
	"testing"

	anycache "code.byted.org/webcast/libs_anycache"
)

// 调用次数，回源时+1，命中缓存时不+1
var simpleFuncCallCount int64

// 回源函数
func SimpleFunc(name string) string {
	atomic.AddInt64(&simpleFuncCallCount, 1)
	return "hello " + name
}

func TestFetcherByLoader(t *testing.T) {
	// 初始化 anycache
	cache := anycache.NewDefault().
		WithNameSpace("example-FetcherByLoader").
		WithoutLog().
		BuildFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return item.(string)
			},
			func(ctx context.Context, inItem interface{}, extraParam interface{}) (interface{}, error) {
				return SimpleFunc(inItem.(string)), nil
			},
		)

	var r1, r2 string

	// 第一次调用，会直接回源
	from1, _ := cache.Get(context.Background(), "anycache", &r1)
	fmt.Println(r1)
	fmt.Println(simpleFuncCallCount)
	fmt.Println(from1)

	// 第二次调用，会命中缓存
	from2, _ := cache.Get(context.Background(), "anycache", &r2)
	fmt.Println(r2)
	fmt.Println(simpleFuncCallCount)
	fmt.Println(from2)

	// Output:
	// hello anycache
	// 1
	// FromLoader
	// hello anycache
	// 1
	// FromCache
}
