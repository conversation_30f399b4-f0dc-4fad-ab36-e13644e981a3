package examples

import (
	"context"
	"fmt"
	"sync/atomic"
	"testing"

	anycache "code.byted.org/webcast/libs_anycache"
)

// 调用次数，回源时+1，命中缓存时不+1
var simpleFuncBooksCallCount int64

// Books 中的 Title 是可导出字段，bookId 是不可导出字段。由于缓存时会把结构体序列化成[]byte，所以不可导出的字段无法缓存
type Books struct {
	Title  string
	bookId int
}

func SimpleBookFunc(name string) Books {
	atomic.AddInt64(&simpleFuncBooksCallCount, 1)
	var books Books
	books.Title = name
	books.bookId = 10086

	return books
}

func TestExportedFields(t *testing.T) {
	// 初始化 anycache
	cache := anycache.NewDefault().
		WithNameSpace("example-ExportedFields").
		WithoutLog().
		BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return item.(string)
			}, func(ctx context.Context, inItem interface{}, extraParam interface{}) (interface{}, error) {
				return SimpleBookFunc(inItem.(string)), nil
			},
		)

	var books1, books2 []Books

	// 第一次调用，会直接回源
	cache.MGet(context.Background(), []string{"anycache"}, &books1)
	fmt.Println(books1[0])
	fmt.Println(simpleFuncBooksCallCount)

	// 第二次调用，会命中缓存
	cache.MGet(context.Background(), []string{"anycache"}, &books2)
	fmt.Println(books2[0])
	fmt.Println(simpleFuncBooksCallCount)

	books3 := SimpleBookFunc("helloworld")
	fmt.Println(books3)

	// Output: {anycache 0}
	// 1
	// {anycache 0}
	// 1
	// {helloworld 10086}
}
