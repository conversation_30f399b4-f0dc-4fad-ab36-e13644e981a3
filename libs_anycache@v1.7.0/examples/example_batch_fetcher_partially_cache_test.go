package examples

import (
	anycache "code.byted.org/webcast/libs_anycache"
	"code.byted.org/webcast/libs_anycache/cache"
	"code.byted.org/webcast/libs_anycache/plugin/codec/sonic_codec"
	"context"
	"errors"
	"fmt"
	"sort"
	"sync/atomic"
	"testing"
)

var (
	// 回源次数
	multiPCFuncMapResultCallCount int64
	// 回源的总个数，和上面的区别是一次回源可能批量请求了多个数据
	multiPCFuncMapResultItemsCount int64
)

// 回源函数
func MultiPCFuncMapResult(ids []int) map[string]string {
	atomic.AddInt64((&multiPCFuncMapResultCallCount), 1)
	atomic.AddInt64(&multiPCFuncMapResultItemsCount, int64(len(ids)))

	ss := make(map[string]string, len(ids))
	for i := range ids {
		if i >= 2 {
			// 固定只会回源前两个
			break
		}
		key := fmt.Sprintf("%d", ids[i])
		ss[key] = key + ":value"
	}
	return ss
}

func TestBatchFetcherPartiallyCache(t *testing.T) {
	cache := anycache.New(cache.MustNewLocalBytesCacheLRU(256), sonic_codec.NewSonic()).
		WithNameSpace("example-batchFetcherPartiallyCache").
		BuildBatchFetcherByBatchLoader(
			// 第一个参数： 从 input item 生成缓存 key
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				return fmt.Sprintf("%d", item)
			},
			// 第二个参数，回源得到 missed item 对应的 key 和 value, 需要以 map 的形式返回，map 的 key 需要和 第一个参数中生成的 key 保持一致
			func(ctx context.Context, missedItems interface{}, extraParam interface{}) (interface{}, error) {
				ids := missedItems.([]int)
				ss := MultiPCFuncMapResult(ids)
				// 模拟回源一直失败
				return ss, errors.New("some error")
			},
		)

	var r1, r2 []string

	// 不设置部分成功即缓存标识
	// 第一次调用，会直接回源，返回失败，但是会缓存回源成功的部分
	from1, _ := cache.MGet(context.Background(), []int{1, 2, 3}, &r1)
	sort.Strings(r1)
	fmt.Println(r1)
	fmt.Println(multiPCFuncMapResultCallCount)
	fmt.Println(multiPCFuncMapResultItemsCount)
	fmt.Println(from1)
	// [  ]
	// 1
	// 3
	// FromLoader

	// 第二次调用，会正常读取到缓存的数据1、2
	from2, _ := cache.MGet(context.Background(), []int{1, 2}, &r2)
	sort.Strings(r2)
	fmt.Println(r2)
	fmt.Println(multiPCFuncMapResultCallCount)
	fmt.Println(multiPCFuncMapResultItemsCount)
	fmt.Println(from2)
	// [ ]
	// 2
	// 5
	// FromLoader

	// ====================
	multiPCFuncMapResultCallCount = 0
	multiPCFuncMapResultItemsCount = 0
	// 第一次调用，会直接回源，返回失败，但是会缓存回源成功的部分
	from3, err := cache.MGet(context.Background(), []int{1, 2, 3}, &r1, anycache.WithPartiallyCache(true))
	sort.Strings(r1)
	fmt.Println(err)
	fmt.Println(r1)
	fmt.Println(multiPCFuncMapResultCallCount)
	fmt.Println(multiPCFuncMapResultItemsCount)
	fmt.Println(from3)
	// some error
	// [ 1:value 2:value]
	// 1
	// 3
	// FromLoader

	// 第二次调用，会正常读取到缓存的数据1、2
	from4, err := cache.MGet(context.Background(), []int{1, 2}, &r2, anycache.WithPartiallyCache(true))
	sort.Strings(r2)
	fmt.Println(err)
	fmt.Println(r2)
	fmt.Println(multiPCFuncMapResultCallCount)
	fmt.Println(multiPCFuncMapResultItemsCount)
	fmt.Println(from4)
	// <nil>
	// [1:value 2:value]
	// 1
	// 3
	// FromCache
}
