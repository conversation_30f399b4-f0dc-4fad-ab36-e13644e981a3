package examples

import (
	"context"
	"fmt"
	"sync/atomic"
	"testing"

	anycache "code.byted.org/webcast/libs_anycache"
)

// 回源次数
var singleFuncCallCount int64

func SingleFunc(prefix, name string) string {
	atomic.AddInt64(&singleFuncCallCount, 1)
	return prefix + " " + name
}

func TestBatchFetcherByLoader(t *testing.T) {
	// 初始化 anycache
	cache := anycache.NewDefault().
		WithNameSpace("example-BatchFetcherByLoader").
		WithoutLog().
		BuildBatchFetcherByLoader(
			func(ctx context.Context, item interface{}, extraParam interface{}) string {
				name, prefix := item.(string), extraParam.(string)
				return prefix + "_" + name
			},
			func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
				name, prefix := missedItem.(string), extraParam.(string)

				return SingleFunc(prefix, name), nil
			},
		)

	var resList1, resList2 []string
	// 第一次调用，会直接回源
	from1, _ := cache.MGet(context.Background(), []string{"a", "b", "c"}, &resList1, anycache.WithExtraParam("hello"))
	fmt.Println(resList1)
	fmt.Println(singleFuncCallCount)
	fmt.Println(from1)

	// 第二次调用，由于a，b，c已经在缓存中了，d不在，所以d会回源
	from2, _ := cache.MGet(context.Background(), []string{"a", "b", "c", "d"}, &resList2, anycache.WithExtraParam("hello"))
	fmt.Println(resList2)
	fmt.Println(singleFuncCallCount)
	fmt.Println(from2)

	// Output:
	// [hello a hello b hello c]
	// 3
	// FromLoader
	// [hello a hello b hello c hello d]
	// 4
	// FromLoaderPartially
}
