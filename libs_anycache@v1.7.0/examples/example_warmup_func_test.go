package examples

// 1.0 版本对此功能暂时不做支持

//import (
//	"context"
//	"fmt"
//	"time"
//
//	anycache "code.byted.org/webcast/libs_anycache"
//)
//
//func Example_warmUpFunc() {
//	cache, err := anycache.NewDefault().WithoutLog().buildForWarmUpFuncRetSlice(
//		func(ctx context.Context, item interface{}, extraParam interface{}) string {
//			return fmt.Sprintf("id:%d", item)
//		},
//		func(ctx context.Context, item interface{}, extraParam interface{}) string {
//			return item.(string)
//		},
//		func(ctx context.Context) anycache.pageWarmUpLoad {
//			// 加载全部数据
//			ids := []int{1, 2, 3, 4, 5, 6}
//			return func(ctx context.Context) (interface{}, bool, error) {
//				var tmp []int
//				for {
//					if len(ids) > 2 {
//						tmp, ids = ids[:2], ids[2:]
//						return MultiFunc(tmp), true, nil
//					} else {
//						return MultiFunc(ids), false, nil
//					}
//				}
//			}
//		},
//		// 预热任务名，取一个本地唯一的名字即可；1 秒刷新一次全量数据
//		"example_warm_up", time.Second)
//
//	if err != nil {
//		panic("init anycache failed: %s" + err.Error())
//	}
//
//	var res []string
//	from1, _ := cache.MGet(context.Background(), []int{1, 2, 3}, &res)
//	fmt.Println(from1, res)
//
//	// Output:
//	// FromCache [id:1 id:2 id:3]
//}
